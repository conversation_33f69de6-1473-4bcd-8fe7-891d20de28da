# Debezium PostgreSQL CDC Setup

This guide provides step-by-step instructions to set up Debezium for Change Data Capture (CDC) from PostgreSQL to Kafka.

## Prerequisites

- Kubernetes cluster (tested with EKS)
- kubectl configured with cluster access
- AWS CLI configured with appropriate credentials
- Docker (for building images)

## Setup Instructions

### 1. Deploy PostgreSQL with Logical Decoding

```bash
# Apply PostgreSQL deployment
kubectl apply -f kubernetes/postgres-cdc-deployment.yaml

# Verify PostgreSQL is running
kubectl get pods -n confluent -l app=postgres
```

### 2. Create Inventory Database and Sample Data

```bash
# Copy the setup script to the PostgreSQL pod
kubectl cp kubernetes/setup-inventory-db.sql confluent/$(kubectl get pods -n confluent -l app=postgres -o name | head -n 1):/tmp/setup-inventory-db.sql

# Execute the setup script
kubectl exec -n confluent -it $(kubectl get pods -n confluent -l app=postgres -o name | head -n 1) -- psql -U postgres -f /tmp/setup-inventory-db.sql
```

### 3. Build and Deploy Debezium Connect

```bash
# Make the build script executable
chmod +x scripts/build-push-debezium-connect.sh

# Build and push the Debezium Connect image (requires AWS credentials)
./scripts/build-push-debezium-connect.sh

# Deploy Debezium Connect
kubectl apply -f kubernetes/debezium-connect.yaml

# Verify Debezium Connect is running
kubectl get pods -n confluent -l app=debezium-connect
```

### 4. Configure Debezium PostgreSQL Connector

```bash
# Apply the inventory connector configuration
kubectl apply -f kubernetes/debezium-inventory-connector.yaml

# Create the connector using the REST API
kubectl exec -n confluent -it $(kubectl get pods -n confluent -l app=debezium-connect -o name | head -n 1) -- \
  curl -X POST -H "Content-Type: application/json" --data @/etc/kafka/connectors/inventory-connector.json http://localhost:8083/connectors

# Verify connector status
kubectl exec -n confluent -it $(kubectl get pods -n confluent -l app=debezium-connect -o name | head -n 1) -- \
  curl -s http://localhost:8083/connectors/inventory-connector/status | jq
```

### 5. Verify Data Capture

```bash
# List Kafka topics (should see inventory.public.* topics)
kubectl exec -n confluent kafka-0 -- kafka-topics --bootstrap-server localhost:9092 --list | grep inventory

# View captured changes in real-time
kubectl exec -n confluent kafka-0 -- kafka-console-consumer \
  --bootstrap-server localhost:9092 \
  --topic inventory.public.customers \
  --from-beginning

# Make changes in PostgreSQL and see them appear in Kafka
kubectl exec -n confluent -it $(kubectl get pods -n confluent -l app=postgres -o name | head -n 1) -- \
  psql -U postgres -d inventory -c "INSERT INTO customers (first_name, last_name, email) VALUES ('Test', 'User', '<EMAIL>');"
```

## Testing the Flink Job

### Unit Tests

Run the unit tests for the Flink job with:
```bash
cd flink-jobs
mvn test
```

### Integration Tests

Integration tests use TestContainers and live Kafka/PostgreSQL containers. They are located in `src/test-integration/java` and executed via the Maven Failsafe plugin during the `verify` phase:
```bash
cd flink-jobs
mvn verify
# or explicitly:
# mvn failsafe:integration-test failsafe:verify
```

## Troubleshooting

### Check Debezium Connect Logs
```bash
kubectl logs -n confluent -l app=debezium-connect -f
```

### Check Connector Status
```bash
kubectl exec -n confluent -it $(kubectl get pods -n confluent -l app=debezium-connect -o name | head -n 1) -- \
  curl -s http://localhost:8083/connectors/inventory-connector/status | jq
```

### Check PostgreSQL Replication Slots
```bash
kubectl exec -n confluent -it $(kubectl get pods -n confluent -l app=postgres -o name | head -n 1) -- \
  psql -U postgres -c "SELECT * FROM pg_replication_slots;"
```

## Cleanup

To remove all resources:

```bash
# Delete the connector
kubectl exec -n confluent -it $(kubectl get pods -n confluent -l app=debezium-connect -o name | head -n 1) -- \
  curl -X DELETE http://localhost:8083/connectors/inventory-connector

# Delete Debezium Connect
kubectl delete -f kubernetes/debezium-connect.yaml

# Delete PostgreSQL
kubectl delete -f kubernetes/postgres-cdc-deployment.yaml
```

---

## Setting up new Codefresh Lambda repository
1. Rename your `src/handlers/codefresh-lambda-template` Lambda func directory.
2. Set up your Lambda function repository variables marked with `TODO`.
3. Create Codefresh project for your repository.
Use [stork](https://g.codefresh.io/projects/stork/edit/pipelines/?projectId=62044333c00da29446570856) or [accounts-data-transform](https://g.codefresh.io/projects/accounts-data-transform/edit/pipelines/?projectId=62042a10da9120380ad912ef) as a reference.   
Remember to set the Codefresh env variables:   
   `TFC_TOKEN`, `ENGDEV_S3A_BA_RW_AWS_ACCESS_KEY_ID` and `ENGDEV_S3A_BA_RW_AWS_SECRET_ACCESS_KEY`.
4. Create a PR for `gdrx-infrastructure` with separate workspace for Terraform plan `auto-apply`.  
You can use [strk app plan](https://github.com/GoodRx/gdrx-infrastructure/blob/main/apps/strk/app/eng-sbx/main.tf) as a reference. Your new workspace should be added [here](https://github.com/GoodRx/gdrx-infrastructure/blob/main/workspaces/workspaces.tf).  

---
#### **_README.md  example:_**
# codefresh-lambda-template
Lambda function repository with deploy/releasing handled by Codefresh

## Table of Contents

---

## Repository Structure & Quick Reference

This repository contains all the components needed to build, deploy, and operate a modern Kafka CDC and stream processing platform. Here’s how it’s organized:

### Directory Overview

| Directory                  | What it’s for                                    |
|----------------------------|--------------------------------------------------|
| `docker/`                  | Custom Docker images (Kafka Connect, etc.)       |
| `scripts/`                 | Build/push automation scripts                    |
| `kubernetes/`              | Raw K8s manifests for all components             |
| `helm/kafka-cdc/`          | Helm chart for full stack deployment             |
| `docs/`                    | Architecture, deployment, and troubleshooting    |

### How the Pieces Fit Together

- **Build image:**  
  `docker/kafka-connect/Dockerfile` + `scripts/build-images.sh`
- **Push image:**  
  `scripts/build-images.sh` (uses env from `scripts/env.sh`)
- **Deploy to K8s:**  
  - Raw YAML: `kubernetes/debezium-connect.yaml`
  - Helm: `helm/kafka-cdc/`
- **Configure pipeline:**  
  - Debezium connector: `kubernetes/deploy-debezium-connector.yaml` (Job) or REST API
- **Docs:**  
  - Everything explained in `/docs` and in the Helm chart README

### Typical Workflow

1. **Build & push Docker image:**
   - `./scripts/build-images.sh`
2. **Deploy Kafka Connect:**
   - `kubectl apply -f kubernetes/debezium-connect.yaml` or use Helm
3. **Deploy Debezium connector:**
   - `kubectl apply -f kubernetes/deploy-debezium-connector.yaml` or use REST API
4. **Verify & monitor:**
   - Check pod status, connector status, and Kafka topics

---
#### **_README.md  example:_**
# codefresh-lambda-template
Lambda function repository with deploy/releasing handled by Codefresh

| Pipeline  | Status                                                                                                                                                                                                                                                                                                                                                                                                                                |
|:---------:|:--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|   Main    | [![Codefresh build status]( https://g.codefresh.io/api/badges/pipeline/goodrx/accounts-data-initial-load%2Fbuild-main?type=cf-1&key=eyJhbGciOiJIUzI1NiJ9.NWVmZDE1OWYxYjkzNjhkYmU5MmE4OTk2.ZXkg07_ClQbaOJac0P5sB6v6qppMowq-Ryxxv1iBFOY)]( https://g.codefresh.io/pipelines/edit/new/builds?id=621555714757d824dec5d1d4&pipeline=build-main&projects=accounts-data-initial-load&projectId=6215552a69237e7b12593611)                     |
|   Build   | [![Codefresh build status]( https://g.codefresh.io/api/badges/pipeline/goodrx/accounts-data-initial-load%2Ftask-build-artifact?type=cf-1&key=eyJhbGciOiJIUzI1NiJ9.NWVmZDE1OWYxYjkzNjhkYmU5MmE4OTk2.ZXkg07_ClQbaOJac0P5sB6v6qppMowq-Ryxxv1iBFOY)]( https://g.codefresh.io/pipelines/edit/new/builds?id=621555d669237ee97c593621&pipeline=task-build-artifact&projects=accounts-data-initial-load&projectId=6215552a69237e7b12593611)   |
|  Release  | [![Codefresh build status]( https://g.codefresh.io/api/badges/pipeline/goodrx/accounts-data-initial-load%2Frelease?type=cf-1&key=eyJhbGciOiJIUzI1NiJ9.NWVmZDE1OWYxYjkzNjhkYmU5MmE4OTk2.ZXkg07_ClQbaOJac0P5sB6v6qppMowq-Ryxxv1iBFOY)]( https://g.codefresh.io/pipelines/edit/new/builds?id=621555ac69237e7af2593620&pipeline=release&projects=accounts-data-initial-load&projectId=6215552a69237e7b12593611)                           |
|  Deploy   | [![Codefresh build status]( https://g.codefresh.io/api/badges/pipeline/goodrx/accounts-data-initial-load%2Ftask-deploy-artifact?type=cf-1&key=eyJhbGciOiJIUzI1NiJ9.NWVmZDE1OWYxYjkzNjhkYmU5MmE4OTk2.ZXkg07_ClQbaOJac0P5sB6v6qppMowq-Ryxxv1iBFOY)]( https://g.codefresh.io/pipelines/edit/new/builds?id=621555e169237e7fb4593622&pipeline=task-deploy-artifact&projects=accounts-data-initial-load&projectId=6215552a69237e7b12593611) |


## Uploading and releasing new version
The updating/releasing new version is automated. After merging to `main` branch a codefresh pipeline should fetch dependencies, create a compressed zip file, push it to `s3a-build-artifacts.grxdev.com` bucket and **create new Lambda resources**. No manual deployment is required.
> ✏️ Remember that `requirements.txt` file **can not** be empty. If you don't use any pkg do not edit the `requirements.txt` file.

### Codefresh pipelines explained:
After merging changes to `main`:
1. Lambda artifacts will be build and pushed to `s3a-build-artifacts.grxdev.com` S3 bucket.
2. Resources' source code location variables will be replaced with new artifact location
   1. Terraform plan on `pii-dev` **will be auto-applied**, Lambda resource will be recreated
   2. (optional) If this step is manually approved in Codefresh Terraform plan on `pii-prd` **will be auto-applied**, Lambda resource will be recreated.
