apiVersion: batch/v1
kind: Job
metadata:
  name: deploy-postgres-cdc-connector
  namespace: confluent
spec:
  ttlSecondsAfterFinished: 300
  template:
    spec:
      containers:
      - name: deploy-connector
        image: curlimages/curl:latest
        command:
        - /bin/sh
        - -c
        - |
          # Wait for Kafka Connect to be ready
          echo "Waiting for Kafka Connect to be ready..."
          until curl -s http://connect:8083/ > /dev/null; do
            echo "Kafka Connect is not ready yet, waiting..."
            sleep 10
          done
          
          echo "Kafka Connect is ready, deploying Debezium connector..."
          
          # Deploy the Debezium connector
          curl -X POST -H "Content-Type: application/json" --data '{
            "name": "postgres-source",
            "config": {
              "connector.class": "io.debezium.connector.postgresql.PostgresConnector",
              "tasks.max": "1",
              "database.hostname": "postgres.confluent.svc.cluster.local",
              "database.port": "5432",
              "database.user": "postgres",
              "database.password": "postgres",
              "database.dbname": "postgres",
              "database.server.name": "postgres",
              "table.include.list": "public.customers,public.orders",
              "plugin.name": "pgoutput",
              "slot.name": "debezium",
              "publication.name": "dbz_publication",
              "topic.prefix": "postgres",
              "topic.creation.enable": "true",
              "topic.creation.default.replication.factor": "1",
              "topic.creation.default.partitions": "3",
              "transforms": "unwrap",
              "transforms.unwrap.type": "io.debezium.transforms.ExtractNewRecordState",
              "transforms.unwrap.drop.tombstones": "false",
              "transforms.unwrap.delete.handling.mode": "rewrite",
              "schema.include.list": "public",
              "key.converter": "org.apache.kafka.connect.json.JsonConverter",
              "key.converter.schemas.enable": "false",
              "value.converter": "org.apache.kafka.connect.json.JsonConverter",
              "value.converter.schemas.enable": "false"
            }
          }' http://connect:8083/connectors
          
          # Check connector status
          echo "Checking connector status..."
          sleep 5
          curl -s http://connect:8083/connectors/postgres-source/status
      restartPolicy: OnFailure
