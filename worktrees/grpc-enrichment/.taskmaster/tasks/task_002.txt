# Task ID: 2
# Title: Create Integration Test for Order Enrichment Flink Job
# Status: pending
# Dependencies: 1
# Priority: high
# Description: Develop an end-to-end integration test for the Order Enrichment Flink job. This test should set up input data, mock or in-process gRPC services, run the Flink job using a test harness (e.g., MiniCluster), and verify the enriched output.
# Details:


# Test Strategy:
The test itself is the verification. Ensure proper setup of test data sources, sinks, and mock/in-process gRPC services. Validate output against expected enriched data.
