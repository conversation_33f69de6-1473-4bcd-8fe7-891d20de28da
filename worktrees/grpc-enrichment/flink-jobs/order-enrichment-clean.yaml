---
apiVersion: flink.apache.org/v1beta1
kind: FlinkDeployment
metadata:
  name: order-enrichment
  namespace: flink
spec:
  image: ************.dkr.ecr.us-west-2.amazonaws.com/flink-jobs:latest
  flinkVersion: v1_17
  serviceAccount: flink
  podTemplate:
    spec:
      initContainers:
      - name: config-init
        image: busybox:1.35.0
        command: ["sh", "-c", "mkdir -p /writable-conf && cp -r /opt/flink/conf/* /writable-conf/ && chmod -R 777 /writable-conf/"]
        volumeMounts:
        - name: flink-config-volume
          mountPath: /opt/flink/conf
        - name: writable-conf-volume
          mountPath: /writable-conf
      containers:
      - name: flink-main-container
        env:
        - name: FLINK_CONF_DIR
          value: /writable-conf
        volumeMounts:
        - name: writable-conf-volume
          mountPath: /writable-conf
      volumes:
      - name: flink-config-volume
        emptyDir: {}
      - name: writable-conf-volume
        emptyDir: {}
  jobManager:
    resource:
      memory: "2048m"
      cpu: 1
  taskManager:
    resource:
      memory: "2048m"
      cpu: 1
  flinkConfiguration:
    jobmanager.memory.process.size: 2048m
    taskmanager.memory.process.size: 2048m
    taskmanager.numberOfTaskSlots: "2"
    state.backend: rocksdb
    state.checkpoints.dir: s3://arch-sink-checkpoints/flink/checkpoints
    state.savepoints.dir: s3://arch-sink-savepoints/flink/savepoints
    execution.checkpointing.interval: 10s
    execution.checkpointing.mode: EXACTLY_ONCE
    s3.endpoint: s3.us-west-2.amazonaws.com
    s3.path.style.access: "false"
    s3.region: us-west-2
    # AWS credentials provider chain configuration
    s3.credentials.provider: auto
    # Enable instance profile credentials
    fs.s3a.aws.credentials.provider: com.amazonaws.auth.InstanceProfileCredentialsProvider
  job:
    jarURI: local:///opt/flink/usrlib/flink-jobs.jar
    parallelism: 2
    upgradeMode: stateless
    state: running
