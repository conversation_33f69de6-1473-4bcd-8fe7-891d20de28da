apiVersion: v1
kind: ConfigMap
metadata:
  name: flink-config-order-enrichment
  namespace: flink
  labels:
    app: order-enrichment
    type: flink-native-kubernetes
data:
  flink-conf.yaml: |
    jobmanager.memory.process.size: 2048m
    taskmanager.memory.process.size: 2048m
    taskmanager.numberOfTaskSlots: 2
    state.backend: rocksdb
    state.checkpoints.dir: s3://arch-sink-checkpoints/flink/checkpoints
    state.savepoints.dir: s3://arch-sink-savepoints/flink/savepoints
    execution.checkpointing.interval: 10s
    execution.checkpointing.mode: EXACTLY_ONCE
  log4j-console.properties: |
    rootLogger.level = INFO
    rootLogger.appenderRef.console.ref = ConsoleAppender
    appender.console.name = ConsoleAppender
    appender.console.type = CONSOLE
    appender.console.layout.type = PatternLayout
    appender.console.layout.pattern = %d{yyyy-MM-dd HH:mm:ss,SSS} %-5p %-60c %x - %m%n
