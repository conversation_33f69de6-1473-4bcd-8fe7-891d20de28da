apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: flink-config-pvc
  namespace: flink
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1Gi
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: flink-config-init
  namespace: flink
data:
  init.sh: |
    #!/bin/sh
    set -e
    cp -r /opt/flink/conf/* /config/
    chmod -R 777 /config/
    echo "Flink configuration copied to persistent volume"
---
apiVersion: batch/v1
kind: Job
metadata:
  name: flink-config-init-job
  namespace: flink
spec:
  template:
    spec:
      containers:
      - name: init-container
        image: flink:1.18.1-java17
        command: ["/bin/sh", "/scripts/init.sh"]
        volumeMounts:
        - name: config-script
          mountPath: /scripts
        - name: flink-config
          mountPath: /config
      restartPolicy: Never
      volumes:
      - name: config-script
        configMap:
          name: flink-config-init
          defaultMode: 0777
      - name: flink-config
        persistentVolumeClaim:
          claimName: flink-config-pvc
  backoffLimit: 1
