---
apiVersion: flink.apache.org/v1beta1
kind: FlinkDeployment
metadata:
  name: order-enrichment
  namespace: flink
spec:
  image: ************.dkr.ecr.us-west-2.amazonaws.com/flink-jobs:latest
  flinkVersion: v1_17
  serviceAccount: flink
  jobManager:
    resource:
      memory: "2048m"
      cpu: 1
    podTemplate:
      spec:
        volumes:
        - name: flink-config-volume
          configMap:
            name: flink-config-order-enrichment
        - name: checkpoint-volume
          emptyDir: {}
        containers:
        - name: flink-main-container
          volumeMounts:
          - name: flink-config-volume
            mountPath: /opt/flink/conf
          - name: checkpoint-volume
            mountPath: /tmp/flink-checkpoints
          command:
          - "/bin/bash"
          - "-c"
          - |
            mkdir -p /tmp/flink-checkpoints /tmp/flink-savepoints
            chmod -R 777 /tmp/flink-checkpoints /tmp/flink-savepoints
            exec /opt/flink/bin/kubernetes-jobmanager.sh kubernetes-application
  taskManager:
    resource:
      memory: "2048m"
      cpu: 1
    podTemplate:
      spec:
        volumes:
        - name: flink-config-volume
          configMap:
            name: flink-config-order-enrichment
        - name: checkpoint-volume
          emptyDir: {}
        containers:
        - name: flink-main-container
          volumeMounts:
          - name: flink-config-volume
            mountPath: /opt/flink/conf
          - name: checkpoint-volume
            mountPath: /tmp/flink-checkpoints
  job:
    jarURI: local:///opt/flink/usrlib/flink-jobs.jar
    parallelism: 1
    upgradeMode: stateless
    state: running
