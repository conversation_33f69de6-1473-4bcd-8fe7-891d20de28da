---
apiVersion: flink.apache.org/v1beta1
kind: FlinkDeployment
metadata:
  name: order-enrichment
  namespace: flink
spec:
  image: ************.dkr.ecr.us-west-2.amazonaws.com/flink-jobs:latest
  flinkVersion: v1_17
  serviceAccount: flink
  jobManager:
    resource:
      memory: "2048m"
      cpu: 1
    podTemplate:
      spec:
        initContainers:
        - name: config-init
          image: busybox:1.35.0
          command: ["sh", "-c", "mkdir -p /writable-conf && cp -r /opt/flink/conf/* /writable-conf/ && chmod -R 777 /writable-conf/"]
          volumeMounts:
          - name: flink-config-volume
            mountPath: /opt/flink/conf
          - name: writable-conf-volume
            mountPath: /writable-conf
        containers:
        - name: flink-main-container
          env:
          - name: FLINK_CONF_DIR
            value: /writable-conf
          volumeMounts:
          - name: writable-conf-volume
            mountPath: /writable-conf
        volumes:
        - name: writable-conf-volume
          emptyDir: {}
  taskManager:
    resource:
      memory: "2048m"
      cpu: 1
    podTemplate:
      spec:
        initContainers:
        - name: config-init
          image: busybox:1.35.0
          command: ["sh", "-c", "mkdir -p /writable-conf && cp -r /opt/flink/conf/* /writable-conf/ && chmod -R 777 /writable-conf/"]
          volumeMounts:
          - name: flink-config-volume
            mountPath: /opt/flink/conf
          - name: writable-conf-volume
            mountPath: /writable-conf
        containers:
        - name: flink-main-container
          env:
          - name: FLINK_CONF_DIR
            value: /writable-conf
          volumeMounts:
          - name: writable-conf-volume
            mountPath: /writable-conf
        volumes:
        - name: writable-conf-volume
          emptyDir: {}
  flinkConfiguration:
    state.backend: rocksdb
    state.checkpoints.dir: file:///tmp/flink-checkpoints
    state.savepoints.dir: file:///tmp/flink-savepoints
    execution.checkpointing.interval: 10s
    execution.checkpointing.mode: EXACTLY_ONCE
  job:
    jarURI: local:///opt/flink/usrlib/flink-jobs.jar
    parallelism: 2
    upgradeMode: stateless
    state: running
