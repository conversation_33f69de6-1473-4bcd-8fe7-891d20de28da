---
apiVersion: flink.apache.org/v1beta1
kind: FlinkDeployment
metadata:
  name: order-enrichment
  namespace: flink
spec:
  image: ************.dkr.ecr.us-west-2.amazonaws.com/flink-jobs:latest
  flinkVersion: v1_17
  serviceAccount: flink
  jobManager:
    resource:
      memory: "2048m"
      cpu: 1
  taskManager:
    resource:
      memory: "2048m"
      cpu: 1
  flinkConfiguration:
    state.backend: filesystem
    state.checkpoints.dir: file:///tmp/flink-checkpoints
    state.savepoints.dir: file:///tmp/flink-savepoints
    execution.checkpointing.interval: 10s
    execution.checkpointing.mode: EXACTLY_ONCE
    kubernetes.container.image.pull-policy: Always
  job:
    jarURI: local:///opt/flink/usrlib/flink-jobs.jar
    parallelism: 1
    upgradeMode: stateless
    args: ["--checkpointDir", "/tmp/flink-checkpoints"]
    state: running
