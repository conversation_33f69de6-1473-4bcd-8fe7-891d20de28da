<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.goodrx</groupId>
  <artifactId>flink-jobs</artifactId>
  <name>flink-jobs</name>
  <version>1.0-SNAPSHOT</version>
  <description>Flink jobs for gRPC enrichment</description>
  <url>http://maven.apache.org</url>
  <build>
    <plugins>
      <plugin>
        <groupId>kr.motd.maven</groupId>
        <artifactId>os-maven-plugin</artifactId>
        <version>${os.maven.plugin.version}</version>
        <executions>
          <execution>
            <phase>initialize</phase>
            <goals>
              <goal>detect</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>build-helper-maven-plugin</artifactId>
        <version>3.3.0</version>
        <executions>
          <execution>
            <id>add-protobuf-sources</id>
            <phase>generate-sources</phase>
            <goals>
              <goal>add-source</goal>
            </goals>
            <configuration>
              <sources>
                <source>${project.build.directory}/generated-sources/protobuf/java</source>
              </sources>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.xolstice.maven.plugins</groupId>
        <artifactId>protobuf-maven-plugin</artifactId>
        <version>${protobuf.maven.plugin.version}</version>
        <executions>
          <execution>
            <goals>
              <goal>compile</goal>
              <goal>compile-custom</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <protocArtifact>com.google.protobuf:protoc:${protobuf.version}:exe:${os.detected.classifier}</protocArtifact>
          <pluginId>grpc-java</pluginId>
          <pluginArtifact>io.grpc:protoc-gen-grpc-java:${grpc.version}:exe:${os.detected.classifier}</pluginArtifact>
          <protoSourceRoot>${project.basedir}/src/main/proto</protoSourceRoot>
          <clearOutputDirectory>false</clearOutputDirectory>
          <outputDirectory>${project.build.directory}/generated-sources/protobuf/java</outputDirectory>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.8.1</version>
        <configuration>
          <source>${java.version}</source>
          <target>${java.version}</target>
          <compilerArgs>
            <arg>-Xlint:all,-serial,-path</arg>
            <arg>-parameters</arg>
          </compilerArgs>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>${maven.surefire.plugin.version}</version>
        <configuration>
          <excludes>
            <exclude>**/*IntegrationTest.java</exclude>
          </excludes>
          <argLine>--add-opens java.base/java.util=ALL-UNNAMED --add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.time=ALL-UNNAMED</argLine>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>build-helper-maven-plugin</artifactId>
        <version>${build.helper.maven.plugin.version}</version>
        <executions>
          <execution>
            <id>add-integration-test-source</id>
            <phase>generate-test-sources</phase>
            <goals>
              <goal>add-test-source</goal>
            </goals>
            <configuration>
              <sources>
                <source>${project.basedir}/src/test-integration/java</source>
              </sources>
            </configuration>
          </execution>
          <execution>
            <id>add-protobuf-sources</id>
            <phase>generate-sources</phase>
            <goals>
              <goal>add-source</goal>
            </goals>
            <configuration>
              <sources>
                <source>${project.build.directory}/generated-sources/protobuf/java</source>
              </sources>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-failsafe-plugin</artifactId>
        <version>${maven.failsafe.plugin.version}</version>
        <executions>
          <execution>
            <id>integration-tests</id>
            <phase>integration-test</phase>
            <goals>
              <goal>integration-test</goal>
              <goal>verify</goal>
            </goals>
            <configuration>
              <includes>
                <include>**/*IntegrationTest.java</include>
              </includes>
              <argLine>--add-opens java.base/java.util=ALL-UNNAMED --add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.time=ALL-UNNAMED</argLine>
            </configuration>
          </execution>
        </executions>
        <configuration>
          <argLine>--add-opens java.base/java.util=ALL-UNNAMED --add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.time=ALL-UNNAMED</argLine>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <version>${maven.shade.plugin.version}</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <filters>
                <filter>
                  <artifact>*:*</artifact>
                  <excludes>
                    <exclude>META-INF/*.SF</exclude>
                    <exclude>META-INF/*.DSA</exclude>
                    <exclude>META-INF/*.RSA</exclude>
                  </excludes>
                </filter>
              </filters>
              <transformers>
                <transformer>
                  <mainClass>com.goodrx.flink.OrderEnrichmentJob</mainClass>
                </transformer>
                <transformer />
              </transformers>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
  <repositories>
    <repository>
      <id>central</id>
      <name>Maven Central</name>
      <url>https://repo.maven.apache.org/maven2/</url>
    </repository>
    <repository>
      <id>apache</id>
      <name>Apache Maven Repository</name>
      <url>https://repository.apache.org/content/repositories/releases/</url>
    </repository>
    <repository>
      <id>confluent</id>
      <name>Confluent Maven Repository</name>
      <url>https://packages.confluent.io/maven/</url>
    </repository>
  </repositories>
  <pluginRepositories>
    <pluginRepository>
      <releases />
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
      <id>apache-releases</id>
      <url>https://repository.apache.org/content/repositories/releases/</url>
    </pluginRepository>
    <pluginRepository>
      <releases>
        <enabled>false</enabled>
      </releases>
      <snapshots />
      <id>apache-snapshots</id>
      <url>https://repository.apache.org/content/repositories/snapshots/</url>
    </pluginRepository>
  </pluginRepositories>
  <dependencies>
    <dependency>
      <groupId>org.apache.flink</groupId>
      <artifactId>flink-java</artifactId>
      <version>1.18.1</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.flink</groupId>
      <artifactId>flink-streaming-java</artifactId>
      <version>1.18.1</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.flink</groupId>
      <artifactId>flink-clients</artifactId>
      <version>1.18.1</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.flink</groupId>
      <artifactId>flink-json</artifactId>
      <version>1.18.1</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.flink</groupId>
      <artifactId>flink-table-planner-loader</artifactId>
      <version>1.18.1</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.flink</groupId>
      <artifactId>flink-test-utils</artifactId>
      <version>1.18.1</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>flink-core</artifactId>
          <groupId>org.apache.flink</groupId>
        </exclusion>
        <exclusion>
          <artifactId>flink-rpc-akka-loader</artifactId>
          <groupId>org.apache.flink</groupId>
        </exclusion>
        <exclusion>
          <artifactId>junit-jupiter</artifactId>
          <groupId>org.junit.jupiter</groupId>
        </exclusion>
        <exclusion>
          <artifactId>flink-statebackend-rocksdb</artifactId>
          <groupId>org.apache.flink</groupId>
        </exclusion>
        <exclusion>
          <artifactId>flink-statebackend-changelog</artifactId>
          <groupId>org.apache.flink</groupId>
        </exclusion>
        <exclusion>
          <artifactId>flink-dstl-dfs</artifactId>
          <groupId>org.apache.flink</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.flink</groupId>
      <artifactId>flink-test-utils-junit</artifactId>
      <version>1.18.1</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>junit-jupiter</artifactId>
          <groupId>org.junit.jupiter</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.apache.flink</groupId>
      <artifactId>flink-runtime</artifactId>
      <version>1.18.1</version>
      <type>test-jar</type>
      <classifier>tests</classifier>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.flink</groupId>
      <artifactId>flink-streaming-java</artifactId>
      <version>1.18.1</version>
      <type>test-jar</type>
      <classifier>tests</classifier>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>testcontainers</artifactId>
      <version>1.19.3</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>junit</artifactId>
          <groupId>junit</groupId>
        </exclusion>
        <exclusion>
          <artifactId>duct-tape</artifactId>
          <groupId>org.rnorth.duct-tape</groupId>
        </exclusion>
        <exclusion>
          <artifactId>docker-java-api</artifactId>
          <groupId>com.github.docker-java</groupId>
        </exclusion>
        <exclusion>
          <artifactId>docker-java-transport-zerodep</artifactId>
          <groupId>com.github.docker-java</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>kafka</artifactId>
      <version>1.19.3</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>postgresql</artifactId>
      <version>1.19.3</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>jdbc</artifactId>
          <groupId>org.testcontainers</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>junit-jupiter</artifactId>
      <version>1.19.3</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.assertj</groupId>
      <artifactId>assertj-core</artifactId>
      <version>3.24.2</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>byte-buddy</artifactId>
          <groupId>net.bytebuddy</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.awaitility</groupId>
      <artifactId>awaitility</artifactId>
      <version>4.2.0</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>hamcrest</artifactId>
          <groupId>org.hamcrest</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.github.tomakehurst</groupId>
      <artifactId>wiremock-jre8</artifactId>
      <version>2.35.0</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>jetty-server</artifactId>
          <groupId>org.eclipse.jetty</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jetty-servlet</artifactId>
          <groupId>org.eclipse.jetty</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jetty-servlets</artifactId>
          <groupId>org.eclipse.jetty</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jetty-webapp</artifactId>
          <groupId>org.eclipse.jetty</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jetty-proxy</artifactId>
          <groupId>org.eclipse.jetty</groupId>
        </exclusion>
        <exclusion>
          <artifactId>http2-server</artifactId>
          <groupId>org.eclipse.jetty.http2</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jetty-alpn-server</artifactId>
          <groupId>org.eclipse.jetty</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jetty-alpn-java-server</artifactId>
          <groupId>org.eclipse.jetty</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jetty-alpn-openjdk8-server</artifactId>
          <groupId>org.eclipse.jetty</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jetty-alpn-java-client</artifactId>
          <groupId>org.eclipse.jetty</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jetty-alpn-openjdk8-client</artifactId>
          <groupId>org.eclipse.jetty</groupId>
        </exclusion>
        <exclusion>
          <artifactId>httpclient5</artifactId>
          <groupId>org.apache.httpcomponents.client5</groupId>
        </exclusion>
        <exclusion>
          <artifactId>xmlunit-core</artifactId>
          <groupId>org.xmlunit</groupId>
        </exclusion>
        <exclusion>
          <artifactId>xmlunit-legacy</artifactId>
          <groupId>org.xmlunit</groupId>
        </exclusion>
        <exclusion>
          <artifactId>xmlunit-placeholders</artifactId>
          <groupId>org.xmlunit</groupId>
        </exclusion>
        <exclusion>
          <artifactId>json-unit-core</artifactId>
          <groupId>net.javacrumbs.json-unit</groupId>
        </exclusion>
        <exclusion>
          <artifactId>json-path</artifactId>
          <groupId>com.jayway.jsonpath</groupId>
        </exclusion>
        <exclusion>
          <artifactId>asm</artifactId>
          <groupId>org.ow2.asm</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jopt-simple</artifactId>
          <groupId>net.sf.jopt-simple</groupId>
        </exclusion>
        <exclusion>
          <artifactId>handlebars</artifactId>
          <groupId>com.github.jknack</groupId>
        </exclusion>
        <exclusion>
          <artifactId>handlebars-helpers</artifactId>
          <groupId>com.github.jknack</groupId>
        </exclusion>
        <exclusion>
          <artifactId>commons-fileupload</artifactId>
          <groupId>commons-fileupload</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-simple</artifactId>
      <version>1.7.36</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.tomcat</groupId>
      <artifactId>annotations-api</artifactId>
      <version>6.0.53</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.platform</groupId>
      <artifactId>junit-platform-engine</artifactId>
      <version>1.10.2</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>opentest4j</artifactId>
          <groupId>org.opentest4j</groupId>
        </exclusion>
        <exclusion>
          <artifactId>apiguardian-api</artifactId>
          <groupId>org.apiguardian</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.junit.platform</groupId>
      <artifactId>junit-platform-launcher</artifactId>
      <version>1.10.2</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>apiguardian-api</artifactId>
          <groupId>org.apiguardian</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.junit.platform</groupId>
      <artifactId>junit-platform-commons</artifactId>
      <version>1.10.2</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>apiguardian-api</artifactId>
          <groupId>org.apiguardian</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-testing</artifactId>
      <version>1.60.0</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>junit</artifactId>
          <groupId>junit</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-api</artifactId>
      <version>5.10.2</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>opentest4j</artifactId>
          <groupId>org.opentest4j</groupId>
        </exclusion>
        <exclusion>
          <artifactId>apiguardian-api</artifactId>
          <groupId>org.apiguardian</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-engine</artifactId>
      <version>5.10.2</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>apiguardian-api</artifactId>
          <groupId>org.apiguardian</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-params</artifactId>
      <version>5.10.2</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>apiguardian-api</artifactId>
          <groupId>org.apiguardian</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>5.11.0</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>byte-buddy-agent</artifactId>
          <groupId>net.bytebuddy</groupId>
        </exclusion>
        <exclusion>
          <artifactId>byte-buddy</artifactId>
          <groupId>net.bytebuddy</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-junit-jupiter</artifactId>
      <version>5.11.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.vintage</groupId>
      <artifactId>junit-vintage-engine</artifactId>
      <version>5.10.2</version>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>junit</artifactId>
          <groupId>junit</groupId>
        </exclusion>
        <exclusion>
          <artifactId>apiguardian-api</artifactId>
          <groupId>org.apiguardian</groupId>
        </exclusion>
      </exclusions>
    </dependency>
  </dependencies>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.junit</groupId>
        <artifactId>junit-bom</artifactId>
        <version>5.10.2</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.apache.flink</groupId>
        <artifactId>flink-parent</artifactId>
        <version>${flink.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter-api</artifactId>
        <version>5.10.2</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter-engine</artifactId>
        <version>5.10.2</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.junit.jupiter</groupId>
        <artifactId>junit-jupiter-params</artifactId>
        <version>5.10.2</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-core</artifactId>
        <version>5.11.0</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-junit-jupiter</artifactId>
        <version>5.11.0</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>io.grpc</groupId>
        <artifactId>grpc-testing</artifactId>
        <version>${grpc.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.junit.vintage</groupId>
        <artifactId>junit-vintage-engine</artifactId>
        <version>5.10.2</version>
        <scope>test</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <properties>
    <scala.binary.version>2.12</scala.binary.version>
    <maven.failsafe.plugin.version>3.0.0-M7</maven.failsafe.plugin.version>
    <java.version>17</java.version>
    <maven.compiler.source>${java.version}</maven.compiler.source>
    <flink.scope>provided</flink.scope>
    <flink.version>1.18.1</flink.version>
    <maven.shade.plugin.version>3.3.0</maven.shade.plugin.version>
    <protobuf.maven.plugin.version>0.6.1</protobuf.maven.plugin.version>
    <os.maven.plugin.version>1.7.1</os.maven.plugin.version>
    <maven.compiler.target>${java.version}</maven.compiler.target>
    <maven.compiler.plugin.version>3.10.1</maven.compiler.plugin.version>
    <protobuf.version>3.25.1</protobuf.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <build.helper.maven.plugin.version>3.3.0</build.helper.maven.plugin.version>
    <maven.surefire.plugin.version>3.0.0</maven.surefire.plugin.version>
    <grpc.version>1.60.0</grpc.version>
  </properties>
</project>
