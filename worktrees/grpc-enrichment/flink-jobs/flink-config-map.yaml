apiVersion: v1
kind: ConfigMap
metadata:
  name: flink-config-order-enrichment
  namespace: flink
data:
  flink-conf.yaml: |
    jobmanager.memory.process.size: 2048m
    taskmanager.memory.process.size: 2048m
    taskmanager.numberOfTaskSlots: 2
    state.backend: filesystem
    state.checkpoints.dir: file:///tmp/flink-checkpoints
    state.savepoints.dir: file:///tmp/flink-savepoints
    execution.checkpointing.interval: 10s
    execution.checkpointing.mode: EXACTLY_ONCE
    kubernetes.container.image.pull-policy: Always
    blob.server.port: 6124
    query.server.port: 6125
    rest.port: 8081
    jobmanager.rpc.address: localhost
    taskmanager.rpc.port: 6122
    jobmanager.rpc.port: 6123
    classloader.resolve-order: parent-first
    env.java.opts: "-XX:+UseG1GC"
