---
# Source: flink-platform/templates/order-enrichment-job.yaml
apiVersion: flink.apache.org/v1beta1
kind: FlinkDeployment
metadata:
  name: order-enrichment
  namespace: flink
spec:
  image: ************.dkr.ecr.us-west-2.amazonaws.com/flink-jobs:latest
  flinkVersion: v1_17
  serviceAccount: flink
  jobManager:
    resource:
      memory: "2048m"
      cpu: 1
    env:
      - name: FLINK_PROPERTIES
        value: |
          jobmanager.memory.process.size: 2048m
          taskmanager.memory.process.size: 2048m
          taskmanager.numberOfTaskSlots: 2
          state.backend: rocksdb
          state.checkpoints.dir: s3://arch-sink-checkpoints/flink/checkpoints
          state.savepoints.dir: s3://arch-sink-savepoints/flink/savepoints
          execution.checkpointing.interval: 10s
          execution.checkpointing.mode: EXACTLY_ONCE
          s3.endpoint: s3.us-west-2.amazonaws.com
          s3.path.style.access: false
          s3.region: us-west-2
  taskManager:
    resource:
      memory: "2048m"
      cpu: 1
    env:
      - name: FLINK_PROPERTIES
        value: |
          jobmanager.memory.process.size: 2048m
          taskmanager.memory.process.size: 2048m
          taskmanager.numberOfTaskSlots: 2
          state.backend: rocksdb
          state.checkpoints.dir: s3://arch-sink-checkpoints/flink/checkpoints
          state.savepoints.dir: s3://arch-sink-savepoints/flink/savepoints
          execution.checkpointing.interval: 10s
          execution.checkpointing.mode: EXACTLY_ONCE
          s3.endpoint: s3.us-west-2.amazonaws.com
          s3.path.style.access: false
          s3.region: us-west-2
  job:
    jarURI: local:///opt/flink/usrlib/flink-jobs.jar
    parallelism: 2
    upgradeMode: savepoint
    state: running
