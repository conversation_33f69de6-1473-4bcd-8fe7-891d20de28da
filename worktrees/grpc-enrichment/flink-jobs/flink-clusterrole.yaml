apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: flink-cluster-role
rules:
  - apiGroups: [""]
    resources: ["pods", "pods/log", "services", "configmaps", "endpoints"]
    verbs: ["get", "list", "watch", "create", "delete", "update", "patch"]
  - apiGroups: ["apps"]
    resources: ["deployments"]
    verbs: ["get", "list", "watch", "create", "delete", "update", "patch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: flink-cluster-role-binding
subjects:
  - kind: ServiceAccount
    name: flink
    namespace: flink
roleRef:
  kind: ClusterRole
  name: flink-cluster-role
  apiGroup: rbac.authorization.k8s.io
