# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Fixed
- Corrected compilation errors in `OrderEnrichmentFunctionTest` by updating to Flink 1.18.1 test harness APIs (`OneInputStreamOperatorTestHarness`).
- Fixed compilation error in `OrderProductEnrichmentIntegrationTest` related to an incorrect `EnrichedOrder` constructor call.
- Resolved test failures in `OrderEnrichmentJobMainIntegrationTest#testSimpleDirectPipeline`:
    - Ensured correct product data (ID 101) is initialized in the test database.
    - Refactored test result collection using static lists and dedicated static nested sink classes (`OrderCaptureSink`, `TestPipelineCollectSink`) to ensure visibility of results between Flink operator threads and the main test thread.
    - Corrected product description assertion to match database data.
