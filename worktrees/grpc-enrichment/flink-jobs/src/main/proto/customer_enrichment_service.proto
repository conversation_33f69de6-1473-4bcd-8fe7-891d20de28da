syntax = "proto3";

option java_multiple_files = true;
option java_package = "com.goodrx.flink.grpc";
option java_outer_classname = "CustomerServiceProto";

package customer;

service CustomerService {
  rpc GetCustomer (CustomerRequest) returns (CustomerResponse) {}
  rpc GetCustomersInBatch (CustomerBatchRequest) returns (CustomerBatchResponse) {}
  rpc GetCustomersStream (stream CustomerRequest) returns (stream CustomerResponse) {}
}

message CustomerRequest {
  int32 customer_id = 1;
}

message CustomerResponse {
  int32 id = 1;
  string first_name = 2;
  string last_name = 3;
  string email = 4;
  repeated Address addresses = 5;
}

message Address {
  string type = 1;
  string street = 2;
  string city = 3;
  string state = 4;
  string zip = 5;
}

message CustomerBatchRequest {
  repeated CustomerRequest requests = 1;
}

message CustomerBatchResponse {
  repeated CustomerResponse responses = 1;
}
