syntax = "proto3";

option java_multiple_files = true;
option java_package = "com.goodrx.flink.grpc";
option java_outer_classname = "ProductServiceProto";

package product;

service ProductService {
  rpc GetProduct (ProductRequest) returns (ProductResponse) {}
  rpc GetProductsInBatch (ProductBatchRequest) returns (ProductBatchResponse) {}
  rpc GetProductsStream (stream ProductRequest) returns (stream ProductResponse) {}
}

message ProductRequest {
  int32 product_id = 1;
}

message ProductResponse {
  int32 id = 1;
  string name = 2;
  string description = 3;
  double price = 4;
  string category = 5;
  map<string, string> attributes = 6;
}

message ProductBatchRequest {
  repeated ProductRequest requests = 1;
}

message ProductBatchResponse {
  repeated ProductResponse responses = 1;
}
