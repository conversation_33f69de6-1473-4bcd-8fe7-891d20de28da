# Flink Job Configuration (Development)

# Job specific parameters
job.name=OrderEnrichmentJob-Dev
job.parallelism=2

# Checkpointing configuration
checkpoint.interval.ms=30000
checkpoint.min.pause.ms=15000
checkpoint.timeout.ms=90000
checkpoint.max.concurrent=1
# checkpoint.dir=file:///tmp/flink-checkpoints/order-enrichment-dev

# Kafka Source (Order Topic)
kafka.source.bootstrap.servers=localhost:9092
kafka.source.topic=orders_topic
kafka.source.group.id=order_enrichment_job_dev_consumer

# Kafka Sink (Enriched Order Topic)
kafka.sink.bootstrap.servers=localhost:9092
kafka.sink.topic=enriched_orders_topic

# gRPC Service Endpoints (Example: Product Service)
# These would be used by the GrpcClientManager or specific UDFs
product.service.host=localhost
product.service.port=50051
product.service.usetls=false

# Example: Customer Service
customer.service.host=localhost
customer.service.port=50052
customer.service.usetls=false
