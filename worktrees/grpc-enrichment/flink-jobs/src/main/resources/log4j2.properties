# Log4j 2.x configuration for Flink job

# Set to info, warn, error, debug etc.
status = warn

# Name of the configuration
name = FlinkJobLogConfig

# Console appender
appender.console.type = Console
appender.console.name = STDOUT
appender.console.layout.type = PatternLayout
appender.console.layout.pattern = %d{yyyy-MM-dd HH:mm:ss,SSS} %-5p %-60c %x - %m%n

# Root logger configuration
rootLogger.level = INFO
rootLogger.appenderRef.stdout.ref = STDOUT

# You can set specific log levels for your application packages
# logger.com.goodrx.flink.enrichment.name = com.goodrx.flink.enrichment
# logger.com.goodrx.flink.enrichment.level = DEBUG

# Flink internal loggers (example: set to WARN to reduce verbosity)
logger.orgapacheflink.name = org.apache.flink
logger.orgapacheflink.level = WARN

logger.orgapachekafka.name = org.apache.kafka
logger.orgapachekafka.level = WARN

# Akka and <PERSON><PERSON> can be verbose, adjust as needed
logger.akka.name = akka
logger.akka.level = WARN

logger.orgapachezookeeper.name = org.apache.zookeeper
logger.orgapachezookeeper.level = WARN

logger.orgeclipsejetty.name = org.eclipse.jetty
logger.orgeclipsejetty.level = WARN

# GRPC loggers (can be noisy on DEBUG)
logger.iogrpc.name = io.grpc
logger.iogrpc.level = INFO
