-- Create customers table
CREATE TABLE IF NOT EXISTS customers (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    email TEXT NOT NULL,
    tier TEXT NOT NULL,
    region TEXT NOT NULL,
    active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(name);

-- Insert sample data
INSERT INTO customers (name, email, tier, region, active) VALUES
    ('<PERSON>', '<EMAIL>', 'GOLD', 'WEST', true),
    ('<PERSON>', '<EMAIL>', 'PLATINUM', 'EAST', true),
    ('<PERSON>', '<EMAIL>', 'SILVER', 'SOUTH', true),
    ('<PERSON>', '<EMAIL>', 'GOLD', 'NORTH', true),
    ('<PERSON>', '<EMAIL>', 'PLATINUM', 'WEST', true)
ON CONFLICT (id) DO NOTHING;
