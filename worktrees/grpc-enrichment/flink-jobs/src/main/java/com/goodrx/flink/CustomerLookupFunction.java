package com.goodrx.flink;

import com.goodrx.flink.model.Customer;
import com.goodrx.flink.model.Order;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.async.ResultFuture;
import org.apache.flink.streaming.api.functions.async.RichAsyncFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;
import org.apache.commons.dbcp2.BasicDataSource;

/**
 * An asynchronous function that enriches Order objects with Customer data.
 * <p>
 * This function connects to a PostgreSQL database and looks up customer details
 * based on the purchaser ID in the order. It then attaches the customer information
 * to the order object. The lookup is performed asynchronously to improve performance.
 * </p>
 * <p>
 * If the order has no purchaser ID, or if the customer cannot be found in the database,
 * the order is returned unchanged.
 * </p>
 * <p>
 * This implementation uses a connection pool and a dedicated thread pool to perform
 * database operations asynchronously, following the Flink AsyncFunction pattern.
 * Each query is submitted to the thread pool, which executes it using a connection
 * from the pool, then returns the result via CompletableFuture.
 * </p>
 */
public class CustomerLookupFunction extends RichAsyncFunction<Order, Order> {
    private static final long serialVersionUID = 1L;
    private static final Logger LOG = LoggerFactory.getLogger(CustomerLookupFunction.class);

    private transient BasicDataSource dataSource;
    private transient ExecutorService executorService;
    
    private final String jdbcUrl;
    private final String jdbcUser;
    private final String jdbcPass;
    private final int numThreads;
    private final int maxConnections;

    /**
     * No-arg constructor for unit tests (overridden createConnection).
     * This allows tests to mock the database connection while still testing the function logic.
     */
    public CustomerLookupFunction() {
        this("", "", "", 4, 8);
    }

    /**
     * Creates a new CustomerLookupFunction with the specified JDBC connection parameters.
     *
     * @param jdbcUrl  The JDBC URL for the database connection
     * @param jdbcUser The username for the database connection
     * @param jdbcPass The password for the database connection
     */
    public CustomerLookupFunction(String jdbcUrl, String jdbcUser, String jdbcPass) {
        this(jdbcUrl, jdbcUser, jdbcPass, 4, 8);
    }
    
    /**
     * Creates a new CustomerLookupFunction with the specified JDBC connection parameters
     * and thread pool size.
     *
     * @param jdbcUrl     The JDBC URL for the database connection
     * @param jdbcUser    The username for the database connection
     * @param jdbcPass    The password for the database connection
     * @param numThreads  The number of threads to use for async execution
     */
    public CustomerLookupFunction(String jdbcUrl, String jdbcUser, String jdbcPass, int numThreads) {
        this(jdbcUrl, jdbcUser, jdbcPass, numThreads, numThreads * 2);
    }
    
    /**
     * Creates a new CustomerLookupFunction with the specified JDBC connection parameters,
     * thread pool size, and connection pool size.
     *
     * @param jdbcUrl        The JDBC URL for the database connection
     * @param jdbcUser       The username for the database connection
     * @param jdbcPass       The password for the database connection
     * @param numThreads     The number of threads to use for async execution
     * @param maxConnections The maximum number of connections in the pool
     */
    public CustomerLookupFunction(String jdbcUrl, String jdbcUser, String jdbcPass, int numThreads, int maxConnections) {
        this.jdbcUrl = jdbcUrl;
        this.jdbcUser = jdbcUser;
        this.jdbcPass = jdbcPass;
        this.numThreads = numThreads;
        this.maxConnections = maxConnections;
    }

    /**
     * Initializes the database connection pool and the thread pool for async execution.
     *
     * @param parameters Configuration parameters for the function
     * @throws Exception if the database connection cannot be established
     */
    @Override
    public void open(Configuration parameters) throws Exception {
        // Initialize connection pool
        dataSource = new BasicDataSource();
        dataSource.setDriverClassName("org.postgresql.Driver");
        dataSource.setUrl(getJdbcUrl());
        dataSource.setUsername(getJdbcUser());
        dataSource.setPassword(getJdbcPass());
        dataSource.setInitialSize(2);
        dataSource.setMaxTotal(maxConnections);
        dataSource.setMaxIdle(maxConnections);
        dataSource.setMinIdle(1);
        dataSource.setMaxWaitMillis(5000);
        dataSource.setTestOnBorrow(true);
        dataSource.setTestWhileIdle(true);
        dataSource.setValidationQuery("SELECT 1");
        
        // Create thread pool with named threads for better debugging
        executorService = Executors.newFixedThreadPool(numThreads, new ThreadFactory() {
            private final AtomicInteger counter = new AtomicInteger();
            private final ThreadGroup group = new ThreadGroup("customer-lookup-pool");
            
            @Override
            public Thread newThread(Runnable r) {
                Thread t = new Thread(group, r, "customer-lookup-" + counter.incrementAndGet());
                if (t.isDaemon()) {
                    t.setDaemon(false);
                }
                if (t.getPriority() != Thread.NORM_PRIORITY) {
                    t.setPriority(Thread.NORM_PRIORITY);
                }
                return t;
            }
        });
        
        LOG.info("Initialized CustomerLookupFunction with {} threads and {} max connections", 
                numThreads, maxConnections);

        // Test connection pool with retries
        int maxRetries = 3;
        long retryDelayMs = 2000; // Increased delay to 2 seconds
        for (int i = 0; i < maxRetries; i++) {
            try (Connection conn = dataSource.getConnection()) {
                LOG.info("Successfully tested database connection pool for CustomerLookupFunction on attempt {}/{}", (i + 1), maxRetries);
                return; // Success
            } catch (SQLException e) { // Catch SQLException specifically for connection issues
                LOG.warn("Failed to test database connection pool for CustomerLookupFunction on attempt {}/{}. Retrying in {}ms...", 
                         (i + 1), maxRetries, retryDelayMs, e);
                if (i == maxRetries - 1) { // Last attempt
                    LOG.error("Failed to establish database connection for CustomerLookupFunction after {} retries.", maxRetries, e);
                    throw e; // Re-throw on last attempt
                }
                try {
                    Thread.sleep(retryDelayMs);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("Thread interrupted during retry delay", ie);
                }
            }
        }
    }

    /**
     * Creates a database connection from the connection pool.
     * This method is protected to allow overriding in tests.
     *
     * @return A Connection to the database from the pool
     * @throws SQLException if the connection cannot be established
     */
    protected Connection getConnection() throws SQLException {
        return dataSource.getConnection();
    }
    
    /**
     * Returns the JDBC URL for database connection.
     * Protected method for testability.
     * 
     * @return The JDBC URL
     */
    protected String getJdbcUrl() {
        return jdbcUrl;
    }
    
    /**
     * Returns the JDBC username for database connection.
     * Protected method for testability.
     * 
     * @return The JDBC username
     */
    protected String getJdbcUser() {
        return jdbcUser;
    }
    
    /**
     * Returns the JDBC password for database connection.
     * Protected method for testability.
     * 
     * @return The JDBC password
     */
    protected String getJdbcPass() {
        return jdbcPass;
    }

    /**
     * Closes the database resources when the function is no longer needed.
     *
     * @throws Exception if the resources cannot be closed properly
     */
    @Override
    public void close() throws Exception {
        if (dataSource != null) {
            dataSource.close();
        }
        if (executorService != null) {
            executorService.shutdown();
        }
        LOG.info("Closed CustomerLookupFunction resources");
    }

    /**
     * Performs an asynchronous lookup of customer data for an order.
     * <p>
     * This method checks if the order has a purchaser ID, and if so, looks up the
     * corresponding customer in the database using a thread pool and connection pool.
     * The customer data is then attached to the order object.
     * </p>
     * <p>
     * If the order has no purchaser ID, or if the customer cannot be found, the
     * order is returned unchanged.
     * </p>
     *
     * @param order         The order to enrich with customer data
     * @param resultFuture  The future to complete with the enriched order
     * @throws Exception if an error occurs during the lookup
     */
    @Override
    public void asyncInvoke(Order order, ResultFuture<Order> resultFuture) throws Exception {
        LOG.info("CustomerLookup: START asyncInvoke for orderId: {}, purchaserId: {}", order.getId(), order.getPurchaser());
        LOG.debug("Looking up customer for order ID={}, customerID={}", order.getId(), order.getPurchaser());
        
        // Skip if no customer ID
        if (order.getPurchaser() == null) {
            LOG.debug("Order {} has no customer ID, skipping lookup", order.getId());
            List<Order> result = new ArrayList<>(1);
            result.add(order);
            resultFuture.complete(result);
            return;
        }
        
        // Submit the database lookup to the thread pool
        CompletableFuture.supplyAsync(() -> {
            Order orderToEnrich = new Order(order); // Create a copy to modify
            Connection conn = null;
            PreparedStatement stmt = null;
            ResultSet rs = null;
            
            try {
                // Get connection from pool
                conn = getConnection();
                
                // Create a prepared statement for this lookup
                stmt = conn.prepareStatement(
                    "SELECT id, first_name, last_name, email FROM customers WHERE id = ?"
                );
                stmt.setInt(1, orderToEnrich.getPurchaser());
                LOG.debug("Executing async query for customer ID={}", orderToEnrich.getPurchaser());
                
                // Execute the query
                rs = stmt.executeQuery();
                
                if (rs.next()) {
                    // Found customer - create object and attach to order
                    Customer customer = new Customer();
                    customer.setId(rs.getInt("id"));
                    customer.setFirstName(rs.getString("first_name"));
                    customer.setLastName(rs.getString("last_name"));
                    customer.setEmail(rs.getString("email"));
                    
                    // Set customer on order
                    orderToEnrich = orderToEnrich.withCustomer(customer);
                    LOG.debug("Found and attached customer: {}={} {}", 
                            customer.getId(), customer.getFirstName(), customer.getLastName());
                } else {
                    LOG.warn("No customer found with ID={}", orderToEnrich.getPurchaser());
                }
            } catch (Exception e) {
                LOG.error("Error looking up customer: {}", e.getMessage(), e);
            } finally {
                // Close resources in reverse order
                try { if (rs != null) rs.close(); } catch (Exception e) { LOG.warn("Error closing ResultSet", e); }
                try { if (stmt != null) stmt.close(); } catch (Exception e) { LOG.warn("Error closing PreparedStatement", e); }
                try { if (conn != null) conn.close(); } catch (Exception e) { LOG.warn("Error closing Connection", e); }
            }
            
            return orderToEnrich;
        }, executorService).thenAccept(finalEnrichedOrder -> {
            // Complete the future with the result
            LOG.debug("Async lookup completed for order={}, customer={}", 
                    finalEnrichedOrder.getId(), 
                    finalEnrichedOrder.getCustomer() != null ? finalEnrichedOrder.getCustomer().getFirstName() : "null");
            List<Order> result = new ArrayList<>(1);
            result.add(finalEnrichedOrder);
            resultFuture.complete(result);
        });
    }

    /**
     * Handles timeout cases by completing the result future with the original order.
     *
     * @param order         The original order
     * @param resultFuture  The future to complete
     */
    @Override
    public void timeout(Order order, ResultFuture<Order> resultFuture) throws Exception {
        LOG.warn("Async customer lookup timed out for order {}", order.getId());
        List<Order> result = new ArrayList<>(1);
        result.add(order);
        resultFuture.complete(result);
    }
}
