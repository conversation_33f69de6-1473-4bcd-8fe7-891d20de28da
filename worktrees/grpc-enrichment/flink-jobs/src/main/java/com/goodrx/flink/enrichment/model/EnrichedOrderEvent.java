package com.goodrx.flink.enrichment.model;

import java.util.Objects;

/**
 * Represents an order event enriched with product information.
 */
public class EnrichedOrderEvent extends OrderEvent {
    private String productName;
    // Potentially other enriched fields like customerName, etc.

    // Default constructor
    public EnrichedOrderEvent() {
        super();
    }

    // Constructor to create from OrderEvent and add enrichment
    public EnrichedOrderEvent(OrderEvent orderEvent, String productName) {
        super(orderEvent.getOrderId(), orderEvent.getCustomerId(), orderEvent.getProductId(), 
              orderEvent.getQuantity(), orderEvent.getPrice(), orderEvent.getEventTimestamp());
        this.productName = productName;
    }

    // Getters and Setters
    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        EnrichedOrderEvent that = (EnrichedOrderEvent) o;
        return Objects.equals(productName, that.productName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), productName);
    }

    @Override
    public String toString() {
        return "EnrichedOrderEvent{" +
                "orderId='" + getOrderId() + '\'' +
                ", customerId='" + getCustomerId() + '\'' +
                ", productId='" + getProductId() + '\'' +
                ", quantity=" + getQuantity() +
                ", price=" + getPrice() +
                ", eventTimestamp=" + getEventTimestamp() +
                ", productName='" + productName + '\'' +
                '}';
    }
}
