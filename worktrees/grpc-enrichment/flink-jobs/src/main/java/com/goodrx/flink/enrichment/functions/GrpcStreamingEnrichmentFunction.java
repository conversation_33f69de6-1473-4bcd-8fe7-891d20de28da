package com.goodrx.flink.enrichment.functions;

import com.goodrx.flink.grpc.ProductRequest;
import com.goodrx.flink.grpc.ProductResponse;
import com.goodrx.flink.grpc.client.GrpcClientManager;
import com.goodrx.flink.grpc.client.ProductServiceClient; // Example, could be generic

import org.apache.flink.api.common.state.MapState;
import org.apache.flink.api.common.state.MapStateDescriptor;
import org.apache.flink.api.common.typeinfo.Types;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import io.grpc.stub.StreamObserver;

import com.goodrx.flink.enrichment.model.ProductDataEvent;

import java.util.UUID;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.logging.Level;
import java.util.logging.Logger;


// TODO: Implement robust backpressure handling (e.g., Flink's Mailbox model, request throttling).
// TODO: Implement parallel request processing (potentially multiple stream instances or careful management of one).
// TODO: Enhance error handling, retries, and state management for fault tolerance.
// TODO: Make gRPC target, timeouts, TLS, stream parameters configurable.
// TODO: Define concrete InputEvent and EnrichedEvent types.

/**
 * Flink ProcessFunction for enriching events using a bi-directional gRPC stream.
 * This is a conceptual skeleton and requires significant fleshing out for production use,
 * especially around backpressure, error handling, and state management for correlation.
 */
public class GrpcStreamingEnrichmentFunction extends ProcessFunction<ProductDataEvent, ProductDataEvent> {

    private static final Logger logger = Logger.getLogger(GrpcStreamingEnrichmentFunction.class.getName());

    private transient GrpcClientManager grpcClientManager;
    private transient ProductServiceClient productServiceClient; // Example, could be for CustomerService or generic
    private transient StreamObserver<ProductRequest> requestObserver;
    
    // State to correlate requests and responses if needed, or manage inflight requests
    private transient MapState<String, ProductDataEvent> inflightRequests;
    // Queue for elements to be sent on the gRPC stream, to handle backpressure from gRPC client to Flink
    private transient ConcurrentLinkedQueue<ProductRequest> sendQueue;


    // Configuration
    private final String serviceHost;
    private final int servicePort;
    private final boolean useTls;
    private final long grpcTimeoutSeconds; // May apply differently for streams

    public GrpcStreamingEnrichmentFunction(String serviceHost, int servicePort, boolean useTls, long grpcTimeoutSeconds) {
        this.serviceHost = serviceHost;
        this.servicePort = servicePort;
        this.useTls = useTls;
        this.grpcTimeoutSeconds = grpcTimeoutSeconds; // General timeout, stream behavior might differ
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        GrpcClientManager.GrpcClientConfig clientConfig = new GrpcClientManager.GrpcClientConfig(
                serviceHost, servicePort, useTls
        );
        grpcClientManager = new GrpcClientManager(clientConfig);
        // Using ProductService as an example for streaming
        productServiceClient = new ProductServiceClient(grpcClientManager, clientConfig, grpcTimeoutSeconds); 

        sendQueue = new ConcurrentLinkedQueue<>();

        MapStateDescriptor<String, ProductDataEvent> descriptor =
                new MapStateDescriptor<>("inflightRequests", Types.STRING, Types.POJO(ProductDataEvent.class));
        inflightRequests = getRuntimeContext().getMapState(descriptor);

        initializeGrpcStream();
        logger.info("GrpcStreamingEnrichmentFunction opened. Service target: " + serviceHost + ":" + servicePort);
    }

    private void initializeGrpcStream() {
        // This StreamObserver handles responses from the server
        StreamObserver<ProductResponse> responseObserver = new StreamObserver<ProductResponse>() {
            @Override
            public void onNext(ProductResponse productResponse) {
                // This is where responses from the gRPC stream are received.
                // Need to correlate productResponse with an original InputEvent.
                // For simplicity, let's assume productResponse contains an ID that can be used for correlation,
                // or we use a correlation ID pattern.
                String correlationId = String.valueOf(productResponse.getId()); // Example: using product ID as correlation
                try {
                    ProductDataEvent originalInput = inflightRequests.get(correlationId);
                    if (originalInput != null) {
                        ProductDataEvent enriched = originalInput; // originalInput is already ProductDataEvent
                        enriched.setProductName(productResponse.getName());
                        enriched.setProductDescription(productResponse.getDescription());
                        // ... set other fields ...
                        
                        // Output the enriched event - HOW? This method doesn't have a Collector.
                        // This logic needs to be integrated with processElement or an output queue.
                        // For now, just logging. A real implementation would need to emit this.
                        logger.log(Level.INFO, "Stream received and processed: " + enriched.getProductId());
                        inflightRequests.remove(correlationId);
                    } else {
                        logger.log(Level.WARNING, "Received response for unknown correlation ID: " + correlationId);
                    }
                } catch (Exception e) {
                    logger.log(Level.SEVERE, "Error processing streamed response for " + correlationId, e);
                }
            }

            @Override
            public void onError(Throwable t) {
                logger.log(Level.SEVERE, "gRPC stream error from server: " + t.getMessage(), t);
                // TODO: Implement robust error handling: re-establish stream, fail pending requests, etc.
                // Potentially clear inflightRequests and re-initialize.
                try {
                    Thread.sleep(1000); // Wait a bit before retrying
                } catch (InterruptedException e) { Thread.currentThread().interrupt(); }
                initializeGrpcStream(); // Attempt to re-initialize
            }

            @Override
            public void onCompleted() {
                logger.info("gRPC stream completed by server.");
                // TODO: Handle stream completion, re-establish if necessary for continuous processing.
                initializeGrpcStream(); // Attempt to re-initialize for continuous operation
            }
        };

        // This requestObserver is used to send requests to the server
        this.requestObserver = productServiceClient.getProductsStream(responseObserver);
        logger.info("gRPC stream initialized.");
    }

    @Override
    public void processElement(ProductDataEvent input, Context ctx, Collector<ProductDataEvent> out) throws Exception {
        // This is where Flink elements arrive.
        // We need to send them to the gRPC stream via requestObserver.
        
        String correlationId = UUID.randomUUID().toString(); // Or use a field from 'input' if suitable
        // Store the input event with its correlation ID for later when the response arrives.
        // Note: Product ID from input might not be unique if multiple requests for same product.
        // For bi-di stream, the server should echo back a correlation ID or key.
        // Here, we'll use input.getProductId() as a key, assuming it's what the server will use in response.
        // This is a simplification and might need a proper correlation ID mechanism.
        String requestKey = String.valueOf(input.getProductId()); 
        inflightRequests.put(requestKey, input);

        ProductRequest request = ProductRequest.newBuilder()
                .setProductId(input.getProductId())
                // .setCorrelationId(correlationId) // If proto supports it
                .build();
        
        try {
            requestObserver.onNext(request);
            logger.log(Level.FINE, "Sent request for product ID on stream: {0}", input.getProductId());
        } catch (Exception e) {
            logger.log(Level.SEVERE, "Failed to send request on gRPC stream for ID: " + input.getProductId(), e);
            inflightRequests.remove(requestKey); // Clean up state
            // TODO: Error handling - dead-letter queue, emit error event, etc.
            ProductDataEvent errorOutput = input;
            errorOutput.setEnrichmentError("Failed to send gRPC stream request: " + e.getMessage());
            out.collect(errorOutput);
            // Potentially re-initialize stream if it's broken
            // initializeGrpcStream(); 
        }
        // Note: The actual enriched output is collected when the response arrives in the responseObserver.onNext()
        // This function, as a ProcessFunction, might not directly collect in processElement for bi-di streams
        // unless it's a request-reply pattern *within* the stream handling.
        // The current skeleton responseObserver.onNext() logs, but needs a mechanism to output to Flink's stream.
        // This might involve using a side output, or having the responseObserver directly use a Collector (if safe).
        // A common pattern is to use an internal queue and process it in onTimer or another method.
    }
    
    // onTimer could be used for timeouts on inflightRequests or batching sends from sendQueue.

    @Override
    public void close() throws Exception {
        logger.info("Closing GrpcStreamingEnrichmentFunction.");
        if (requestObserver != null) {
            try {
                requestObserver.onCompleted(); // Signal server that client is done sending
            } catch (Exception e) {
                logger.log(Level.WARNING, "Exception while completing client request stream.", e);
            }
        }
        if (productServiceClient != null) {
            productServiceClient.close();
        }
        if (grpcClientManager != null) {
            GrpcClientManager.GrpcClientConfig config = new GrpcClientManager.GrpcClientConfig(serviceHost, servicePort, useTls);
            grpcClientManager.shutdownChannel(config);
        }
        super.close();
    }
}
