package com.goodrx.flink.enrichment.model;

import java.util.HashMap;
import java.util.Map;

// Interfaces defined in GrpcProductLookupFunction, reproduced here for clarity
// or could be top-level interfaces in this package.

interface InputEvent {
    int getProductId();
    // Potentially other common fields like eventId, timestamp
    String getEventId();
    long getEventTimestamp();
}

interface EnrichedEvent extends InputEvent {
    void setProductName(String name);
    void setProductDescription(String description);
    void setProductPrice(double price);
    void setProductCategory(String category);
    void setProductAttributes(Map<String, String> attributes);
    void setEnrichmentError(String error);
    boolean hasEnrichmentError();
    String getEnrichmentError();
}

public class ProductDataEvent implements EnrichedEvent {
    private String eventId;
    private long eventTimestamp;
    private int productId;
    private String originalPayload; // Example of other data carried by the event

    // Enriched fields
    private String productName;
    private String productDescription;
    private double productPrice;
    private String productCategory;
    private Map<String, String> productAttributes;
    private String enrichmentError;

    // Constructors
    public ProductDataEvent() {
        this.productAttributes = new HashMap<>();
    }

    public ProductDataEvent(String eventId, long eventTimestamp, int productId, String originalPayload) {
        this.eventId = eventId;
        this.eventTimestamp = eventTimestamp;
        this.productId = productId;
        this.originalPayload = originalPayload;
        this.productAttributes = new HashMap<>();
    }

    // Getters for InputEvent
    @Override
    public String getEventId() { return eventId; }
    @Override
    public long getEventTimestamp() { return eventTimestamp; }
    @Override
    public int getProductId() { return productId; }

    // Setters for InputEvent (if mutable)
    public void setEventId(String eventId) { this.eventId = eventId; }
    public void setEventTimestamp(long eventTimestamp) { this.eventTimestamp = eventTimestamp; }
    public void setProductId(int productId) { this.productId = productId; }


    public String getOriginalPayload() { return originalPayload; }
    public void setOriginalPayload(String originalPayload) { this.originalPayload = originalPayload; }

    // Getters and Setters for EnrichedEvent
    @Override
    public void setProductName(String name) { this.productName = name; }
    public String getProductName() { return productName; }

    @Override
    public void setProductDescription(String description) { this.productDescription = description; }
    public String getProductDescription() { return productDescription; }

    @Override
    public void setProductPrice(double price) { this.productPrice = price; }
    public double getProductPrice() { return productPrice; }

    @Override
    public void setProductCategory(String category) { this.productCategory = category; }
    public String getProductCategory() { return productCategory; }

    @Override
    public void setProductAttributes(Map<String, String> attributes) { this.productAttributes = attributes; }
    public Map<String, String> getProductAttributes() { return productAttributes; }

    @Override
    public void setEnrichmentError(String error) { this.enrichmentError = error; }
    @Override
    public String getEnrichmentError() { return enrichmentError; }
    @Override
    public boolean hasEnrichmentError() { return this.enrichmentError != null && !this.enrichmentError.isEmpty(); }

    @Override
    public String toString() {
        return "ProductDataEvent{" +
                "eventId='" + eventId + '\'' +
                ", eventTimestamp=" + eventTimestamp +
                ", productId=" + productId +
                ", originalPayload='" + originalPayload + '\'' +
                ", productName='" + productName + '\'' +
                ", productDescription='" + productDescription + '\'' +
                ", productPrice=" + productPrice +
                ", productCategory='" + productCategory + '\'' +
                ", productAttributes=" + productAttributes +
                ", enrichmentError='" + enrichmentError + '\'' +
                '}';
    }
}
