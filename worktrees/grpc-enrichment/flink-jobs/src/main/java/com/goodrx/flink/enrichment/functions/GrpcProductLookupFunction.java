package com.goodrx.flink.enrichment.functions;

import com.goodrx.flink.grpc.ProductResponse;
import com.goodrx.flink.grpc.client.GrpcClientManager;
import com.goodrx.flink.grpc.client.ProductServiceClient;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.async.ResultFuture;
import org.apache.flink.streaming.api.functions.async.RichAsyncFunction;

import java.util.Collections;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Supplier;
import java.util.logging.Level;
import java.util.logging.Logger;

import com.goodrx.flink.enrichment.model.ProductDataEvent;


// TODO: Implement caching for frequently accessed products (e.g., Guava Cache, Caffeine)
// TODO: Implement fallback mechanisms (e.g., return default value, flag for later processing)
// TODO: Make gRPC target host/port, timeouts, TLS configurable (e.g., via Flink parameters)

/**
 * Flink RichAsyncFunction for enriching events with product data using a gRPC ProductService.
 * Assumes InputEvent has a method to get a product ID, and EnrichedEvent can store product details.
 */
public class GrpcProductLookupFunction extends RichAsyncFunction<ProductDataEvent, ProductDataEvent> {

    private static final Logger logger = Logger.getLogger(GrpcProductLookupFunction.class.getName());

    private transient GrpcClientManager grpcClientManager;
    private transient ProductServiceClient productServiceClient;
    private transient ExecutorService executorService; // For managing futures from gRPC client

    // Configuration values (could be passed via constructor or Flink parameters)
    private final String productServiceHost;
    private final int productServicePort;
    private final boolean useTls;
    private final long grpcTimeoutSeconds;

    public GrpcProductLookupFunction(String productServiceHost, int productServicePort, boolean useTls, long grpcTimeoutSeconds) {
        this.productServiceHost = productServiceHost;
        this.productServicePort = productServicePort;
        this.useTls = useTls;
        this.grpcTimeoutSeconds = grpcTimeoutSeconds;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        GrpcClientManager.GrpcClientConfig clientConfig = new GrpcClientManager.GrpcClientConfig(
                productServiceHost, productServicePort, useTls
        );
        // Using a shared GrpcClientManager instance might be better if managed globally,
        // but for a self-contained function, creating one here is simpler.
        // Consider how GrpcClientManager lifecycle is best managed in a larger Flink app.
        grpcClientManager = new GrpcClientManager(clientConfig); // Default config for the manager is also the specific one here
        productServiceClient = new ProductServiceClient(grpcClientManager, clientConfig, grpcTimeoutSeconds);
        
        // Executor for handling gRPC future callbacks
        // The size should be tuned based on expected parallelism and external service latency
        int poolSize = getRuntimeContext().getNumberOfParallelSubtasks(); // As a starting point
        executorService = Executors.newFixedThreadPool(poolSize);
        logger.info("GrpcProductLookupFunction opened. ProductService target: " + productServiceHost + ":" + productServicePort);
    }

    @Override
    public void close() throws Exception {
        logger.info("Closing GrpcProductLookupFunction.");
        if (productServiceClient != null) {
            productServiceClient.close(); // Client close is mostly symbolic as channel is manager's
        }
        if (grpcClientManager != null) {
            // This will shut down the channel if this function instance was the only user.
            // If the manager/channel is shared, this might be too aggressive or might be handled elsewhere.
            GrpcClientManager.GrpcClientConfig config = new GrpcClientManager.GrpcClientConfig(productServiceHost, productServicePort, useTls);
            grpcClientManager.shutdownChannel(config);
            // Or grpcClientManager.shutdownAll() if this function "owns" the manager.
        }
        if (executorService != null) {
            executorService.shutdown();
        }
        super.close();
    }

    @Override
    public void asyncInvoke(ProductDataEvent input, ResultFuture<ProductDataEvent> resultFuture) throws Exception {
        final int productId = input.getProductId();
        
        // For now, assuming EnrichedEvent can be instantiated from InputEvent
        // In a real scenario, you might have a factory or copy constructor
        final ProductDataEvent output = input; // Input is already the output object, to be enriched

        CompletableFuture.supplyAsync(() -> {
            try {
                return productServiceClient.getProduct(productId);
            } catch (Exception e) {
                logger.log(Level.WARNING, "Failed to get product data for ID: " + productId, e);
                // TODO: Implement fallback logic here
                // For now, just rethrow as a runtime exception to be caught by the outer layer
                throw new RuntimeException("gRPC call failed for product ID " + productId, e);
            }
        }, executorService).thenAcceptAsync(productResponse -> {
            if (productResponse != null) {
                output.setProductName(productResponse.getName());
                output.setProductDescription(productResponse.getDescription());
                output.setProductPrice(productResponse.getPrice());
                output.setProductCategory(productResponse.getCategory());
                // TODO: Handle attributes map
                logger.log(Level.FINE, "Successfully enriched product ID: {0}", productId);
            } else {
                // This case might occur if getProduct can return null (not typical for gRPC unless error handled that way)
                // Or if fallback logic above returned null.
                logger.log(Level.WARNING, "Product data not found or fallback for ID: {0}", productId);
                output.setEnrichmentError("Product not found or lookup failed for ID: " + productId);
            }
            resultFuture.complete(Collections.singleton(output));
        }, executorService).exceptionally(throwable -> {
            logger.log(Level.SEVERE, "Exception in async product lookup for ID: " + productId, throwable);
            output.setEnrichmentError("Exception during product lookup: " + throwable.getMessage());
            // TODO: Implement more sophisticated error handling / dead-letter queue
            resultFuture.complete(Collections.singleton(output)); // Complete with error info
            return null;
        });
    }

    @Override
    public void timeout(ProductDataEvent input, ResultFuture<ProductDataEvent> resultFuture) throws Exception {
        logger.log(Level.WARNING, "Async lookup timeout for product ID: {0}", input.getProductId());
        ProductDataEvent output = input; // Input is already the output object
        output.setEnrichmentError("Timeout during product lookup for ID: " + input.getProductId());
        // TODO: Implement fallback or specific timeout handling
        resultFuture.complete(Collections.singleton(output));
    }
}
