package com.goodrx.flink.enrichment.functions;

import com.goodrx.flink.grpc.CustomerResponse;
import com.goodrx.flink.grpc.client.GrpcClientManager;
import com.goodrx.flink.grpc.client.CustomerServiceClient;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.async.ResultFuture;
import org.apache.flink.streaming.api.functions.async.RichAsyncFunction;

import java.util.Collections;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.logging.Level;
import java.util.logging.Logger;

import com.goodrx.flink.enrichment.model.CustomerDataEvent;

// TODO: Implement caching for frequently accessed customers
// TODO: Implement fallback mechanisms
// TODO: Make gRPC target host/port, timeouts, TLS configurable

/**
 * Flink RichAsyncFunction for enriching events with customer data using a gRPC CustomerService.
 */
public class GrpcCustomerLookupFunction extends RichAsyncFunction<CustomerDataEvent, CustomerDataEvent> {

    private static final Logger logger = Logger.getLogger(GrpcCustomerLookupFunction.class.getName());

    private transient GrpcClientManager grpcClientManager;
    private transient CustomerServiceClient customerServiceClient;
    private transient ExecutorService executorService;

    private final String customerServiceHost;
    private final int customerServicePort;
    private final boolean useTls;
    private final long grpcTimeoutSeconds;

    public GrpcCustomerLookupFunction(String customerServiceHost, int customerServicePort, boolean useTls, long grpcTimeoutSeconds) {
        this.customerServiceHost = customerServiceHost;
        this.customerServicePort = customerServicePort;
        this.useTls = useTls;
        this.grpcTimeoutSeconds = grpcTimeoutSeconds;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        GrpcClientManager.GrpcClientConfig clientConfig = new GrpcClientManager.GrpcClientConfig(
                customerServiceHost, customerServicePort, useTls
        );
        grpcClientManager = new GrpcClientManager(clientConfig);
        customerServiceClient = new CustomerServiceClient(grpcClientManager, clientConfig, grpcTimeoutSeconds);
        
        int poolSize = getRuntimeContext().getNumberOfParallelSubtasks();
        executorService = Executors.newFixedThreadPool(poolSize);
        logger.info("GrpcCustomerLookupFunction opened. CustomerService target: " + customerServiceHost + ":" + customerServicePort);
    }

    @Override
    public void close() throws Exception {
        logger.info("Closing GrpcCustomerLookupFunction.");
        if (customerServiceClient != null) {
            customerServiceClient.close();
        }
        if (grpcClientManager != null) {
            GrpcClientManager.GrpcClientConfig config = new GrpcClientManager.GrpcClientConfig(customerServiceHost, customerServicePort, useTls);
            grpcClientManager.shutdownChannel(config);
        }
        if (executorService != null) {
            executorService.shutdown();
        }
        super.close();
    }

    @Override
    public void asyncInvoke(CustomerDataEvent input, ResultFuture<CustomerDataEvent> resultFuture) throws Exception {
        final int customerId = input.getCustomerId();
        final CustomerDataEvent output = input; // Input is already the output object, to be enriched

        CompletableFuture.supplyAsync(() -> {
            try {
                return customerServiceClient.getCustomer(customerId);
            } catch (Exception e) {
                logger.log(Level.WARNING, "Failed to get customer data for ID: " + customerId, e);
                throw new RuntimeException("gRPC call failed for customer ID " + customerId, e);
            }
        }, executorService).thenAcceptAsync(customerResponse -> {
            if (customerResponse != null) {
                output.setCustomerFirstName(customerResponse.getFirstName());
                output.setCustomerLastName(customerResponse.getLastName());
                output.setCustomerEmail(customerResponse.getEmail());
                // TODO: Handle addresses list from customerResponse.getAddressesList()
                logger.log(Level.FINE, "Successfully enriched customer ID: {0}", customerId);
            } else {
                logger.log(Level.WARNING, "Customer data not found or fallback for ID: {0}", customerId);
                output.setCustomerEnrichmentError("Customer not found or lookup failed for ID: " + customerId);
            }
            resultFuture.complete(Collections.singleton(output));
        }, executorService).exceptionally(throwable -> {
            logger.log(Level.SEVERE, "Exception in async customer lookup for ID: " + customerId, throwable);
            output.setCustomerEnrichmentError("Exception during customer lookup: " + throwable.getMessage());
            resultFuture.complete(Collections.singleton(output));
            return null;
        });
    }

    @Override
    public void timeout(CustomerDataEvent input, ResultFuture<CustomerDataEvent> resultFuture) throws Exception {
        logger.log(Level.WARNING, "Async lookup timeout for customer ID: {0}", input.getCustomerId());
        CustomerDataEvent output = input; // Input is already the output object
        output.setCustomerEnrichmentError("Timeout during customer lookup for ID: " + input.getCustomerId());
        resultFuture.complete(Collections.singleton(output));
    }
}
