package com.goodrx.flink.enrichment;

import org.apache.flink.api.java.utils.ParameterTool;
import org.apache.flink.streaming.api.CheckpointingMode;
import org.apache.flink.streaming.api.environment.CheckpointConfig;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.connector.kafka.source.reader.deserializer.KafkaRecordDeserializationSchema;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import com.goodrx.flink.enrichment.model.OrderEvent;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.streaming.api.datastream.SingleOutputStreamOperator;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.apache.flink.util.OutputTag;
import com.goodrx.flink.grpc.client.GrpcClientManager;
import com.goodrx.flink.enrichment.functions.ProductEnrichmentFunction;
import com.goodrx.flink.enrichment.model.EnrichedOrderEvent;
import org.apache.flink.streaming.api.datastream.AsyncDataStream;
import java.util.concurrent.TimeUnit;
import java.util.List;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.flink.formats.json.JsonSerializationSchema;
import org.apache.flink.connector.base.DeliveryGuarantee;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;
import java.util.concurrent.ConcurrentHashMap;

/**
 * A Flink streaming job that enriches incoming order events with additional information
 * by making asynchronous calls to external gRPC services (e.g., Product Service, Customer Service).
 * 
 * <p>The job performs the following key steps:
 * <ol>
 *   <li><b>Consumes</b> raw order messages (expected as JSON strings) from a Kafka topic.</li>
 *   <li><b>Parses</b> these JSON strings into {@link com.goodrx.flink.enrichment.model.OrderEvent} objects.
 *       Malformed records are routed to a Dead Letter Queue (DLQ) side output.</li>
 *   <li><b>Assigns</b> timestamps and watermarks to the stream of valid OrderEvents for event time processing.</li>
 *   <li><b>Enriches</b> each OrderEvent by asynchronously calling a Product gRPC service to fetch product details.
 *       This step uses {@link org.apache.flink.streaming.api.datastream.AsyncDataStream#unorderedWait} for efficient
 *       non-blocking I/O with the external service. The result is an {@link com.goodrx.flink.enrichment.model.EnrichedOrderEvent}.</li>
 *   <li>(Future) Further enrichment steps (e.g., customer details) can be added in a similar fashion.</li>
 *   <li><b>Sinks</b> the final enriched order events to an output Kafka topic.</li>
 * </ol>
 *
 * <p><b>Configuration:</b>
 * The job is configured using Flink's {@link org.apache.flink.api.java.utils.ParameterTool}.
 * Parameters can be passed via command-line arguments or a properties file.
 * Key configurable parameters include Kafka broker details, topic names, gRPC service endpoints,
 * checkpointing settings, and job parallelism.
 * An example properties file (e.g., {@code config-dev.properties}) might look like:
 * <pre>{@code
 * job.name=OrderEnrichmentGrpc-Dev
 * job.parallelism=2
 * checkpoint.interval.ms=60000
 * kafka.source.bootstrap.servers=localhost:9092
 * kafka.source.topic=raw_orders
 * kafka.sink.topic=enriched_orders
 * product.service.host=localhost
 * product.service.port=50051
 * product.service.use.tls=false
 * ...
 * }</pre>
 *
 * <p><b>Execution Example (conceptual):</b>
 * <pre>{@code
 * ./bin/flink run -c com.goodrx.flink.enrichment.OrderEnrichmentJob flink-jobs.jar --propsFile /path/to/config-dev.properties
 * }</pre>
 *
 * <p>The job utilizes {@link com.goodrx.flink.grpc.client.GrpcClientManager} for managing gRPC channels,
 * although in the current UDF ({@link com.goodrx.flink.enrichment.functions.ProductEnrichmentFunction}),
 * the client manager and service client are instantiated per parallel UDF instance.
 */
public class OrderGrpcEnrichmentJob {

    private static final Logger LOG = LoggerFactory.getLogger(OrderGrpcEnrichmentJob.class);
    private static final String MALFORMED_RECORDS_TAG_NAME = "malformed-json-orders";
    private static final String SINK_NAME = "enrichedOrderEventSink"; // Key for SINK_REGISTRY
    
    // For integration tests
    public static List<EnrichedOrderEvent> TEST_RESULTS = new java.util.concurrent.CopyOnWriteArrayList<>();
    public static volatile org.apache.flink.core.execution.JobClient runningTestJobClient = null;
    
    // Sink registry for integration testing - allows tests to register custom sinks
    private static final ConcurrentHashMap<String, SinkFunction<?>> SINK_REGISTRY = new ConcurrentHashMap<>();
    
    /**
     * Registers a sink function to be used by the job for a specific data type.
     * This method is used primarily for integration testing to allow tests to provide custom sinks.
     * 
     * @param name The name/key of the sink to register
     * @param sinkFunction The sink function to register
     */
    public static <T> void registerSink(String name, SinkFunction<T> sinkFunction) {
        SINK_REGISTRY.put(name, sinkFunction);
        LOG.info("Registered custom sink with name: {}", name);
    }
    
    /**
     * Retrieves a registered sink function by name.
     * 
     * @param name The name/key of the sink to retrieve
     * @return The registered sink function, or null if none exists with that name
     */
    @SuppressWarnings("unchecked")
    public static <T> SinkFunction<T> getSink(String name) {
        return (SinkFunction<T>) SINK_REGISTRY.get(name);
    }
    
    /**
     * Clears all registered sinks.
     */
    public static void clearSinks() {
        SINK_REGISTRY.clear();
        LOG.info("Cleared all registered sinks");
    }

    // ProcessFunction to parse JSON and handle errors
    public static class JsonToOrderEventParser extends ProcessFunction<String, OrderEvent> {
        private transient ObjectMapper objectMapper;
        private final OutputTag<String> malformedRecordsOutputTag;

        public JsonToOrderEventParser(OutputTag<String> malformedRecordsOutputTag) {
            this.malformedRecordsOutputTag = malformedRecordsOutputTag;
        }

        @Override
        public void open(org.apache.flink.configuration.Configuration parameters) throws Exception {
            super.open(parameters);
            objectMapper = new ObjectMapper();
        }

        @Override
        public void processElement(String value, Context ctx, Collector<OrderEvent> out) throws Exception {
            try {
                OrderEvent orderEvent = objectMapper.readValue(value, OrderEvent.class);
                out.collect(orderEvent);
            } catch (Exception e) {
                LOG.warn("Failed to parse JSON to OrderEvent: {}. Record: {}", e.getMessage(), value);
                ctx.output(malformedRecordsOutputTag, value); // Send malformed record to side output
            }
        }
    }

    public static void main(String[] args) throws Exception {
        LOG.info("Initializing Order Enrichment Flink Job...");

        ParameterTool params = initializeParameters(args);
        StreamExecutionEnvironment env = createExecutionEnvironment();
        configureEnvironment(env, params);

        final OutputTag<String> malformedRecordsTag = new OutputTag<String>(MALFORMED_RECORDS_TAG_NAME){};

        DataStream<String> rawOrderStream = createRawOrderStream(env, params);
        SingleOutputStreamOperator<OrderEvent> parsedOrderStream = processRawOrderStream(rawOrderStream, malformedRecordsTag);
        DataStream<OrderEvent> orderEventStreamWithWatermarks = assignWatermarks(parsedOrderStream);
        handleMalformedRecords(parsedOrderStream, malformedRecordsTag);

        DataStream<EnrichedOrderEvent> productEnrichedStream = enrichWithProductData(orderEventStreamWithWatermarks, params);
        sinkEnrichedOrdersToKafka(productEnrichedStream, params);

        // Instantiate GrpcClientManager for potential shutdown hook (UDFs manage their own clients)
        GrpcClientManager grpcClientManager = new GrpcClientManager(null);
        LOG.info("GrpcClientManager instantiated for potential shutdown hook.");
        // Consider adding JVM shutdown hook for grpcClientManager.shutdownAll() if appropriate for the deployment.
        // Runtime.getRuntime().addShutdownHook(new Thread(grpcClientManager::shutdownAll));

        executeFlinkJob(env, params);
    }

    /**
     * Initializes and returns a {@link ParameterTool} from command-line arguments.
     * Optionally, it can be extended to merge parameters from a properties file if specified.
     * <p>
     * Example: If {@code --propsFile /path/to/config.properties} is passed, those properties are loaded.
     * </p>
     *
     * @param args Command-line arguments passed to the main method.
     * @return A {@link ParameterTool} instance loaded with the provided arguments.
     */
    private static ParameterTool initializeParameters(String[] args) {
        ParameterTool params = ParameterTool.fromArgs(args);
        // Example: Load from a properties file if specified
        // if (params.has("propsFile")) {
        //     try {
        //         params = ParameterTool.fromPropertiesFile(params.getRequired("propsFile")).mergeWith(params);
        //     } catch (java.io.IOException e) {
        //         LOG.error("Failed to load properties file: {}", params.getRequired("propsFile"), e);
        //         // Decide if to throw an exception or proceed with defaults/CLI params
        //     }
        // }
        return params;
    }

    /**
     * Creates and returns a new {@link StreamExecutionEnvironment}.
     * This is the foundational context for a Flink streaming application.
     *
     * @return A new {@link StreamExecutionEnvironment} instance.
     */
    private static StreamExecutionEnvironment createExecutionEnvironment() {
        return StreamExecutionEnvironment.getExecutionEnvironment();
    }

    /**
     * Configures the Flink {@link StreamExecutionEnvironment}, including checkpointing and parallelism,
     * based on values from the provided {@link ParameterTool}.
     * <p>
     * Checkpointing configuration includes interval, mode (EXACTLY_ONCE), pause between checkpoints,
     * timeout, concurrent checkpoints, and cleanup behavior. Parallelism is set if specified in parameters.
     * </p>
     *
     * @param env The {@link StreamExecutionEnvironment} to configure.
     * @param params The {@link ParameterTool} containing configuration values (e.g., {@code checkpoint.interval.ms}, {@code job.parallelism}).
     */
    private static void configureEnvironment(StreamExecutionEnvironment env, ParameterTool params) {
        // Configure checkpointing
        long checkpointInterval = params.getLong("checkpoint.interval.ms", 60000); // Default to 60 seconds
        if (checkpointInterval > 0) {
            LOG.info("Enabling checkpointing with interval: {} ms", checkpointInterval);
            env.enableCheckpointing(checkpointInterval);
            CheckpointConfig checkpointConfig = env.getCheckpointConfig();
            checkpointConfig.setCheckpointingMode(CheckpointingMode.EXACTLY_ONCE);
            checkpointConfig.setMinPauseBetweenCheckpoints(params.getLong("checkpoint.min.pause.ms", 30000));
            checkpointConfig.setCheckpointTimeout(params.getLong("checkpoint.timeout.ms", 120000));
            checkpointConfig.setMaxConcurrentCheckpoints(params.getInt("checkpoint.max.concurrent", 1));
            checkpointConfig.setExternalizedCheckpointCleanup(CheckpointConfig.ExternalizedCheckpointCleanup.RETAIN_ON_CANCELLATION);
            // Consider RocksDBStateBackend for production
            // String checkpointDir = params.get("checkpoint.dir");
            // if (checkpointDir != null && !checkpointDir.isEmpty()) {
            //     LOG.info("Using RocksDBStateBackend with checkpoint directory: {}", checkpointDir);
            //     try {
            //         env.setStateBackend(new org.apache.flink.contrib.streaming.state.RocksDBStateBackend(checkpointDir, true));
            //     } catch (java.io.IOException e) {
            //         LOG.error("Failed to initialize RocksDBStateBackend at {}: {}", checkpointDir, e.getMessage(), e);
            //     }
            // } else {
            //     LOG.warn("Checkpoint directory not specified. Using default state backend.");
            // }
        } else {
            LOG.warn("Checkpointing is disabled (checkpoint.interval.ms <= 0).");
        }

        // Set parallelism
        if (params.has("job.parallelism")) {
            env.setParallelism(params.getInt("job.parallelism"));
            LOG.info("Job parallelism set to: {}", env.getParallelism());
        }
    }

    /**
     * Creates a {@link DataStream} of raw order messages (as strings) from a Kafka source.
     * Configuration for the Kafka source (bootstrap servers, topic, group ID, starting offsets)
     * is read from the provided {@link ParameterTool}.
     * <p>
     * Example Kafka parameters: {@code kafka.source.bootstrap.servers}, {@code kafka.source.topic}.
     * </p>
     *
     * @param env The {@link StreamExecutionEnvironment} to create the source in.
     * @param params The {@link ParameterTool} containing Kafka configuration parameters.
     * @return A {@link DataStream} of raw order messages as strings.
     */
    private static DataStream<String> createRawOrderStream(StreamExecutionEnvironment env, ParameterTool params) {
        String bootstrapServers = params.getRequired("kafka.source.bootstrap.servers");
        String topic = params.getRequired("kafka.source.topic");
        String groupId = params.get("kafka.source.group.id", "order-enrichment-grpc-consumer");

        LOG.info("Creating Kafka source with bootstrap servers: {}, topic: {}, group ID: {}",
                 bootstrapServers, topic, groupId);

        KafkaSource<String> kafkaSource = KafkaSource.<String>builder()
                .setBootstrapServers(bootstrapServers)
                .setTopics(topic)
                .setGroupId(groupId)
                .setDeserializer(KafkaRecordDeserializationSchema.valueOnly(new SimpleStringSchema()))
                .setStartingOffsets(OffsetsInitializer.latest()) // Or earliest(), or specific offsets
                .build();

        return env.fromSource(kafkaSource, WatermarkStrategy.noWatermarks(), "Raw Kafka Order Source");
    }

    /**
     * Processes a {@link DataStream} of raw JSON strings, parsing them into {@link OrderEvent} objects.
     * Uses the {@link JsonToOrderEventParser} which routes malformed JSON records to a side output stream
     * identified by the provided {@link OutputTag}.
     * <p>
     * Input: A stream of JSON strings like {@code {"orderId": "123", ...}}.
     * Output: A primary stream of {@link OrderEvent} objects and a side output for unparseable strings.
     * </p>
     *
     * @param rawOrderStream The input {@link DataStream} of raw JSON strings.
     * @param malformedRecordsTag The {@link OutputTag} used to identify the side output for malformed records.
     * @return A {@link SingleOutputStreamOperator} containing parsed {@link OrderEvent} objects, with a side output for errors.
     */
    private static SingleOutputStreamOperator<OrderEvent> processRawOrderStream(DataStream<String> rawOrderStream, OutputTag<String> malformedRecordsTag) {
        return rawOrderStream
                .process(new JsonToOrderEventParser(malformedRecordsTag))
                .name("JSON Parser and DLQ Router");
    }

    /**
     * Assigns timestamps and watermarks to a stream of {@link OrderEvent} objects.
     * This implementation uses a {@link WatermarkStrategy#forMonotonousTimestamps()} based on the event's timestamp.
     * <p>
     * Input: A stream of {@link OrderEvent}s, e.g., after parsing.
     * Output: The same stream of {@link OrderEvent}s, but now with timestamps and watermarks assigned.
     * </p>
     *
     * @param parsedOrderStream The input {@link SingleOutputStreamOperator} of {@link OrderEvent} objects.
     * @return A {@link DataStream} of {@link OrderEvent} objects with watermarks.
     */
    private static DataStream<OrderEvent> assignWatermarks(SingleOutputStreamOperator<OrderEvent> parsedOrderStream) {
        WatermarkStrategy<OrderEvent> watermarkStrategy = WatermarkStrategy
                .<OrderEvent>forMonotonousTimestamps()
                .withTimestampAssigner((event, timestamp) -> event.getEventTimestamp());

        return parsedOrderStream
                .assignTimestampsAndWatermarks(watermarkStrategy)
                .name("Parsed Order Events with Watermarks");
    }

    /**
     * Handles malformed records from the side output of the JSON parsing step.
     * Currently, it logs these records by printing them to standard output. In a production scenario,
     * these records would typically be sent to a Dead Letter Queue (DLQ) Kafka topic or another error handling mechanism.
     * <p>
     * Example: A malformed JSON string like {@code "not a json"} would be routed here.
     * </p>
     *
     * @param parsedOrderStream The {@link SingleOutputStreamOperator} from which the side output is extracted.
     * @param malformedRecordsTag The {@link OutputTag} identifying the side output stream of malformed records.
     */
    private static void handleMalformedRecords(SingleOutputStreamOperator<OrderEvent> parsedOrderStream, OutputTag<String> malformedRecordsTag) {
        DataStream<String> malformedStream = parsedOrderStream.getSideOutput(malformedRecordsTag);
        malformedStream.map(record -> "Malformed Record: " + record)
                       .name("Malformed Records Logger")
                       .print(); // Placeholder: print to logs. In prod, send to DLQ sink.
        LOG.info("Malformed records handling (DLQ logging) defined.");
    }

    /**
     * Enriches a stream of {@link OrderEvent} objects with product data by making asynchronous calls
     * to an external Product gRPC service using the {@link ProductEnrichmentFunction}.
     * The enrichment is performed using {@link AsyncDataStream#unorderedWait}.
     * <p>
     * Configuration for the gRPC service (host, port, TLS) and async operator (timeout, capacity)
     * is read from the provided {@link ParameterTool}.
     * </p>
     *
     * @param orderEventStream The input {@link DataStream} of {@link OrderEvent} objects to enrich.
     * @param params The {@link ParameterTool} containing gRPC service configuration.
     * @return A {@link DataStream} of {@link EnrichedOrderEvent} objects with product information.
     */
    private static DataStream<EnrichedOrderEvent> enrichWithProductData(DataStream<OrderEvent> orderEventStream, ParameterTool params) {
        String productServiceHost = params.get("product.service.host", "localhost");
        int productServicePort = params.getInt("product.service.port", 50051);
        boolean productServiceUseTls = params.getBoolean("product.service.use.tls", false);
        long asyncTimeoutMs = params.getLong("async.timeout.ms", 5000);
        int asyncCapacity = params.getInt("async.capacity", 100);

        LOG.info("Configuring product enrichment with gRPC service at {}:{} (TLS: {}), timeout: {}ms, capacity: {}",
                 productServiceHost, productServicePort, productServiceUseTls, asyncTimeoutMs, asyncCapacity);

        ProductEnrichmentFunction productEnrichmentFunction = new ProductEnrichmentFunction(
                productServiceHost, productServicePort, productServiceUseTls);

        return AsyncDataStream.unorderedWait(
                orderEventStream,
                productEnrichmentFunction,
                asyncTimeoutMs,
                TimeUnit.MILLISECONDS,
                asyncCapacity
        ).name("Product Enrichment via gRPC");
    }

    /**
     * Sinks enriched order events to a Kafka topic.
     * Configuration for the Kafka sink (bootstrap servers, topic) is read from the provided {@link ParameterTool}.
     * <p>
     * The enriched events are serialized as JSON using {@link JsonSerializationSchema}.
     * </p>
     *
     * @param enrichedOrderStream The input {@link DataStream} of {@link EnrichedOrderEvent} objects to sink.
     * @param params The {@link ParameterTool} containing Kafka sink configuration.
     */
    private static void sinkEnrichedOrdersToKafka(DataStream<EnrichedOrderEvent> enrichedOrderStream, ParameterTool params) {
        // Check if we should use a test sink instead of Kafka
        SinkFunction<EnrichedOrderEvent> testSink = getSink(SINK_NAME);
        if (testSink != null) {
            LOG.info("Using registered test sink for enriched order events");
            enrichedOrderStream.addSink(testSink).name("Test Sink for Enriched Orders");
            return;
        }

        String bootstrapServers = params.getRequired("kafka.sink.bootstrap.servers");
        String topic = params.getRequired("kafka.sink.topic");

        LOG.info("Creating Kafka sink with bootstrap servers: {}, topic: {}", bootstrapServers, topic);

        KafkaSink<EnrichedOrderEvent> kafkaSink = KafkaSink.<EnrichedOrderEvent>builder()
                .setBootstrapServers(bootstrapServers)
                .setRecordSerializer(KafkaRecordSerializationSchema.builder()
                        .setTopic(topic)
                        .setValueSerializationSchema(new JsonSerializationSchema<EnrichedOrderEvent>())
                        .build())
                .setDeliveryGuarantee(DeliveryGuarantee.AT_LEAST_ONCE)
                .build();

        enrichedOrderStream.sinkTo(kafkaSink).name("Enriched Orders Kafka Sink");
    }

    /**
     * Executes the Flink job with a job name retrieved from the {@link ParameterTool}.
     * If no job name is specified in the parameters (key: {@code job.name}), a default name "OrderEnrichmentGrpc" is used.
     *
     * @param env The {@link StreamExecutionEnvironment} containing the defined Flink job.
     * @param params The {@link ParameterTool} used to retrieve the job name.
     * @throws Exception If an error occurs during job execution.
     */
    private static void executeFlinkJob(StreamExecutionEnvironment env, ParameterTool params) throws Exception {
        String jobName = params.get("job.name", "OrderEnrichmentGrpc");
        LOG.info("Executing Flink job: {}", jobName);
        
        // Check for test sink mode
        boolean useTestSink = params.getBoolean("use.test.sink", false);
        
        if (useTestSink) {
            // In test mode, execute async and store the job client for the test to access
            org.apache.flink.core.execution.JobClient client = env.executeAsync("test-job");
            runningTestJobClient = client; // Make client available to the test
            try {
                client.getJobExecutionResult().get(); // Block here until job finishes or is cancelled by test
            } catch (java.util.concurrent.CancellationException | java.lang.InterruptedException e) {
                // Log interruption, it's expected if test cancels the job
                if (e instanceof java.lang.InterruptedException) {
                    LOG.info("Test job execution was cancelled or interrupted as expected.", e);
                } else {
                    LOG.error("Test job execution failed with an unexpected error.", e);
                    throw e;
                }
            }
        } else {
            // Normal execution
            env.execute(jobName);
        }
    }
}
