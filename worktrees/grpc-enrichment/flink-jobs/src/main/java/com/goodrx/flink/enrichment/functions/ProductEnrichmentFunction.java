package com.goodrx.flink.enrichment.functions;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.goodrx.flink.enrichment.model.EnrichedOrderEvent;
import com.goodrx.flink.enrichment.model.OrderEvent;
import com.goodrx.flink.grpc.client.GrpcClientManager;
import com.goodrx.flink.grpc.client.ProductServiceClient;
import com.goodrx.flink.grpc.ProductRequest; // Added for request object
import com.goodrx.flink.grpc.ProductResponse;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.async.ResultFuture;
import org.apache.flink.streaming.api.functions.async.RichAsyncFunction;

import java.util.Collections;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * A Flink {@link RichAsyncFunction} that enriches an incoming {@link OrderEvent}
 * with product details obtained by making an asynchronous call to an external Product gRPC service.
 * <p>
 * This function is designed to be used with Flink's {@link AsyncDataStream#unorderedWait}
 * or {@link AsyncDataStream#orderedWait} operators for efficient, non-blocking I/O operations.
 * </p>
 * <p>
 * For each {@link OrderEvent}, it extracts the {@code productId} and calls the Product gRPC service
 * to fetch the corresponding product name. The original {@link OrderEvent} is then combined with the
 * fetched product name to produce an {@link EnrichedOrderEvent}.
 * </p>
 * <p>
 * The gRPC client and its underlying {@link GrpcClientManager} are initialized in the {@link #open(Configuration)}
 * method and resources are cleaned up in the {@link #close()} method. Each parallel instance of this function
 * maintains its own gRPC client connection.
 * </p>
 * <p><b>Input:</b> {@link OrderEvent} - The raw order event to be enriched.</p>
 * <p><b>Output:</b> {@link EnrichedOrderEvent} - The order event enriched with product information.</p>
 * <p>
 * If the product ID is missing, or if an error or timeout occurs during the gRPC call,
 * the function will output an {@link EnrichedOrderEvent} with a placeholder or error indication
 * for the product name (e.g., "N/A - Missing ProductID", "N/A - Enrichment Error", "N/A - Enrichment Timeout").
 * </p>
 *
 * @param productServiceHost The hostname or IP address of the Product gRPC service.
 * @param productServicePort The port number of the Product gRPC service.
 * @param productServiceUseTls A boolean flag indicating whether to use TLS for the gRPC connection.
 */
public class ProductEnrichmentFunction extends RichAsyncFunction<OrderEvent, EnrichedOrderEvent> {

    private static final Logger LOG = LoggerFactory.getLogger(ProductEnrichmentFunction.class); // Ensure this is not re-declared if already present at class level

    private transient GrpcClientManager grpcClientManager;
    private transient ProductServiceClient productServiceClient;

    private final String productServiceHost;
    private final int productServicePort;
    private final boolean productServiceUseTls;

    public ProductEnrichmentFunction(String host, int port, boolean useTls) {
        this.productServiceHost = host;
        this.productServicePort = port;
        this.productServiceUseTls = useTls;
    }

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        // Each parallel instance of this function will have its own client manager and client.
        // Consider sharing GrpcClientManager if appropriate and thread-safe for your setup.
        grpcClientManager = new GrpcClientManager(null); // No default config
        GrpcClientManager.GrpcClientConfig clientConfig = new GrpcClientManager.GrpcClientConfig(
                productServiceHost, productServicePort, productServiceUseTls);
        // Assuming a default request timeout, e.g., 5000ms. This could also be made configurable.
        long defaultRequestTimeoutMs = 5000L;
        productServiceClient = new ProductServiceClient(grpcClientManager, clientConfig, defaultRequestTimeoutMs);
        LOG.info("ProductEnrichmentFunction opened. ProductService client initialized for {}:{}{} with timeout {}ms", 
                 productServiceHost, productServicePort, productServiceUseTls ? " (TLS)" : "", defaultRequestTimeoutMs);
    }

    @Override
    public void close() throws Exception {
        if (productServiceClient != null) {
            // productServiceClient.close(); // Assuming ProductServiceClient has a close method
        }
        if (grpcClientManager != null) {
            LOG.info("Closing GrpcClientManager in ProductEnrichmentFunction.");
            grpcClientManager.shutdownAll(); // Shuts down channels created by this manager instance
            // Wait for termination, similar to GrpcClientManagerTest
            // This might block the task manager's shutdown thread if not handled carefully.
            // Consider if awaitTermination is critical here or if shutdownNow is sufficient.
        }
        super.close();
    }

    @Override
    public void asyncInvoke(OrderEvent input, ResultFuture<EnrichedOrderEvent> resultFuture) { // Removed throws Exception as CompletableFuture handles it
        final String orderId = input.getOrderId(); // For logging
        final String productId = input.getProductId();
        LOG.info("[Order: {}] AsyncInvoke started for Product ID: {}", orderId, productId);

        if (productId == null || productId.isEmpty()) {
            LOG.warn("[Order: {}] Product ID is null or empty. Skipping enrichment.", orderId);
            resultFuture.complete(Collections.singleton(new EnrichedOrderEvent(input, "N/A - Missing ProductID")));
            return;
        }

        LOG.info("[Order: {}] Attempting to parse Product ID: '{}' to int", orderId, productId);
        int intProductId;
        try {
            intProductId = Integer.parseInt(productId);
        } catch (NumberFormatException e) {
            LOG.error("[Order: {}] Invalid Product ID format: '{}'. Must be an integer.", orderId, productId, e);
            resultFuture.complete(Collections.singleton(new EnrichedOrderEvent(input, "N/A - Invalid ProductID Format")));
            return;
        }

        ProductRequest grpcRequest = ProductRequest.newBuilder().setProductId(intProductId).build();
        // Get the ListenableFuture from the gRPC client's futureStub
        ListenableFuture<ProductResponse> listenableFuture = productServiceClient.getFutureStub().getProduct(grpcRequest);
        
        // Convert ListenableFuture to CompletableFuture
        CompletableFuture<ProductResponse> future = CompletableFuture.supplyAsync(() -> {
            try {
                // Block and get the result from ListenableFuture, respecting a timeout
                // This is executed in the CompletableFuture's thread pool, not Flink's task thread
                return listenableFuture.get(productServiceClient.getDefaultTimeoutSec(), TimeUnit.SECONDS);
            } catch (Exception e) {
                 // Log and rethrow to be caught by whenComplete
                LOG.error("[Order: {}] Exception while getting result from ListenableFuture for Product ID {}: {}", orderId, intProductId, e.getMessage(), e);
                throw new RuntimeException(e); // This will trigger the 'throwable' in whenComplete
            }
        });

        future.whenComplete((response, throwable) -> {
            if (throwable != null) {
                LOG.error("[Order: {}] Failed to enrich Product ID {}: {}.", orderId, productId, throwable.getMessage(), throwable);
                resultFuture.complete(Collections.singleton(new EnrichedOrderEvent(input, "N/A - Enrichment Error")));
            } else {
                if (response == null || !response.isInitialized()) {
                    LOG.warn("[Order: {}] Received null or uninitialized ProductResponse for Product ID: {}. Product Name: N/A", orderId, productId);
                    resultFuture.complete(Collections.singleton(new EnrichedOrderEvent(input, "N/A - Product Not Found")));
                } else {
                    String productName = response.getName();
                    LOG.info("[Order: {}] Successfully enriched Product ID {}. Product Name: '{}'", orderId, productId, productName);
                    resultFuture.complete(Collections.singleton(new EnrichedOrderEvent(input, productName)));
                }
            }
        });
    }

    @Override
    public void timeout(OrderEvent input, ResultFuture<EnrichedOrderEvent> resultFuture) {
        LOG.warn("[Order: {}] Async operation timed out for Product ID: {}. Emitting with error indication.", input.getOrderId(), input.getProductId());
        resultFuture.complete(Collections.singleton(new EnrichedOrderEvent(input, "N/A - Enrichment Timeout")));
    }
}
