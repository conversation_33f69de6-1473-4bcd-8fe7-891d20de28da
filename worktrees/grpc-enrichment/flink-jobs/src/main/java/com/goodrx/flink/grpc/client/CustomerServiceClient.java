package com.goodrx.flink.grpc.client;

import com.goodrx.flink.grpc.Address;
import com.goodrx.flink.grpc.CustomerBatchRequest;
import com.goodrx.flink.grpc.CustomerBatchResponse;
import com.goodrx.flink.grpc.CustomerRequest;
import com.goodrx.flink.grpc.CustomerResponse;
import com.goodrx.flink.grpc.CustomerServiceGrpc;
import io.grpc.ManagedChannel;
import io.grpc.StatusRuntimeException;
import io.grpc.stub.StreamObserver;

import java.util.concurrent.TimeUnit;
import java.util.logging.Level;
import java.util.logging.Logger;

// TODO: Implement robust backpressure mechanisms for streaming calls.
// TODO: Enhance error handling and retry logic (possibly via GrpcClientManager or interceptors).

/**
 * Client for interacting with the CustomerService gRPC service.
 * This client wraps the generated gRPC stubs and uses GrpcClientManager to obtain channels.
 */
public class CustomerServiceClient implements AutoCloseable {

    private static final Logger logger = Logger.getLogger(CustomerServiceClient.class.getName());
    private final ManagedChannel channel;
    private final CustomerServiceGrpc.CustomerServiceBlockingStub blockingStub;
    private final CustomerServiceGrpc.CustomerServiceFutureStub futureStub;
    private final CustomerServiceGrpc.CustomerServiceStub asyncStub;

    private final long defaultTimeoutSec;

    /**
     * Constructs a CustomerServiceClient using a GrpcClientManager and a target string.
     *
     * @param clientManager The GrpcClientManager to obtain the channel from.
     * @param target The target address string (e.g., "hostname:port").
     * @param defaultTimeoutSec Default timeout in seconds for RPC calls.
     */
    public CustomerServiceClient(GrpcClientManager clientManager, String target, long defaultTimeoutSec) {
        this.channel = clientManager.getChannel(target);
        this.blockingStub = CustomerServiceGrpc.newBlockingStub(this.channel);
        this.futureStub = CustomerServiceGrpc.newFutureStub(this.channel);
        this.asyncStub = CustomerServiceGrpc.newStub(this.channel);
        this.defaultTimeoutSec = defaultTimeoutSec;
    }

    /**
     * Constructs a CustomerServiceClient using a GrpcClientManager and a specific GrpcClientConfig.
     *
     * @param clientManager The GrpcClientManager to obtain the channel from.
     * @param config The GrpcClientConfig for the channel.
     * @param defaultTimeoutSec Default timeout in seconds for RPC calls.
     */
    public CustomerServiceClient(GrpcClientManager clientManager, GrpcClientManager.GrpcClientConfig config, long defaultTimeoutSec) {
        this.channel = clientManager.getChannel(config);
        this.blockingStub = CustomerServiceGrpc.newBlockingStub(this.channel);
        this.futureStub = CustomerServiceGrpc.newFutureStub(this.channel);
        this.asyncStub = CustomerServiceGrpc.newStub(this.channel);
        this.defaultTimeoutSec = defaultTimeoutSec;
    }

    /**
     * Gets a single customer by ID. (Unary RPC - Blocking)
     *
     * @param customerId The ID of the customer to retrieve.
     * @return The CustomerResponse.
     * @throws StatusRuntimeException if the RPC fails.
     */
    public CustomerResponse getCustomer(int customerId) throws StatusRuntimeException {
        CustomerRequest request = CustomerRequest.newBuilder().setCustomerId(customerId).build();
        logger.log(Level.INFO, "Requesting customer with ID: {0}", customerId);
        try {
            return blockingStub.withDeadlineAfter(defaultTimeoutSec, TimeUnit.SECONDS).getCustomer(request);
        } catch (StatusRuntimeException e) {
            logger.log(Level.WARNING, "RPC failed: {0}, Request: {1}", new Object[]{e.getStatus(), request});
            throw e;
        }
    }

    /**
     * Gets multiple customers in a batch. (Unary RPC - Blocking)
     *
     * @param batchRequest The CustomerBatchRequest containing multiple customer IDs.
     * @return The CustomerBatchResponse.
     * @throws StatusRuntimeException if the RPC fails.
     */
    public CustomerBatchResponse getCustomersInBatch(CustomerBatchRequest batchRequest) throws StatusRuntimeException {
        logger.log(Level.INFO, "Requesting customers in batch. Count: {0}", batchRequest.getRequestsCount());
        try {
            return blockingStub.withDeadlineAfter(defaultTimeoutSec, TimeUnit.SECONDS).getCustomersInBatch(batchRequest);
        } catch (StatusRuntimeException e) {
            logger.log(Level.WARNING, "RPC failed: {0}, Batch Request Item Count: {1}", new Object[]{e.getStatus(), batchRequest.getRequestsCount()});
            throw e;
        }
    }

    /**
     * Initiates a bi-directional streaming call to get customers.
     *
     * @param responseObserver The observer to handle incoming CustomerResponse messages from the server.
     * @return A StreamObserver to send CustomerRequest messages to the server.
     */
    public StreamObserver<CustomerRequest> getCustomersStream(StreamObserver<CustomerResponse> responseObserver) {
        logger.info("Initiating customer stream.");
        return asyncStub.getCustomersStream(responseObserver);
    }

    @Override
    public void close() {
        logger.info("CustomerServiceClient instance is being closed. Channel lifecycle managed by GrpcClientManager.");
    }

    // Example usage (optional, for testing or demonstration)
    public static void main(String[] args) {
        GrpcClientManager.GrpcClientConfig config = new GrpcClientManager.GrpcClientConfig("localhost", 50052, false); // Assuming customer service on 50052
        GrpcClientManager manager = new GrpcClientManager(config);

        try (CustomerServiceClient client = new CustomerServiceClient(manager, config, 5)) {
            // Test GetCustomer
            try {
                CustomerResponse customer = client.getCustomer(1);
                System.out.println("GetCustomer Response: " + customer.getFirstName() + " " + customer.getLastName());
            } catch (StatusRuntimeException e) {
                System.err.println("GetCustomer RPC failed: " + e.getStatus());
            }

            // Test GetCustomersInBatch
            try {
                CustomerBatchRequest batchRequest = CustomerBatchRequest.newBuilder()
                        .addRequests(CustomerRequest.newBuilder().setCustomerId(101))
                        .addRequests(CustomerRequest.newBuilder().setCustomerId(102))
                        .build();
                CustomerBatchResponse batchResponse = client.getCustomersInBatch(batchRequest);
                System.out.println("GetCustomersInBatch Response Count: " + batchResponse.getResponsesCount());
                 if (batchResponse.getResponsesCount() > 0) {
                     System.out.println("First customer in batch: " + batchResponse.getResponses(0).getFirstName());
                }
            } catch (StatusRuntimeException e) {
                System.err.println("GetCustomersInBatch RPC failed: " + e.getStatus());
            }
            
            // Test GetCustomersStream (Bi-directional)
            System.out.println("\nTesting GetCustomersStream (Bi-directional)...");
            StreamObserver<CustomerResponse> responseObserver = new StreamObserver<CustomerResponse>() {
                @Override
                public void onNext(CustomerResponse value) {
                    System.out.println("Stream Response: " + value.getFirstName() + " " + value.getLastName() + " (ID: " + value.getId() + ")");
                }

                @Override
                public void onError(Throwable t) {
                    System.err.println("Stream Error: " + t.getMessage());
                }

                @Override
                public void onCompleted() {
                    System.out.println("Stream completed from server.");
                }
            };

            StreamObserver<CustomerRequest> requestObserver = client.getCustomersStream(responseObserver);
            try {
                for (int i = 1; i <= 3; i++) {
                    CustomerRequest streamRequest = CustomerRequest.newBuilder().setCustomerId(300 + i).build();
                    System.out.println("Sending stream request for customer ID: " + streamRequest.getCustomerId());
                    requestObserver.onNext(streamRequest);
                    Thread.sleep(500); 
                }
            } catch (RuntimeException e) {
                requestObserver.onError(e);
                System.err.println("Error sending stream requests: " + e.getMessage());
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                System.err.println("Stream request sending interrupted.");
                requestObserver.onError(e);
            }
            requestObserver.onCompleted();

            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }

        } finally {
            System.out.println("Shutting down GrpcClientManager...");
            manager.shutdownAll();
        }
    }
}
