package com.goodrx.flink.enrichment.model;

import java.util.Objects;

/**
 * Represents an incoming order event.
 * This is a simple POJO for demonstration.
 * In a real scenario, this might be generated from Avro/Protobuf or be more complex.
 */
public class OrderEvent {
    private String orderId;
    private String customerId;
    private String productId;
    private int quantity;
    private double price;
    private long eventTimestamp; // For event time processing

    // Default constructor for Flink POJO detection
    public OrderEvent() {}

    public OrderEvent(String orderId, String customerId, String productId, int quantity, double price, long eventTimestamp) {
        this.orderId = orderId;
        this.customerId = customerId;
        this.productId = productId;
        this.quantity = quantity;
        this.price = price;
        this.eventTimestamp = eventTimestamp;
    }

    // Getters and Setters
    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public int getQuantity() {
        return quantity;
    }

    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }

    public double getPrice() {
        return price;
    }

    public void setPrice(double price) {
        this.price = price;
    }

    public long getEventTimestamp() {
        return eventTimestamp;
    }

    public void setEventTimestamp(long eventTimestamp) {
        this.eventTimestamp = eventTimestamp;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        OrderEvent that = (OrderEvent) o;
        return quantity == that.quantity &&
                Double.compare(that.price, price) == 0 &&
                eventTimestamp == that.eventTimestamp &&
                Objects.equals(orderId, that.orderId) &&
                Objects.equals(customerId, that.customerId) &&
                Objects.equals(productId, that.productId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(orderId, customerId, productId, quantity, price, eventTimestamp);
    }

    @Override
    public String toString() {
        return "OrderEvent{" +
                "orderId='" + orderId + '\'' +
                ", customerId='" + customerId + '\'' +
                ", productId='" + productId + '\'' +
                ", quantity=" + quantity +
                ", price=" + price +
                ", eventTimestamp=" + eventTimestamp +
                '}';
    }
}
