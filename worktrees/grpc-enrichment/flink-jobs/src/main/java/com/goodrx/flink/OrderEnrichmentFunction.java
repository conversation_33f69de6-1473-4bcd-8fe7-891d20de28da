package com.goodrx.flink;

import com.goodrx.flink.model.Customer;
import com.goodrx.flink.model.EnrichedOrder;
import com.goodrx.flink.model.Order;
import com.goodrx.flink.model.Product;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.ProcessFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * A Flink {@link ProcessFunction} that performs stateless enrichment of an {@link Order}.
 * <p>
 * This function assumes that the input {@code Order} objects have already been partially enriched
 * with {@link Customer} and {@link Product} objects by preceding asynchronous lookup functions.
 * </p>
 * <p>
 * For each incoming order, it simply transforms the input {@link Order} (with its associated
 * {@link Customer} and {@link Product} data) into an {@link EnrichedOrder} object.
 * This function does not perform any stateful operations like counting.
 * </p>
 * <p>
 * <b>Example Data Transformation:</b>
 * </p>
 * <p><b>Input (an {@link Order} object, after async lookups):</b></p>
 * <pre>{@code
 * // Assume this Order object arrives:
 * Order {
 *   id=123L,
 *   orderDate="2023-10-27T10:00:00Z",
 *   purchaser=100,
 *   quantity=2,
 *   productId=201,
 *   deleted=false,
 *   customer=Customer{id=100, firstName="John", lastName="Doe", email="<EMAIL>"},
 *   product=Product{id=201, name="Super Widget", description="A very fine widget.", weight=0.5}
 * }
 * }</pre>
 *
 * <p><b>Output (an {@link EnrichedOrder} object emitted by this function):</b></p>
 * <pre>{@code
 * EnrichedOrder {
 *   id=123L,
 *   orderDate="2023-10-27T10:00:00Z", // from original order
 *   purchaserId=100,                  // from original order
 *   quantity=2,                       // from original order
 *   productId=201,                    // from original order
 *   deleted=false,                    // from original order
 *   customerFirstName="John",         // from order.getCustomer()
 *   customerLastName="Doe",           // from order.getCustomer()
 *   customerEmail="<EMAIL>", // from order.getCustomer()
 *   productName="Super Widget",       // from order.getProduct()
 *   productDescription="A very fine widget.", // from order.getProduct()
 *   productWeight=0.5                 // from order.getProduct()
 * }
 * }</pre>
 * <p>
 * <b>Flink Pipeline Usage Example:</b>
 * <pre>{@code
 * DataStream<Order> ordersWithCustomerAndProduct = ... ; // Stream of Orders with Customer and Product populated
 * DataStream<EnrichedOrder> fullyEnrichedOrders = ordersWithCustomerAndProduct.process(new OrderEnrichmentFunction());
 * // If the stream was previously keyed only for a downstream KeyedProcessFunction that is now removed or changed,
 * // the keyBy operation might no longer be necessary before this ProcessFunction.
 * }</pre>
 * </p>
 */
public class OrderEnrichmentFunction extends ProcessFunction<Order, EnrichedOrder> {

    private static final long serialVersionUID = 1L;
    private static final Logger LOG = LoggerFactory.getLogger(OrderEnrichmentFunction.class);

    // No state needed for simple enrichment

    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters); // Retain for potential ProcessFunction lifecycle, can be removed if truly empty
        LOG.info("OrderEnrichmentFunction initialized (stateless).");
    }

    @Override
    public void processElement(
            Order order,
            ProcessFunction<Order, EnrichedOrder>.Context ctx, 
            Collector<EnrichedOrder> out) throws Exception {
        
        LOG.info("[OEF ENTRY] Processing orderId: {}. Incoming order.getCustomer() is {}. Incoming order.getProduct() is {}", 
                  order.getId(), 
                  order.getCustomer() != null ? "present" : "NULL", 
                  order.getProduct() != null ? "present" : "NULL");
        
        if (order.getCustomer() == null) {
            LOG.warn("Order {} is missing customer details. Emitting enriched order with available data.", order.getId());
        }
        if (order.getProduct() == null) {
            LOG.warn("Order {} is missing product details. Emitting enriched order with available data.", order.getId());
        }

        // Create enriched order with customer and product details
        // The EnrichedOrder constructor will handle null customer/product if they occur
        EnrichedOrder enrichedOrder = new EnrichedOrder(
            order,
            order.getCustomer(), 
            order.getProduct()
        );

        // Output the enriched order
        LOG.info("OrderEnrichmentFunction: Emitting EnrichedOrder for orderId: {}, purchaserId: {}. EnrichedOrder: {}", 
                  enrichedOrder.getId(), (order != null ? order.getPurchaser() : null), enrichedOrder);
        out.collect(enrichedOrder);
    }
}
