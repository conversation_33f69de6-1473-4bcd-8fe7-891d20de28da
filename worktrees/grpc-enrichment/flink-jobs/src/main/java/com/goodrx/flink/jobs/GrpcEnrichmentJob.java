package com.goodrx.flink.jobs;

import com.goodrx.flink.enrichment.functions.GrpcCustomerLookupFunction;
import com.goodrx.flink.enrichment.functions.GrpcProductLookupFunction;
// Import model classes (adjust if interfaces are top-level)
import com.goodrx.flink.enrichment.model.CustomerDataEvent;
import com.goodrx.flink.enrichment.model.ProductDataEvent;

import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.streaming.api.datastream.AsyncDataStream;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;

import java.util.UUID;
import java.util.concurrent.TimeUnit;


public class GrpcEnrichmentJob {

    // Configuration for gRPC services (should be externalized)
    private static final String PRODUCT_SERVICE_HOST = "localhost";
    private static final int PRODUCT_SERVICE_PORT = 50051; // Example port
    private static final String CUSTOMER_SERVICE_HOST = "localhost";
    private static final int CUSTOMER_SERVICE_PORT = 50052; // Example port
    private static final boolean USE_TLS = false;
    private static final long GRPC_TIMEOUT_SECONDS = 10;
    private static final long ASYNC_TIMEOUT_MS = 15000; // Timeout for Flink async operations


    public static void main(String[] args) throws Exception {
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();

        // 1. Define a Source (example: simple data generator)
        // In a real scenario, this would likely be a Kafka consumer or similar.
        DataStream<ProductDataEvent> productInputStream = env.fromElements(
                new ProductDataEvent(UUID.randomUUID().toString(), System.currentTimeMillis(), 101, "payload1"),
                new ProductDataEvent(UUID.randomUUID().toString(), System.currentTimeMillis(), 102, "payload2"),
                new ProductDataEvent(UUID.randomUUID().toString(), System.currentTimeMillis(), 999, "payload_fail_test") // for testing error
        ).assignTimestampsAndWatermarks(WatermarkStrategy.forMonotonousTimestamps());
        
        DataStream<CustomerDataEvent> customerInputStream = env.fromElements(
                new CustomerDataEvent(UUID.randomUUID().toString(), System.currentTimeMillis(), 201, "order_abc"),
                new CustomerDataEvent(UUID.randomUUID().toString(), System.currentTimeMillis(), 202, "order_xyz"),
                new CustomerDataEvent(UUID.randomUUID().toString(), System.currentTimeMillis(), 888, "order_fail_test")
        ).assignTimestampsAndWatermarks(WatermarkStrategy.forMonotonousTimestamps());


        // 2. Enrich with Product Data using GrpcProductLookupFunction
        // The interfaces in GrpcProductLookupFunction need to match ProductDataEvent
        // For now, assuming ProductDataEvent correctly implements them.
        DataStream<ProductDataEvent> enrichedProductStream = AsyncDataStream.unorderedWait(
                productInputStream,
                new GrpcProductLookupFunction(
                        PRODUCT_SERVICE_HOST,
                        PRODUCT_SERVICE_PORT,
                        USE_TLS,
                        GRPC_TIMEOUT_SECONDS
                ),
                ASYNC_TIMEOUT_MS,
                TimeUnit.MILLISECONDS,
                100 // Capacity for the async buffer
        ).name("ProductEnrichment");

        // 3. Enrich with Customer Data using GrpcCustomerLookupFunction
        DataStream<CustomerDataEvent> enrichedCustomerStream = AsyncDataStream.unorderedWait(
                customerInputStream,
                new GrpcCustomerLookupFunction(
                        CUSTOMER_SERVICE_HOST,
                        CUSTOMER_SERVICE_PORT,
                        USE_TLS,
                        GRPC_TIMEOUT_SECONDS
                ),
                ASYNC_TIMEOUT_MS,
                TimeUnit.MILLISECONDS,
                100
        ).name("CustomerEnrichment");

        // TODO: Implement different enrichment strategies (single, batch, streaming)
        // The GrpcStreamingEnrichmentFunction would be used differently, likely not with AsyncDataStream.
        // For example:
        // DataStream<EnrichedEvent> streamingEnrichedStream = inputStream
        // .keyBy(...) // if stateful processing per key
        // .process(new GrpcStreamingEnrichmentFunction(...));

        // 4. Define a Sink (example: print to console)
        enrichedProductStream.map((MapFunction<ProductDataEvent, String>) value -> {
            if (value.hasEnrichmentError()) {
                return "Failed Product Event: " + value.getEventId() + ", Error: " + value.getEnrichmentError();
            }
            return "Enriched Product Event: ID=" + value.getProductId() + ", Name=" + value.getProductName() + ", Desc=" + value.getProductDescription();
        }).name("ProductSink").print();
        
        enrichedCustomerStream.map((MapFunction<CustomerDataEvent, String>) value -> {
            if (value.hasCustomerEnrichmentError()) {
                return "Failed Customer Event: " + value.getEventId() + ", Error: " + value.getCustomerEnrichmentError();
            }
            return "Enriched Customer Event: ID=" + value.getCustomerId() + ", Name=" + value.getCustomerFirstName() + " " + value.getCustomerLastName();
        }).name("CustomerSink").print();


        // TODO: Add performance monitoring and fault tolerance mechanisms.

        env.execute("gRPC Enrichment Job");
    }
}
