package com.goodrx.flink.model;

import com.fasterxml.jackson.annotation.JsonProperty;

public class Product {
    private Integer id;
    private String name;
    private String description;
    private Double weight;

    // Default constructor for Jackson
    public Product() {}

    public Product(Integer id, String name, String description, Double weight) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.weight = weight;
    }

    // Getters and setters
    public Integer getId() { return id; }
    public void setId(Integer id) { this.id = id; }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public Double getWeight() { return weight; }
    @JsonProperty("weight")
    public void setWeight(Double weight) { this.weight = weight; }

    @Override
    public String toString() {
        return String.format(
            "Product{id=%d, name='%s', weight=%.2f}",
            id, name, weight
        );
    }
}
