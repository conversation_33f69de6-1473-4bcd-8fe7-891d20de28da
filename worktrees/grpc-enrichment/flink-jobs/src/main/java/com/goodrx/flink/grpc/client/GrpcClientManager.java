package com.goodrx.flink.grpc.client;

import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import io.grpc.inprocess.InProcessChannelBuilder;
// For more advanced TLS/mTLS, NettyChannelBuilder might be needed
// import io.grpc.netty.shaded.io.grpc.netty.NettyChannelBuilder;
// import io.grpc.netty.shaded.io.netty.handler.ssl.SslContext;
// import io.grpc.netty.shaded.io.netty.handler.ssl.SslContextBuilder;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.logging.Level;
import java.util.logging.Logger;

// TODO: Implement connection pooling (e.g., using a library or custom logic if needed beyond gRPC's capabilities)
// TODO: Implement retry and circuit breaker patterns (e.g., using Resilience4j or gRPC built-in retry policies)
// TODO: Implement metrics collection (e.g., using Micrometer or Dropwizard Metrics via interceptors)
// TODO: Enhance TLS/mTLS security with specific certificate trust and key management

/**
 * Manages gRPC channels, including their lifecycle, configuration, and potentially pooling.
 * This class is intended to be a central point for obtaining and managing gRPC connections
 * to various services within the Flink jobs.
 */
public class GrpcClientManager {

    private static final Logger logger = Logger.getLogger(GrpcClientManager.class.getName());
    private final ConcurrentHashMap<GrpcClientConfig, ManagedChannel> channels = new ConcurrentHashMap<>(); // Use GrpcClientConfig as key
    private final GrpcClientConfig defaultConfig;

    /**
     * Configuration for a gRPC client channel.
     */
    public static class GrpcClientConfig {
        private final String targetHost;
        private final int targetPort;
        private final boolean useTls;
        // TODO: Add fields for TLS certificates (trust, key), timeouts, load balancing, retry policies etc.

        public GrpcClientConfig(String targetHost, int targetPort, boolean useTls) {
            this.targetHost = targetHost;
            this.targetPort = targetPort;
            this.useTls = useTls;
        }

        public String getTarget() {
            return targetHost + ":" + targetPort;
        }

        public boolean isUseTls() {
            return useTls;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            GrpcClientConfig that = (GrpcClientConfig) o;
            return targetPort == that.targetPort &&
                   useTls == that.useTls &&
                   java.util.Objects.equals(targetHost, that.targetHost);
        }

        @Override
        public int hashCode() {
            return java.util.Objects.hash(targetHost, targetPort, useTls);
        }
    }

    /**
     * Constructs a GrpcClientManager with a default configuration.
     * @param defaultConfig The default configuration to use if specific config is not provided for a target.
     */
    public GrpcClientManager(GrpcClientConfig defaultConfig) {
        this.defaultConfig = defaultConfig;
    }

    /**
     * Gets or creates a ManagedChannel for the specified target string (e.g., "hostname:port").
     * Uses the default configuration if one was provided to the manager.
     *
     * @param target The target address string.
     * @return A ManagedChannel.
     */
    public ManagedChannel getChannel(String target) {
        // Create a temporary config from the target string and default settings
        // This assumes a simple 'host:port' format for the target string
        String[] parts = target.split(":");
        if (parts.length != 2) {
            throw new IllegalArgumentException("Target string '" + target + "' must be in 'host:port' format.");
        }
        String host = parts[0];
        int port;
        try {
            port = Integer.parseInt(parts[1]);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Invalid port in target string '" + target + "'.", e);
        }
        // Use default TLS setting if defaultConfig is available, otherwise default to false (plaintext)
        boolean useTls = (this.defaultConfig != null) ? this.defaultConfig.isUseTls() : false;
        GrpcClientConfig configFromTarget = new GrpcClientConfig(host, port, useTls);
        return getChannel(configFromTarget);
    }

    /**
     * Gets or creates a ManagedChannel based on the provided GrpcClientConfig.
     *
     * @param config The specific configuration for this channel.
     * @return A ManagedChannel.
     */
    public ManagedChannel getChannel(GrpcClientConfig config) {
        if (config == null) {
            throw new IllegalArgumentException("GrpcClientConfig cannot be null.");
        }
        logger.log(Level.INFO, "[DEBUG_GET_CHANNEL_CONFIG_KEY] Attempting to get/create channel for config - Host: " + config.targetHost + ", Port: " + config.targetPort + ", TLS: " + config.useTls + ", HashCode: " + config.hashCode());

        // Now uses the GrpcClientConfig object itself as the key, relying on its hashCode and equals methods.
        return channels.computeIfAbsent(config, c -> {
            logger.log(Level.INFO, "[DEBUG_GET_CHANNEL_LAMBDA] Cache miss for config - Host: " + c.targetHost + ", Port: " + c.targetPort + ", TLS: " + c.useTls + ". Calling createChannel.");
            return createChannel(c);
        });
    }

    // Create channel now directly uses GrpcClientConfig
    private ManagedChannel createChannel(GrpcClientConfig config) {
        if (config == null) {
            // This case should ideally be handled by the caller or a default config should always be resolved before this point.
            // If defaultConfig is null and a raw target string was used, a temporary config would have been made.
            // If getChannel(config) was called with null, it would throw an IllegalArgumentException.
            // For safety, if it somehow reaches here with null, we must throw.
            throw new IllegalStateException("Cannot create channel with null GrpcClientConfig and no default config path.");
        }

        String targetForBuilder = config.getTarget(); // host:port string for ManagedChannelBuilder
        logger.info("Creating new gRPC channel for config: " + config.targetHost + ":" + config.targetPort + ", TLS: " + config.useTls);

        ManagedChannelBuilder<?> channelBuilder;
        logger.log(Level.INFO, "[DEBUG_CREATE_CHANNEL] Received config with host: " + config.targetHost + ", port: " + config.targetPort + ", TLS: " + config.useTls);
        // Heuristic: if port is 0, assume it's an in-process channel request using the host as server name.
        // In-process channels do not use ports in the same way.
        if (config.targetPort == 0) {
            logger.log(Level.INFO, "[DEBUG_IN_PROCESS_PATH] Port is 0 for host: " + config.targetHost + ". Using InProcessChannelBuilder.");
            channelBuilder = io.grpc.inprocess.InProcessChannelBuilder.forName(config.targetHost).directExecutor();
        } else {
            logger.log(Level.INFO, "[DEBUG_REGULAR_PATH] Port is " + config.targetPort + " for host: " + config.targetHost + ". Using ManagedChannelBuilder for target: " + targetForBuilder);
            channelBuilder = ManagedChannelBuilder.forTarget(targetForBuilder);
        }

        // TLS configuration is only applicable for non-in-process channels.
        // InProcessChannelBuilder does not have useTransportSecurity() or usePlaintext().
        if (!(channelBuilder instanceof io.grpc.inprocess.InProcessChannelBuilder)) {
            if (config.isUseTls()) {
            channelBuilder.useTransportSecurity();
            // Example for more advanced TLS:
            // try {
            // SslContext sslContext = SslContextBuilder.forClient()
            // .trustManager(new File("path/to/ca.crt")) // Example CA certificate
            // .build();
            // channelBuilder = NettyChannelBuilder.forTarget(target).sslContext(sslContext);
            // } catch (Exception e) {
            //    logger.log(Level.SEVERE, "Failed to configure TLS for channel: " + target, e);
            //    throw new RuntimeException("TLS configuration failed for " + target, e);
            // }
            logger.info("Using TLS for channel: " + targetForBuilder);
        } else {
            channelBuilder.usePlaintext();
            logger.info("Using plaintext for channel: " + targetForBuilder);
            }
        } else {
             logger.info("TLS/plaintext configuration skipped for InProcessChannel: " + config.targetHost);
        }

        // TODO: Add further configurations:
        // channelBuilder.defaultLoadBalancingPolicy("round_robin"); // Example
        // channelBuilder.enableRetry(); // Requires service config or client-side interceptor
        // channelBuilder.keepAliveTime(30, TimeUnit.SECONDS);
        // channelBuilder.keepAliveTimeout(10, TimeUnit.SECONDS);
        // channelBuilder.keepAliveWithoutCalls(true);
        // channelBuilder.userAgent("MyFlinkGrpcClient/1.0");
        // channelBuilder.intercept( /* client interceptors for logging, metrics, auth, retry */);

        ManagedChannel builtChannel = channelBuilder.build();
        logger.log(Level.INFO, "[DEBUG_BUILT_CHANNEL_AUTHORITY] Authority of channel directly from builder: " + builtChannel.authority());
        return builtChannel;
    }

    /**
     * Shuts down a specific channel by its target string.
     *
     * @param target The target address of the channel to shut down.
     */
    /**
     * Shuts down a specific channel by its GrpcClientConfig.
     *
     * @param config The GrpcClientConfig of the channel to shut down.
     */
    public void shutdownChannel(GrpcClientConfig config) {
        if (config == null) return;

        ManagedChannel channel = channels.remove(config);
        if (channel != null) {
            String targetInfo = config.getTarget() + " (TLS: " + config.isUseTls() + ")";
            logger.info("Attempting to shut down gRPC channel for config: " + targetInfo);
            try {
                if (!channel.isTerminated()) {
                    channel.shutdown();
                    if (!channel.awaitTermination(5, TimeUnit.SECONDS)) {
                        channel.shutdownNow();
                        if (!channel.awaitTermination(5, TimeUnit.SECONDS)) {
                            logger.warning("Channel for config " + targetInfo + " did not terminate.");
                        }
                    }
                }
            } catch (InterruptedException e) {
                logger.log(Level.WARNING, "Interrupted while shutting down channel for config: " + targetInfo, e);
                channel.shutdownNow(); // Force shutdown on interrupt
                Thread.currentThread().interrupt();
            }
            logger.info("Channel for config " + targetInfo + " shutdown status: " + channel.isTerminated());
        }
    }

    /**
     * Shuts down all managed channels.
     * This should typically be called when the Flink job or application is stopping.
     */
    public void shutdownAll() {
        logger.info("Shutting down all managed gRPC channels.");
        // Iterate over a copy of keys to avoid ConcurrentModificationException if shutdownChannel modifies the map
        for (GrpcClientConfig config : channels.keySet().toArray(new GrpcClientConfig[0])) {
            shutdownChannel(config); // Now calls the updated shutdownChannel with GrpcClientConfig
        }
        channels.clear(); // Ensure the map is cleared, though remove in shutdownChannel should handle it.
        logger.info("All channels processed for shutdown.");
    }

    // Optional: Main method for basic standalone testing of the manager
    public static void main(String[] args) {
        GrpcClientConfig defaultConfig = new GrpcClientConfig("localhost", 50051, false);
        GrpcClientManager manager = new GrpcClientManager(defaultConfig);

        ManagedChannel channel1 = manager.getChannel("localhost:50051");
        System.out.println("Channel 1 ('localhost:50051') state: " + channel1.getState(true));

        GrpcClientConfig specificConfig = new GrpcClientConfig("localhost", 8080, true);
        ManagedChannel channel2 = manager.getChannel(specificConfig);
        System.out.println("Channel 2 ('localhost:8080' with TLS) state: " + channel2.getState(true));
        
        ManagedChannel channel3 = manager.getChannel("example.com:443"); // Will use default config (localhost:50051)
                                                                         // which is likely incorrect for example.com:443
        System.out.println("Channel 3 ('example.com:443' using default config) state: " + channel3.getState(true));


        System.out.println("\nSimulating work...\n");
        try {
            Thread.sleep(1000); // Simulate some activity
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        manager.shutdownAll();
        System.out.println("All channels shut down procedure initiated.");
        System.out.println("Channel 1 state after shutdown: " + channel1.isTerminated());
        System.out.println("Channel 2 state after shutdown: " + channel2.isTerminated());
        System.out.println("Channel 3 state after shutdown: " + channel3.isTerminated());
    }
}
