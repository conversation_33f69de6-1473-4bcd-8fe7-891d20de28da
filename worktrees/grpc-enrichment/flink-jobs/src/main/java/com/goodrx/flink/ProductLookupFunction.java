package com.goodrx.flink;

import com.goodrx.flink.model.Order;
import com.goodrx.flink.model.Product;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.async.ResultFuture;
import org.apache.flink.streaming.api.functions.async.RichAsyncFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Collections;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import org.apache.commons.dbcp2.BasicDataSource;

/**
 * An asynchronous function that enriches Order objects with Product data.
 * <p>
 * This function connects to a PostgreSQL database and looks up product details
 * based on the product ID in the order. It then attaches the product information
 * to the order object. The lookup is performed asynchronously to improve performance.
 * </p>
 * <p>
 * If the order has no product ID, or if the product cannot be found in the database,
 * the order is returned unchanged.
 * </p>
 * <p>
 * This implementation uses a connection pool and a dedicated thread pool to perform
 * database operations asynchronously, following the Flink AsyncFunction pattern.
 * Each query is submitted to the thread pool, which executes it using a connection
 * from the pool, then returns the result via CompletableFuture.
 * </p>
 */
public class ProductLookupFunction extends RichAsyncFunction<Order, Order> {
    private static final long serialVersionUID = 1L;
    private static final Logger LOG = LoggerFactory.getLogger(ProductLookupFunction.class);

    protected transient BasicDataSource dataSource;
    protected transient ExecutorService executorService;
    
    private final String jdbcUrl;
    private final String jdbcUser;
    private final String jdbcPass;
    private final int numThreads;
    private final int maxConnections;

    /**
     * Creates a new ProductLookupFunction with empty JDBC connection parameters.
     * This constructor should only be used when subclassing for testing purposes.
     */
    public ProductLookupFunction() {
        this("", "", "", 4, 8);
    }

    /**
     * Creates a new ProductLookupFunction with the specified JDBC connection parameters.
     *
     * @param jdbcUrl  The JDBC URL for the database connection
     * @param jdbcUser The username for the database connection
     * @param jdbcPass The password for the database connection
     */
    public ProductLookupFunction(String jdbcUrl, String jdbcUser, String jdbcPass) {
        this(jdbcUrl, jdbcUser, jdbcPass, 4, 8);
    }
    
    /**
     * Creates a new ProductLookupFunction with the specified JDBC connection parameters
     * and thread pool size.
     *
     * @param jdbcUrl     The JDBC URL for the database connection
     * @param jdbcUser    The username for the database connection
     * @param jdbcPass    The password for the database connection
     * @param numThreads  The number of threads to use for async execution
     */
    public ProductLookupFunction(String jdbcUrl, String jdbcUser, String jdbcPass, int numThreads) {
        this(jdbcUrl, jdbcUser, jdbcPass, numThreads, numThreads * 2);
    }
    
    /**
     * Creates a new ProductLookupFunction with the specified JDBC connection parameters,
     * thread pool size, and connection pool size.
     *
     * @param jdbcUrl        The JDBC URL for the database connection
     * @param jdbcUser       The username for the database connection
     * @param jdbcPass       The password for the database connection
     * @param numThreads     The number of threads to use for async execution
     * @param maxConnections The maximum number of connections in the pool
     */
    public ProductLookupFunction(String jdbcUrl, String jdbcUser, String jdbcPass, int numThreads, int maxConnections) {
        this.jdbcUrl = jdbcUrl;
        this.jdbcUser = jdbcUser;
        this.jdbcPass = jdbcPass;
        this.numThreads = numThreads;
        this.maxConnections = maxConnections;
    }

    /**
     * Initializes the database connection pool and the thread pool for async execution.
     *
     * @param parameters Configuration parameters for the function
     * @throws Exception if the database connection cannot be established
     */
    @Override
    public void open(Configuration parameters) throws Exception {
        // Initialize connection pool
        dataSource = new BasicDataSource();
        dataSource.setDriverClassName("org.postgresql.Driver");
        dataSource.setUrl(getJdbcUrl());
        dataSource.setUsername(getJdbcUser());
        dataSource.setPassword(getJdbcPass());
        dataSource.setInitialSize(2);
        dataSource.setMaxTotal(maxConnections);
        dataSource.setMaxIdle(maxConnections);
        dataSource.setMinIdle(1);
        dataSource.setMaxWaitMillis(5000);
        dataSource.setTestOnBorrow(true);
        dataSource.setTestWhileIdle(true);
        dataSource.setValidationQuery("SELECT 1");
        
        // Create thread pool with named threads for better debugging
        executorService = Executors.newFixedThreadPool(numThreads, new ThreadFactory() {
            private final AtomicInteger counter = new AtomicInteger();
            private final ThreadGroup group = new ThreadGroup("product-lookup-pool");
            
            @Override
            public Thread newThread(Runnable r) {
                Thread t = new Thread(group, r, "product-lookup-" + counter.incrementAndGet());
                if (t.isDaemon()) {
                    t.setDaemon(false);
                }
                if (t.getPriority() != Thread.NORM_PRIORITY) {
                    t.setPriority(Thread.NORM_PRIORITY);
                }
                return t;
            }
        });
        
        LOG.info("Initialized ProductLookupFunction with {} threads and {} max connections", 
                numThreads, maxConnections);

        // Test connection pool with retries
        int maxRetries = 3;
        long retryDelayMs = 2000;
        for (int i = 0; i < maxRetries; i++) {
            try (Connection conn = dataSource.getConnection()) {
                LOG.info("Successfully tested database connection pool for ProductLookupFunction on attempt {}/{}", (i + 1), maxRetries);
                return; // Success
            } catch (SQLException e) { // Catch SQLException specifically for connection issues
                LOG.warn("Failed to test database connection pool for ProductLookupFunction on attempt {}/{}. Retrying in {}ms...", 
                         (i + 1), maxRetries, retryDelayMs, e);
                if (i == maxRetries - 1) { // Last attempt
                    LOG.error("Failed to establish database connection for ProductLookupFunction after {} retries.", maxRetries, e);
                    throw e; // Re-throw on last attempt
                }
                try {
                    Thread.sleep(retryDelayMs);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("Thread interrupted during retry delay", ie);
                }
            }
        }
    }

    /**
     * Creates a database connection from the connection pool.
     * This method is protected to allow overriding in tests.
     *
     * @return A Connection to the database from the pool
     * @throws SQLException if the connection cannot be established
     */
    protected Connection getConnection() throws SQLException {
        if (dataSource == null) {
            throw new SQLException("DataSource is not initialized. Make sure open() was called.");
        }
        return dataSource.getConnection();
    }
    
    /**
     * Returns the JDBC URL for database connection.
     * Protected method for testability.
     * 
     * @return The JDBC URL
     */
    protected String getJdbcUrl() {
        return jdbcUrl;
    }
    
    /**
     * Returns the JDBC username for database connection.
     * Protected method for testability.
     * 
     * @return The JDBC username
     */
    protected String getJdbcUser() {
        return jdbcUser;
    }
    
    /**
     * Returns the JDBC password for database connection.
     * Protected method for testability.
     * 
     * @return The JDBC password
     */
    protected String getJdbcPass() {
        return jdbcPass;
    }

    /**
     * Closes the database resources when the function is no longer needed.
     *
     * @throws Exception if the resources cannot be closed properly
     */
    @Override
    public void close() throws Exception {
        LOG.info("Closing ProductLookupFunction resources...");
        if (dataSource != null) {
            try {
                dataSource.close();
                LOG.info("Database connection pool closed for {}.", this.getClass().getSimpleName());
            } catch (SQLException e) {
                LOG.error("Error closing database connection pool for {}.", this.getClass().getSimpleName(), e);
            }
        }
        if (executorService != null && !executorService.isShutdown()) {
            LOG.info("Shutting down executor service for {}.", this.getClass().getSimpleName());
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(5, TimeUnit.SECONDS)) {
                    LOG.warn("Executor service did not terminate in 5 seconds, forcing shutdown for {}.", this.getClass().getSimpleName());
                    executorService.shutdownNow();
                } else {
                    LOG.info("Executor service for {} shut down gracefully.", this.getClass().getSimpleName());
                }
            } catch (InterruptedException e) {
                LOG.warn("Interrupted while waiting for executor service to shut down for {}.", this.getClass().getSimpleName());
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        LOG.info("Closed ProductLookupFunction resources");
    }

    /**
     * Performs an asynchronous lookup of product data for an order.
     * <p>
     * This method checks if the order has a product ID, and if so, looks up the
     * corresponding product in the database using a thread pool and connection pool.
     * The product data is then attached to the order object.
     * </p>
     * <p>
     * If the order has no product ID, or if the product cannot be found, the
     * order is returned unchanged.
     * </p>
     *
     * @param order         The order to enrich with product data
     * @param resultFuture  The future to complete with the enriched order
     * @throws Exception if an error occurs during the lookup
     */
    @Override
    public void asyncInvoke(Order order, ResultFuture<Order> resultFuture) throws Exception {
        LOG.info("ProductLookup: START asyncInvoke for orderId: {}, productId: {}", order.getId(), order.getProductId());

        if (order.getProductId() == null) {
            LOG.debug("Order {} has no product ID, skipping product lookup", order.getId());
            resultFuture.complete(Collections.singletonList(order));
            return;
        }

        CompletableFuture.supplyAsync(() -> {
            Product product = null;
            Connection conn = null;
            PreparedStatement stmt = null;
            ResultSet rs = null;
            
            try {
                conn = getConnection();
                stmt = conn.prepareStatement(
                    "SELECT id, name, description, weight FROM products WHERE id = ?"
                );
                stmt.setInt(1, order.getProductId());
                LOG.debug("Executing async query for product ID={}", order.getProductId());
                rs = stmt.executeQuery();
                
                if (rs.next()) {
                    product = new Product(
                        rs.getInt("id"),
                        rs.getString("name"),
                        rs.getString("description"),
                        rs.getDouble("weight")
                    );
                    LOG.debug("Found product data: id={}, name={}", 
                            product.getId(), product.getName());
                } else {
                    LOG.warn("No product found for productId={}", order.getProductId());
                }
            } catch (Exception e) {
                LOG.error("Error looking up product for order: " + order.getId(), e);
                // product remains null, exceptionally block will handle future completion
            } finally {
                try { if (rs != null) rs.close(); } catch (SQLException e) { LOG.warn("Error closing ResultSet", e); }
                try { if (stmt != null) stmt.close(); } catch (SQLException e) { LOG.warn("Error closing PreparedStatement", e); }
                try { if (conn != null) conn.close(); } catch (SQLException e) { LOG.warn("Error closing Connection", e); }
            }
            return product; 
        }, executorService)
        .thenAccept(lookedUpProduct -> {
            Order orderToEmit = new Order(order); 
            if (lookedUpProduct != null) {
                orderToEmit = orderToEmit.withProduct(lookedUpProduct);
                LOG.debug("Successfully enriched order {} with product {}", 
                        orderToEmit.getId(), lookedUpProduct.getId());
            } else {
                LOG.debug("No product attached to order {} (lookedUpProduct was null or error occurred in supplyAsync)", orderToEmit.getId());
            }
            
            LOG.debug("Completing future for order {}. Product on orderToEmit: {}", orderToEmit.getId(), orderToEmit.getProduct());
            resultFuture.complete(Collections.singletonList(orderToEmit)); 
        })
        .exceptionally(ex -> {
            LOG.error("Exception in product lookup CompletableFuture chain for orderId: {}", order.getId(), ex);
            resultFuture.complete(Collections.singletonList(order)); 
            return null; 
        });
    }

    /**
     * Handles timeout cases by completing the result future with the original order.
     *
     * @param order         The original order
     * @param resultFuture  The future to complete
     */
    @Override
    public void timeout(Order order, ResultFuture<Order> resultFuture) throws Exception {
        LOG.warn("Timeout while looking up product for order: {}. Passing original order through.", order.getId());
        resultFuture.complete(Collections.singletonList(order));
    }
}
