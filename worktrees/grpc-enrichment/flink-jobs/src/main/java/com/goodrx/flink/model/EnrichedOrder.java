package com.goodrx.flink.model;

import java.time.Instant;
import java.math.BigDecimal;

public class EnrichedOrder {
    // Order details
    private Long id;
    private transient Instant orderDate;
    private Integer quantity;
    private transient Instant enrichmentTime;
    
    // Customer details
    private Integer purchaserId;
    private String customerFirstName;
    private String customerLastName;
    private String customerEmail;
    
    // Product details
    private Integer productId;
    private String productName;
    private String productDescription;
    private Double productWeight;
    

    // Default constructor for integration tests
    public EnrichedOrder() {
        this.enrichmentTime = Instant.now();
    }
    
    public EnrichedOrder(Order order, Customer customer, Product product) {
        // Order details
        this.id = order.getId();
        this.orderDate = order.getOrderDate();
        this.quantity = order.getQuantity();
        this.purchaserId = order.getPurchaser();
        this.productId = order.getProductId();
        this.enrichmentTime = Instant.now();

        // Customer details
        if (customer != null) {
            this.customerFirstName = customer.getFirstName();
            this.customerLastName = customer.getLastName();
            this.customerEmail = customer.getEmail();
        }
        
        // Product details
        if (product != null) {
            this.productName = product.getName();
            this.productDescription = product.getDescription();
            this.productWeight = product.getWeight();
        }
        
    }

    // Order Getters
    public Long getId() { return id; }
    public Instant getOrderDate() { return orderDate; }
    public Integer getQuantity() { return quantity; }
    public Instant getEnrichmentTime() { return enrichmentTime; }
    
    // Order Setters
    public void setId(Long id) { this.id = id; }

    // Customer Getters
    public Integer getPurchaserId() { return purchaserId; }
    public String getCustomerFirstName() { return customerFirstName; }
    public String getCustomerLastName() { return customerLastName; }
    public String getCustomerEmail() { return customerEmail; }
    
    // Product Getters
    public Integer getProductId() { return productId; }
    public String getProductName() { return productName; }
    public String getProductDescription() { return productDescription; }
    public Double getProductWeight() { return productWeight; }
    
    // Product Setters
    public void setProductName(String productName) { this.productName = productName; }

    @Override
    public String toString() {
        return String.format(
            "EnrichedOrder{id=%d, customer='%s %s', quantity=%d, product='%s', email='%s'}",
            id, 
            customerFirstName != null ? customerFirstName : "",
            customerLastName != null ? customerLastName : "",
            quantity,
            productName != null ? productName : "N/A",
            customerEmail != null ? customerEmail : ""
        );
    }
}
