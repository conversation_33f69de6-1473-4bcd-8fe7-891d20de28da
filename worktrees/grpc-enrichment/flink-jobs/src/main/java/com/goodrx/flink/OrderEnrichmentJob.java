package com.goodrx.flink;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.goodrx.flink.CustomerLookupFunction;
import com.goodrx.flink.ProductLookupFunction;
import com.goodrx.flink.model.EnrichedOrder;
import com.goodrx.flink.model.Order;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.datastream.AsyncDataStream;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.TimeUnit;

import org.apache.flink.api.java.utils.ParameterTool;

public class OrderEnrichmentJob {
    // Default configuration values
    private static final long DEFAULT_CUSTOMER_LOOKUP_TIMEOUT_MS = 60000L;
    private static final int DEFAULT_CUSTOMER_LOOKUP_CAPACITY = 100;
    private static final int DEFAULT_CUSTOMER_LOOKUP_THREADS = 4;
    private static final int DEFAULT_CUSTOMER_LOOKUP_CONNECTIONS_MAX = 8;

    private static final long DEFAULT_PRODUCT_LOOKUP_TIMEOUT_MS = 1000L;
    private static final int DEFAULT_PRODUCT_LOOKUP_CAPACITY = 20;
    private static final int DEFAULT_PRODUCT_LOOKUP_THREADS = 4;
    private static final int DEFAULT_PRODUCT_LOOKUP_CONNECTIONS_MAX = 8;
    public static volatile org.apache.flink.core.execution.JobClient runningTestJobClient = null;
    private static final Logger LOG = LoggerFactory.getLogger(OrderEnrichmentJob.class);
    private static final ObjectMapper MAPPER = new ObjectMapper();
    public static List<EnrichedOrder> TEST_RESULTS = new CopyOnWriteArrayList<>();
    
    // For testing - allows tests to provide a custom deserializer
    private static MapFunction<String, Order> customDeserializer = null;
    
    /**
     * Register a custom deserializer for testing
     */
    public static void registerCustomDeserializer(MapFunction<String, Order> deserializer) {
        customDeserializer = deserializer;
        LOG.info("Registered custom deserializer: {}", deserializer.getClass().getName());
    }

    /**
     * Main entry point for the Flink Order Enrichment job.
     * <p>
     * This job reads order data from Kafka, enriches it with customer and product details
     * from a PostgreSQL database, and then outputs the enriched orders.
     * It supports custom deserialization and a test sink for integration testing purposes.
     * </p>
     * <p>Required parameters (passed as command-line arguments, e.g., --key value):</p>
     * <ul>
     *   <li><code>kafka.bootstrap.servers</code>: Kafka bootstrap servers.</li>
     *   <li><code>orders.topic</code>: Kafka topic for orders.</li>
     *   <li><code>jdbc.url</code>: JDBC URL for the PostgreSQL database.</li>
     *   <li><code>jdbc.user</code>: JDBC username.</li>
     *   <li><code>jdbc.password</code>: JDBC password.</li>
     * </ul>
     * <p>Optional parameters:</p>
     * <ul>
     *   <li><code>group.id</code>: Kafka consumer group ID (default: flink-order-enrichment).</li>
     *   <li><code>use.custom.deserializer</code>: Boolean, true to use a registered custom deserializer (default: false).</li>
     *   <li><code>group.id</code>: Kafka consumer group ID (default: flink-order-enrichment).</li>
     *   <li><code>use.custom.deserializer</code>: Boolean, true to use a registered custom deserializer (default: false).</li>
     *   <li><code>use.test.sink</code>: Boolean, true to use a test sink that collects results in memory (default: false).</li>
     *   <li><code>customer.lookup.timeout.ms</code>: Timeout for customer lookup async operation in milliseconds (default: 60000).</li>
     *   <li><code>customer.lookup.capacity</code>: Capacity for customer lookup async operation (default: 100).</li>
     *   <li><code>customer.lookup.threads</code>: Number of threads for customer lookup database operations (default: 4).</li>
     *   <li><code>customer.lookup.connections.max</code>: Max DB connections for customer lookup (default: 8).</li>
     *   <li><code>product.lookup.timeout.ms</code>: Timeout for product lookup async operation in milliseconds (default: 1000).</li>
     *   <li><code>product.lookup.capacity</code>: Capacity for product lookup async operation (default: 20).</li>
     *   <li><code>product.lookup.threads</code>: Number of threads for product lookup database operations (default: 4).</li>
     *   <li><code>product.lookup.connections.max</code>: Max DB connections for product lookup (default: 8).</li>
     * </ul>
     *
     * @param args Command-line arguments for configuring the Flink job.
     * @throws Exception if the job execution fails or required parameters are missing.
     */
    public static void main(String[] args) throws Exception {
        // Parse parameters from command line args
        ParameterTool params = ParameterTool.fromArgs(args);

        LOG.info("Command line args: {}", String.join(", ", args));
        LOG.info("Parsed parameters: {}", params.toMap());

        // Configuration parameters
        boolean useCustomDeserializer = params.getBoolean("use.custom.deserializer", false);
        if (useCustomDeserializer && customDeserializer == null) {
            LOG.warn("Custom deserializer requested via 'use.custom.deserializer=true' but no deserializer was registered. Falling back to default.");
            useCustomDeserializer = false;
        } else if (useCustomDeserializer) {
            LOG.info("Using custom deserializer: {}", customDeserializer.getClass().getName());
        }

        String bootstrapServers = params.getRequired("kafka.bootstrap.servers");
        LOG.info("Using Kafka bootstrap servers: {}", bootstrapServers);

        String ordersTopic = params.getRequired("orders.topic");
        LOG.info("Using orders topic: {}", ordersTopic);

        String groupId = params.get("group.id", "flink-order-enrichment");
        LOG.info("Using Kafka group ID: {}", groupId);

        String jdbcUrl = params.getRequired("jdbc.url");
        LOG.info("Using JDBC URL: {}", jdbcUrl);

        String jdbcUser = params.getRequired("jdbc.user");
        LOG.info("Using JDBC user: {}", jdbcUser);

        String jdbcPass = params.getRequired("jdbc.password");
        LOG.info("Using JDBC password: [REDACTED]");

        boolean useTestSink = params.getBoolean("use.test.sink", false);
        LOG.info("Use test sink: {}", useTestSink);

        // Async operation parameters
        long customerLookupTimeoutMs = params.getLong("customer.lookup.timeout.ms", DEFAULT_CUSTOMER_LOOKUP_TIMEOUT_MS);
        int customerLookupCapacity = params.getInt("customer.lookup.capacity", DEFAULT_CUSTOMER_LOOKUP_CAPACITY);
        int customerLookupThreads = params.getInt("customer.lookup.threads", DEFAULT_CUSTOMER_LOOKUP_THREADS);
        int customerLookupConnectionsMax = params.getInt("customer.lookup.connections.max", DEFAULT_CUSTOMER_LOOKUP_CONNECTIONS_MAX);

        long productLookupTimeoutMs = params.getLong("product.lookup.timeout.ms", DEFAULT_PRODUCT_LOOKUP_TIMEOUT_MS);
        int productLookupCapacity = params.getInt("product.lookup.capacity", DEFAULT_PRODUCT_LOOKUP_CAPACITY);
        int productLookupThreads = params.getInt("product.lookup.threads", DEFAULT_PRODUCT_LOOKUP_THREADS);
        int productLookupConnectionsMax = params.getInt("product.lookup.connections.max", DEFAULT_PRODUCT_LOOKUP_CONNECTIONS_MAX);

        LOG.info("Customer Lookup: timeout={}ms, capacity={}, threads={}, connections={}", 
                 customerLookupTimeoutMs, customerLookupCapacity, customerLookupThreads, customerLookupConnectionsMax);
        LOG.info("Product Lookup: timeout={}ms, capacity={}, threads={}, connections={}", 
                 productLookupTimeoutMs, productLookupCapacity, productLookupThreads, productLookupConnectionsMax);

        // Set up the execution environment
        final StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.getConfig().registerKryoType(java.time.Instant.class);
        
        // Enable checkpointing for fault tolerance
        env.enableCheckpointing(5000); // Checkpoint every 5 seconds

        // Configure Kafka source for orders
        KafkaSource<String> ordersSource = KafkaSource.<String>builder()
                .setBootstrapServers(bootstrapServers)
                .setTopics(ordersTopic)
                .setGroupId(groupId)
                .setStartingOffsets(OffsetsInitializer.earliest())
                .setValueOnlyDeserializer(new SimpleStringSchema())
                .build();

        // Create input stream from Kafka with watermarks
        // Use the appropriate deserializer based on configuration
        DataStream<Order> orders;
        if (useCustomDeserializer) {
            LOG.info("Using custom deserializer for orders");
            orders = env.fromSource(ordersSource, WatermarkStrategy.noWatermarks(), "Orders Source")
                    .map(customDeserializer)
                    .map((Order o) -> { LOG.info("[Pipeline] After custom deserialization: {}", o); return o; })
                    .filter(o->o!=null && !o.isDeleted())
                    .map((Order o) -> { LOG.info("[Pipeline] After filter (custom deserializer path): {}", o); return o; });
        } else {
            LOG.info("Using default Debezium deserializer for orders");
            orders = env.fromSource(ordersSource, WatermarkStrategy.noWatermarks(), "Orders Source")
                    .map(new DebeziumOrderDeserializer())
                    .map((Order o) -> { LOG.info("[Pipeline] After default deserialization: {}", o); return o; })
                    .filter(o->o!=null && !o.isDeleted())
                    .map((Order o) -> { LOG.info("[Pipeline] After filter (default deserializer path): {}", o); return o; });
        }

        // Add async customer lookup using JDBC
        CustomerLookupFunction customerLookupFunction = new CustomerLookupFunction(jdbcUrl, jdbcUser, jdbcPass, customerLookupThreads, customerLookupConnectionsMax);
        
        DataStream<Order> ordersWithCustomer = AsyncDataStream.unorderedWait(
                orders,
                customerLookupFunction,
                customerLookupTimeoutMs, TimeUnit.MILLISECONDS,
                customerLookupCapacity);
        LOG.info("Added customer lookup function with timeout {}ms and capacity {}", customerLookupTimeoutMs, customerLookupCapacity);

        // Add async product lookup
        ProductLookupFunction productLookupFunction = new ProductLookupFunction(jdbcUrl, jdbcUser, jdbcPass, productLookupThreads, productLookupConnectionsMax);
        DataStream<Order> withProduct = AsyncDataStream.unorderedWait(
                ordersWithCustomer,
                productLookupFunction,
                productLookupTimeoutMs, 
                TimeUnit.MILLISECONDS,
                productLookupCapacity 
        );
        LOG.info("Added product lookup function with timeout {}ms and capacity {}", productLookupTimeoutMs, productLookupCapacity);

        // Process orders to create enriched orders
        DataStream<EnrichedOrder> enrichedOrders = withProduct
                .process(new OrderEnrichmentFunction());

        if (useTestSink) {
            TEST_RESULTS.clear();
            enrichedOrders.addSink(new SinkFunction<EnrichedOrder>() {
                @Override
                public void invoke(EnrichedOrder value, Context ctx) {
                    LOG.info("[Pipeline] TestSink: Received enriched order: {}", value);
                    TEST_RESULTS.add(value);
                }
            });
            org.apache.flink.core.execution.JobClient client = env.executeAsync("test-job");
            runningTestJobClient = client; // Make client available to the test
            try {
                client.getJobExecutionResult().get(); // Block here until job finishes or is cancelled by test
            } catch (Exception e) {
                // Log interruption, it's expected if test cancels the job
                if (e.getCause() instanceof java.util.concurrent.CancellationException || e instanceof InterruptedException) {
                    LOG.info("Test job execution was cancelled or interrupted as expected.", e);
                } else {
                    LOG.error("Test job execution failed with an unexpected error.", e);
                    throw e;
                }
            }
        } else {
            enrichedOrders.print();
            env.execute("Order Enrichment Job");
        }
    }
    
    /**
     * A Flink {@link org.apache.flink.api.common.functions.MapFunction} that deserializes Debezium CDC (Change Data Capture) messages 
     * from JSON strings into {@link com.goodrx.flink.model.Order} objects.
     * <p>
     * This deserializer is designed to be flexible and can handle two common Debezium message structures:
     * <ul>
     *   <li>Standard Debezium format: Extracts order data from the 'after' field, which
     *       represents the state of the row after the change.</li>
     *   <li>Custom/Legacy format: Extracts order data from a 'payload' field if 'after' is not present.
     *       This provides compatibility with potentially older or custom message producers.</li>
     * </ul>
     * It also inspects the Debezium operation type ('op' field):
     * <ul>
     *   <li>'c' (create), 'r' (read/snapshot), 'u' (update): The order is considered active.</li>
     *   <li>'d' (delete): The order is marked as deleted by setting the 'deleted' flag
     *       on the resulting {@code Order} object to {@code true}.</li>
     * </ul>
     * If the essential data fields ('after' or 'payload') are missing, or if parsing fails,
     * it logs a warning and may return {@code null} or an incomplete {@code Order} object,
     * depending on the specific parsing error and data availability.
     * </p>
     * <p>
     * <b>Example Input (Standard Debezium 'after' field):</b>
     * <pre>{@code
     * {
     *   "before": null,
     *   "after": {
     *     "id": 1001,
     *     "order_date": "2023-05-22T20:30:00Z",
     *     "purchaser": 1,
     *     "quantity": 2,
     *     "product_id": 101
     *   },
     *   "source": { ... },
     *   "op": "c",
     *   "ts_ms": 1621720800000,
     *   "transaction": null
     * }
     * }</pre>
     * <b>Example Output ({@code Order} object):</b>
     * <pre>{@code
     * Order{id=1001, orderDate=2023-05-22T20:30:00Z, purchaser=1, quantity=2, productId=101, deleted=false, ...}
     * }</pre>
     * </p>
     * <p>
     * <b>Example Input (Custom 'payload' field):</b>
     * <pre>{@code
     * {
     *   "payload": {
     *     "id": 1002,
     *     "order_date": "2023-05-23T10:15:00Z",
     *     "purchaser": 2,
     *     "quantity": 1,
     *     "product_id": 102
     *   },
     *   "op": "c",
     *   ...
     * }
     * }</pre>
     * <b>Example Output ({@code Order} object):</b>
     * <pre>{@code
     * Order{id=1002, orderDate=2023-05-23T10:15:00Z, purchaser=2, quantity=1, productId=102, deleted=false, ...}
     * }</pre>
     * </p>
     */
    public static class DebeziumOrderDeserializer implements MapFunction<String, Order> {
        private static final long serialVersionUID = 1L;
        private transient ObjectMapper mapper;
        
        @Override
        public Order map(String value) throws Exception {
            if (mapper == null) {
                mapper = new ObjectMapper();
            }
            
            try {
                LOG.debug("Deserializing message: {}", value);
                JsonNode jsonNode = mapper.readTree(value);
                
                // Try to extract the order data from either 'payload' or 'after' field
                JsonNode dataNode = jsonNode.path("after");
                if (dataNode.isMissingNode()) {
                    dataNode = jsonNode.path("payload");
                }

                if (dataNode.isMissingNode()) {
                    LOG.warn("No 'after' or 'payload' field found in message: {}", value);
                    return null; // Or throw an exception, depending on desired error handling
                }
                
                // Deserialize using @JsonCreator constructor in Order
                // This relies on @JsonProperty annotations in the Order constructor
                Order order = mapper.treeToValue(dataNode, Order.class);

                if (order == null) {
                    // This might happen if dataNode is empty or doesn't match Order structure
                    LOG.warn("Failed to deserialize dataNode into Order object. dataNode: {}", dataNode.toString());
                    return null;
                }

                // Handle Debezium 'op' field for deletes.
                // This 'op' field is part of the outer message envelope, not 'dataNode'.
                // If 'op' is 'd', the order should be marked as deleted,
                // potentially overriding any 'deleted' status from within 'dataNode'.
                JsonNode opNode = jsonNode.path("op");
                if (!opNode.isMissingNode() && "d".equals(opNode.asText())) {
                    if (!order.isDeleted()) { // Only create a new object if the state changes
                        order = order.withDeleted(true);
                    }
                }
                
                LOG.debug("Successfully deserialized order: {}", order);
                return order;
                
            } catch (Exception e) {
                LOG.error("Error deserializing order: " + value, e);
                throw e; // Rethrow to allow Flink to handle or log the error appropriately
            }
        }
    }

   
}
