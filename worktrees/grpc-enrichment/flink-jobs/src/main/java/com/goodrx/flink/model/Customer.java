package com.goodrx.flink.model;

import com.fasterxml.jackson.annotation.JsonProperty;

public class Customer {
    private Integer id;
    private String firstName;
    private String lastName;
    private String email;

    // Default constructor for Jackson
    public Customer() {}

    public Customer(Integer id, String firstName, String lastName, String email) {
        this.id = id;
        this.firstName = firstName;
        this.lastName = lastName;
        this.email = email;
    }

    // Getters and setters
    public Integer getId() { return id; }
    public void setId(Integer id) { this.id = id; }

    @JsonProperty("first_name")
    public String getFirstName() { return firstName; }
    public void setFirstName(String firstName) { this.firstName = firstName; }

    @JsonProperty("last_name")
    public String getLastName() { return lastName; }
    public void setLastName(String lastName) { this.lastName = lastName; }

    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }
    
    // Helper method to get full name
    public String getFullName() {
        return (firstName != null ? firstName + " " : "") + (lastName != null ? lastName : "");
    }

    @Override
    public String toString() {
        return String.format(
            "Customer{id=%d, name='%s %s', email='%s'}",
            id,
            firstName != null ? firstName : "",
            lastName != null ? lastName : "",
            email != null ? email : ""
        );
    }
}
