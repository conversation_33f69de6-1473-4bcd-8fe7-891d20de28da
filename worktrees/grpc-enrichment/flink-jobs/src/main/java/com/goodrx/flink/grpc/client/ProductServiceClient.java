package com.goodrx.flink.grpc.client;

import com.goodrx.flink.grpc.ProductBatchRequest;
import com.goodrx.flink.grpc.ProductBatchResponse;
import com.goodrx.flink.grpc.ProductRequest;
import com.goodrx.flink.grpc.ProductResponse;
import com.goodrx.flink.grpc.ProductServiceGrpc;
import io.grpc.ManagedChannel;
import io.grpc.StatusRuntimeException;
import io.grpc.stub.StreamObserver;

import java.util.concurrent.TimeUnit;
import java.util.logging.Level;
import java.util.logging.Logger;

// TODO: Implement robust backpressure mechanisms for streaming calls.
// TODO: Enhance error handling and retry logic (possibly via GrpcClientManager or interceptors).

/**
 * Client for interacting with the ProductService gRPC service.
 * This client wraps the generated gRPC stubs and uses GrpcClientManager to obtain channels.
 */
public class ProductServiceClient implements AutoCloseable {

    private static final Logger logger = Logger.getLogger(ProductServiceClient.class.getName());
    private final ManagedChannel channel;
    private final ProductServiceGrpc.ProductServiceBlockingStub blockingStub;
    private final ProductServiceGrpc.ProductServiceFutureStub futureStub;
    private final ProductServiceGrpc.ProductServiceStub asyncStub; // For client-side streaming or bi-di

    private final long defaultTimeoutSec;

    /**
     * Constructs a ProductServiceClient using a GrpcClientManager and a target string.
     *
     * @param clientManager The GrpcClientManager to obtain the channel from.
     * @param target The target address string (e.g., "hostname:port").
     * @param defaultTimeoutSec Default timeout in seconds for RPC calls.
     */
    public ProductServiceClient(GrpcClientManager clientManager, String target, long defaultTimeoutSec) {
        this.channel = clientManager.getChannel(target);
        this.blockingStub = ProductServiceGrpc.newBlockingStub(this.channel);
        this.futureStub = ProductServiceGrpc.newFutureStub(this.channel);
        this.asyncStub = ProductServiceGrpc.newStub(this.channel);
        this.defaultTimeoutSec = defaultTimeoutSec;
    }

    /**
     * Constructs a ProductServiceClient using a GrpcClientManager and a specific GrpcClientConfig.
     *
     * @param clientManager The GrpcClientManager to obtain the channel from.
     * @param config The GrpcClientConfig for the channel.
     * @param defaultTimeoutSec Default timeout in seconds for RPC calls.
     */
    public ProductServiceClient(GrpcClientManager clientManager, GrpcClientManager.GrpcClientConfig config, long defaultTimeoutSec) {
        this.channel = clientManager.getChannel(config);
        this.blockingStub = ProductServiceGrpc.newBlockingStub(this.channel);
        this.futureStub = ProductServiceGrpc.newFutureStub(this.channel);
        this.asyncStub = ProductServiceGrpc.newStub(this.channel);
        this.defaultTimeoutSec = defaultTimeoutSec;
    }

    public ProductServiceGrpc.ProductServiceFutureStub getFutureStub() {
        return futureStub;
    }

    public long getDefaultTimeoutSec() {
        return defaultTimeoutSec;
    }

    /**
     * Gets a single product by its ID. (Unary RPC - Blocking)
     *
     * @param productId The ID of the product to retrieve.
     * @return The ProductResponse.
     * @throws StatusRuntimeException if the RPC fails.
     */
    public ProductResponse getProduct(int productId) throws StatusRuntimeException {
        ProductRequest request = ProductRequest.newBuilder().setProductId(productId).build();
        logger.log(Level.INFO, "Requesting product with ID: {0}", productId);
        try {
            return blockingStub.withDeadlineAfter(defaultTimeoutSec, TimeUnit.SECONDS).getProduct(request);
        } catch (StatusRuntimeException e) {
            logger.log(Level.WARNING, "RPC failed: {0}, Request: {1}", new Object[]{e.getStatus(), request});
            throw e;
        }
    }

    /**
     * Gets multiple products in a batch. (Unary RPC - Blocking)
     *
     * @param batchRequest The ProductBatchRequest containing multiple product IDs.
     * @return The ProductBatchResponse.
     * @throws StatusRuntimeException if the RPC fails.
     */
    public ProductBatchResponse getProductsInBatch(ProductBatchRequest batchRequest) throws StatusRuntimeException {
        logger.log(Level.INFO, "Requesting products in batch. Count: {0}", batchRequest.getRequestsCount());
        try {
            return blockingStub.withDeadlineAfter(defaultTimeoutSec, TimeUnit.SECONDS).getProductsInBatch(batchRequest);
        } catch (StatusRuntimeException e) {
            logger.log(Level.WARNING, "RPC failed: {0}, Batch Request Item Count: {1}", new Object[]{e.getStatus(), batchRequest.getRequestsCount()});
            throw e;
        }
    }

    /**
     * Initiates a client-streaming call to get products.
     * The caller is responsible for sending ProductRequest messages via the returned StreamObserver
     * and handling responses via the provided responseObserver.
     *
     * @param responseObserver The observer to handle incoming ProductResponse messages from the server.
     * @return A StreamObserver to send ProductRequest messages to the server.
     */
    public StreamObserver<ProductRequest> getProductsStream(StreamObserver<ProductResponse> responseObserver) {
        logger.info("Initiating product stream.");
        // For client-streaming or bi-directional streaming, use the asyncStub.
        // This example assumes a client-streaming scenario where the client sends a stream of requests.
        // If it's server-streaming, the method signature would be different (request -> StreamObserver<Response>).
        // If it's bi-directional, it would be StreamObserver<Request> -> StreamObserver<Response>.
        // The .proto defines it as: rpc GetProductsStream (stream ProductRequest) returns (stream ProductResponse) {}
        // This is a bi-directional stream.
        return asyncStub.getProductsStream(responseObserver);
    }


    /**
     * Closes the client by shutting down the underlying channel.
     * Note: The channel is managed by GrpcClientManager. This method provides a way
     * to signal that this specific client instance is done, but the channel itself
     * might be shared and its lifecycle is ultimately controlled by GrpcClientManager.
     * For true "closing" of this client's resources, rely on GrpcClientManager.shutdownChannel(target)
     * or shutdownAll().
     *
     * This AutoCloseable implementation is more for try-with-resources convenience if the client
     * is used in a short-lived scope, but doesn't guarantee channel shutdown if shared.
     */
    @Override
    public void close() {
        // The channel lifecycle is managed by GrpcClientManager.
        // This method can be a no-op or could signal to the manager if needed.
        // For simplicity, we'll log that this client instance is "closed".
        logger.info("ProductServiceClient instance is being closed. Channel lifecycle managed by GrpcClientManager.");
    }

    // Example usage (optional, for testing or demonstration)
    public static void main(String[] args) {
        GrpcClientManager.GrpcClientConfig config = new GrpcClientManager.GrpcClientConfig("localhost", 50051, false);
        GrpcClientManager manager = new GrpcClientManager(config); // Default config for the manager

        try (ProductServiceClient client = new ProductServiceClient(manager, config, 5)) {
            // Test GetProduct
            try {
                ProductResponse product = client.getProduct(1);
                System.out.println("GetProduct Response: " + product.getName());
            } catch (StatusRuntimeException e) {
                System.err.println("GetProduct RPC failed: " + e.getStatus());
            }

            // Test GetProductsInBatch
            try {
                ProductBatchRequest batchRequest = ProductBatchRequest.newBuilder()
                        .addRequests(ProductRequest.newBuilder().setProductId(101))
                        .addRequests(ProductRequest.newBuilder().setProductId(102))
                        .build();
                ProductBatchResponse batchResponse = client.getProductsInBatch(batchRequest);
                System.out.println("GetProductsInBatch Response Count: " + batchResponse.getResponsesCount());
                if (batchResponse.getResponsesCount() > 0) {
                     System.out.println("First product in batch: " + batchResponse.getResponses(0).getName());
                }
            } catch (StatusRuntimeException e) {
                System.err.println("GetProductsInBatch RPC failed: " + e.getStatus());
            }

            // Test GetProductsStream (Bi-directional)
            System.out.println("\nTesting GetProductsStream (Bi-directional)...");
            StreamObserver<ProductResponse> responseObserver = new StreamObserver<ProductResponse>() {
                @Override
                public void onNext(ProductResponse value) {
                    System.out.println("Stream Response: " + value.getName() + " (ID: " + value.getId() + ")");
                }

                @Override
                public void onError(Throwable t) {
                    System.err.println("Stream Error: " + t.getMessage());
                }

                @Override
                public void onCompleted() {
                    System.out.println("Stream completed from server.");
                }
            };

            StreamObserver<ProductRequest> requestObserver = client.getProductsStream(responseObserver);
            try {
                for (int i = 1; i <= 3; i++) {
                    ProductRequest streamRequest = ProductRequest.newBuilder().setProductId(200 + i).build();
                    System.out.println("Sending stream request for product ID: " + streamRequest.getProductId());
                    requestObserver.onNext(streamRequest);
                    Thread.sleep(500); // Simulate some delay between requests
                }
            } catch (RuntimeException e) {
                requestObserver.onError(e);
                System.err.println("Error sending stream requests: " + e.getMessage());
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                System.err.println("Stream request sending interrupted.");
                requestObserver.onError(e);
            }
            requestObserver.onCompleted(); // Client is done sending requests

            // Wait for a bit for stream to complete (in a real app, manage this more robustly)
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }

        } finally {
            System.out.println("Shutting down GrpcClientManager...");
            manager.shutdownAll(); // Important to shut down the manager to close channels
        }
    }
}
