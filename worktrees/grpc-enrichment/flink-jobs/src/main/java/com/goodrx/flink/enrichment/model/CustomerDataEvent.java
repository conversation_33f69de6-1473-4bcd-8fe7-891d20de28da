package com.goodrx.flink.enrichment.model;

import com.goodrx.flink.grpc.Address; // Assuming this is the generated Address proto message
import java.util.List;
import java.util.ArrayList;

// Interfaces defined in GrpcCustomerLookupFunction, reproduced here for clarity.

interface CustomerInputEvent {
    int getCustomerId();
    // Potentially other common fields like eventId, timestamp
    String getEventId();
    long getEventTimestamp();
}

interface CustomerEnrichedEvent extends CustomerInputEvent {
    void setCustomerFirstName(String firstName);
    void setCustomerLastName(String lastName);
    void setCustomerEmail(String email);
    void setCustomerAddresses(List<Address> addresses); // Using the gRPC generated Address type
    void setCustomerEnrichmentError(String error);
    boolean hasCustomerEnrichmentError();
    String getCustomerEnrichmentError();
}

public class CustomerDataEvent implements CustomerEnrichedEvent {
    private String eventId;
    private long eventTimestamp;
    private int customerId;
    private String originalOrderDetails; // Example of other data

    // Enriched fields
    private String customerFirstName;
    private String customerLastName;
    private String customerEmail;
    private List<Address> customerAddresses;
    private String customerEnrichmentError;

    public CustomerDataEvent() {
        this.customerAddresses = new ArrayList<>();
    }

    public CustomerDataEvent(String eventId, long eventTimestamp, int customerId, String originalOrderDetails) {
        this.eventId = eventId;
        this.eventTimestamp = eventTimestamp;
        this.customerId = customerId;
        this.originalOrderDetails = originalOrderDetails;
        this.customerAddresses = new ArrayList<>();
    }

    // Getters for CustomerInputEvent
    @Override
    public String getEventId() { return eventId; }
    @Override
    public long getEventTimestamp() { return eventTimestamp; }
    @Override
    public int getCustomerId() { return customerId; }

    // Setters for CustomerInputEvent
    public void setEventId(String eventId) { this.eventId = eventId; }
    public void setEventTimestamp(long eventTimestamp) { this.eventTimestamp = eventTimestamp; }
    public void setCustomerId(int customerId) { this.customerId = customerId; }

    public String getOriginalOrderDetails() { return originalOrderDetails; }
    public void setOriginalOrderDetails(String originalOrderDetails) { this.originalOrderDetails = originalOrderDetails; }

    // Getters and Setters for CustomerEnrichedEvent
    @Override
    public void setCustomerFirstName(String firstName) { this.customerFirstName = firstName; }
    public String getCustomerFirstName() { return customerFirstName; }

    @Override
    public void setCustomerLastName(String lastName) { this.customerLastName = lastName; }
    public String getCustomerLastName() { return customerLastName; }

    @Override
    public void setCustomerEmail(String email) { this.customerEmail = email; }
    public String getCustomerEmail() { return customerEmail; }

    @Override
    public void setCustomerAddresses(List<Address> addresses) { this.customerAddresses = addresses; }
    public List<Address> getCustomerAddresses() { return customerAddresses; }

    @Override
    public void setCustomerEnrichmentError(String error) { this.customerEnrichmentError = error; }
    @Override
    public String getCustomerEnrichmentError() { return customerEnrichmentError; }
    @Override
    public boolean hasCustomerEnrichmentError() { return this.customerEnrichmentError != null && !this.customerEnrichmentError.isEmpty(); }

    @Override
    public String toString() {
        return "CustomerDataEvent{" +
                "eventId='" + eventId + '\'' +
                ", eventTimestamp=" + eventTimestamp +
                ", customerId=" + customerId +
                ", originalOrderDetails='" + originalOrderDetails + '\'' +
                ", customerFirstName='" + customerFirstName + '\'' +
                ", customerLastName='" + customerLastName + '\'' +
                ", customerEmail='" + customerEmail + '\'' +
                ", customerAddressesCount=" + (customerAddresses != null ? customerAddresses.size() : 0) +
                ", customerEnrichmentError='" + customerEnrichmentError + '\'' +
                '}';
    }
}
