package com.goodrx.flink.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import java.time.Instant;

@JsonIgnoreProperties(ignoreUnknown = true)
public class Order {
    private final Long id;
    private final Instant orderDate;
    private final Integer purchaser;
    private final Integer quantity;
    private final Integer productId;
    private final boolean deleted;
    private final transient Customer customer; // For enrichment
    private final transient Product product;   // For enrichment

    // Constructor for all fields (used by withers and copy constructor)
    private Order(Long id, Instant orderDate, Integer purchaser, Integer quantity, Integer productId, boolean deleted, Customer customer, Product product) {
        this.id = id;
        this.orderDate = orderDate;
        this.purchaser = purchaser;
        this.quantity = quantity;
        this.productId = productId;
        this.deleted = deleted;
        this.customer = customer;
        this.product = product;
    }

    // Primary constructor for initial creation (without enrichment)
    public Order(Long id, Instant orderDate, Integer purchaser, Integer quantity, Integer productId) {
        this(id, orderDate, purchaser, quantity, productId, false, null, null);
    }

    // Constructor for Jackson deserialization and easier testing with string date
    @JsonCreator
    public Order(
            @JsonProperty("id") Long id,
            @JsonProperty("order_date") String orderDateStr,
            @JsonProperty("purchaser") Integer purchaser,
            @JsonProperty("quantity") Integer quantity,
            @JsonProperty("product_id") Integer productId,
            @JsonProperty(value = "__deleted", defaultValue = "false") boolean deleted) {
        this.id = id;
        this.orderDate = parseOrderDate(orderDateStr);
        this.purchaser = purchaser;
        this.quantity = quantity;
        this.productId = productId;
        this.deleted = deleted;
        this.customer = null; // Initial deserialization won't have enriched data
        this.product = null;  // Initial deserialization won't have enriched data
    }

    private static Instant parseOrderDate(String orderDateStr) {
        if (orderDateStr == null) return null;
        try {
            return Instant.parse(orderDateStr);
        } catch (Exception e) {
            try {
                long micros = Long.parseLong(orderDateStr);
                return Instant.ofEpochMilli(micros / 1000);
            } catch (NumberFormatException ex) {
                return Instant.now(); // Fallback or consider throwing an error
            }
        }
    }

    // Copy constructor (delegates to the private all-args constructor)
    public Order(Order other) {
        this(other.id, other.orderDate, other.purchaser, other.quantity, other.productId, other.deleted, other.customer, other.product);
    }

    // Getters (no setters for immutability)
    public Long getId() { return id; }
    public Instant getOrderDate() { return orderDate; }
    public Integer getPurchaser() { return purchaser; }
    public Integer getQuantity() { return quantity; }
    public Integer getProductId() { return productId; }
    @JsonProperty("__deleted") // Keep for Jackson serialization if needed, or handle in constructor
    public boolean isDeleted() { return deleted; }
    public Customer getCustomer() { return customer; }
    public Product getProduct() { return product; }

    // Wither methods
    public Order withCustomer(Customer newCustomer) {
        return new Order(this.id, this.orderDate, this.purchaser, this.quantity, this.productId, this.deleted, newCustomer, this.product);
    }

    public Order withProduct(Product newProduct) {
        return new Order(this.id, this.orderDate, this.purchaser, this.quantity, this.productId, this.deleted, this.customer, newProduct);
    }

    public Order withDeleted(boolean newDeleted) {
        return new Order(this.id, this.orderDate, this.purchaser, this.quantity, this.productId, newDeleted, this.customer, this.product);
    }

    @Override
    public String toString() {
        return "Order{" +
                "id=" + id +
                ", orderDate=" + orderDate +
                ", purchaser=" + purchaser +
                ", quantity=" + quantity +
                ", productId=" + productId +
                ", deleted=" + deleted +
                ", customer=" + (customer != null ? customer.getFirstName() + " " + customer.getLastName() : "null") +
                ", product=" + (product != null ? product.getName() : "null") +
                '}';
    }
}
