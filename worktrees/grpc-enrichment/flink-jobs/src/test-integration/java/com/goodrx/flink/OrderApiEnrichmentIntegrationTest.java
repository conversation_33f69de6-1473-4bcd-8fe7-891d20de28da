package com.goodrx.flink;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.goodrx.flink.model.Customer;
import com.github.tomakehurst.wiremock.WireMockServer;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;

import static com.github.tomakehurst.wiremock.client.WireMock.*;
import static org.assertj.core.api.Assertions.assertThat;

/**
 * Simple integration test that verifies API-based customer enrichment.
 * Tests database reading and external API integration separately.
 */
@Testcontainers
public class OrderApiEnrichmentIntegrationTest {

    @Container
    private static final PostgreSQLContainer<?> POSTGRES = new PostgreSQLContainer<>("postgres:13")
        .withDatabaseName("inventory")
        .withUsername("postgres")
        .withPassword("postgres");

    private static WireMockServer wireMock;

    @BeforeAll
    static void startWireMock() {
        wireMock = new WireMockServer(8089);
        wireMock.start();
    }

    @AfterAll
    static void stopWireMock() {
        if (wireMock != null) {
            wireMock.stop();
        }
    }

    @BeforeEach
    void setUp() throws Exception {
        wireMock.resetAll();
        
        // Set up database
        try (Connection conn = DriverManager.getConnection(
                POSTGRES.getJdbcUrl(), POSTGRES.getUsername(), POSTGRES.getPassword());
             Statement stmt = conn.createStatement()) {
            
            stmt.execute("DROP TABLE IF EXISTS orders CASCADE");
            stmt.execute("CREATE TABLE orders (id SERIAL PRIMARY KEY, purchaser INT)");
            stmt.execute("INSERT INTO orders (purchaser) VALUES (1)");
        }
    }

    @Test
    void shouldEnrichOrderWithApiCustomer() throws Exception {
        // Mock the API response
        wireMock.stubFor(get(urlEqualTo("/api/customers/1"))
            .willReturn(aResponse()
                .withHeader("Content-Type", "application/json")
                .withBody("{\"id\":1,\"first_name\":\"Test\",\"last_name\":\"User\"}"))
        );

        // Read purchaser ID from database
        int purchaserId;
        try (Connection conn = DriverManager.getConnection(
                POSTGRES.getJdbcUrl(), POSTGRES.getUsername(), POSTGRES.getPassword());
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery("SELECT purchaser FROM orders WHERE id=1")) {
            
            assertThat(rs.next()).isTrue();
            purchaserId = rs.getInt(1);
        }

        // Call the API
        HttpClient client = HttpClient.newHttpClient();
        HttpRequest request = HttpRequest.newBuilder()
            .uri(new URI("http://localhost:8089/api/customers/" + purchaserId))
            .GET()
            .build();
        
        HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
        
        // Verify the response
        assertThat(response.statusCode()).isEqualTo(200);
        
        ObjectMapper mapper = new ObjectMapper();
        Customer customer = mapper.readValue(response.body(), Customer.class);
        
        assertThat(customer).isNotNull();
        assertThat(customer.getId()).isEqualTo(1);
        assertThat(customer.getFirstName()).isEqualTo("Test");
        assertThat(customer.getLastName()).isEqualTo("User");
    }
}
