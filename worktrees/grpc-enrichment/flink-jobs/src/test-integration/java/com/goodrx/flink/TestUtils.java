package com.goodrx.flink;

import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.NewTopic;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testcontainers.containers.KafkaContainer;

import java.util.Collections;
import java.util.Properties;
import java.util.concurrent.TimeUnit;

/**
 * Simplified utility methods for integration tests.
 */
public class TestUtils {
    private static final Logger LOG = LoggerFactory.getLogger(TestUtils.class);

    /**
     * Create a Kafka topic if it doesn't exist.
     */
    public static void createKafkaTopic(KafkaContainer kafka, String topicName) throws Exception {
        Properties props = new Properties();
        props.put("bootstrap.servers", kafka.getBootstrapServers());
        
        try (AdminClient adminClient = AdminClient.create(props)) {
            NewTopic newTopic = new NewTopic(topicName, 1, (short) 1);
            adminClient.createTopics(Collections.singleton(newTopic))
                      .all()
                      .get(10, TimeUnit.SECONDS);
            LOG.info("Created Kafka topic: {}", topicName);
        }
    }

    /**
     * Send a test order message to Kafka.
     */
    public static void sendTestOrder(KafkaContainer kafka, String topic, String orderJson) throws Exception {
        Properties props = new Properties();
        props.put("bootstrap.servers", kafka.getBootstrapServers());  
        props.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        props.put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        props.put("acks", "all");

        try (KafkaProducer<String, String> producer = new KafkaProducer<>(props)) {
            ProducerRecord<String, String> record = new ProducerRecord<>(topic, "test-key", orderJson);
            producer.send(record).get(5, TimeUnit.SECONDS);
            LOG.info("Sent test order to topic: {}", topic);
        }
    }
}
