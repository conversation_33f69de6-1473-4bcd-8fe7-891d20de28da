package com.goodrx.flink;

import com.goodrx.flink.model.Customer;
import com.goodrx.flink.model.EnrichedOrder;
import com.goodrx.flink.model.Order;  
import com.goodrx.flink.model.Product;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.Statement;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Simple integration test focusing on database connectivity and basic enrichment logic.
 * This test verifies the core components work together without complex Kafka streaming.
 */
@Testcontainers
public class SimpleIntegrationTest {
    private static final Logger LOG = LoggerFactory.getLogger(SimpleIntegrationTest.class);

    @Container
    private static final PostgreSQLContainer<?> POSTGRES = new PostgreSQLContainer<>("postgres:13")
        .withDatabaseName("inventory")
        .withUsername("postgres")
        .withPassword("postgres");

    @BeforeEach
    void setUp() throws Exception {
        initializeDatabase();
    }

    @Test
    void testCustomerLookupFunction() throws Exception {
        LOG.info("=== Testing Customer Lookup Function ===");
        

        
        // Test synchronous lookup (simplified for testing)
        try (Connection conn = DriverManager.getConnection(
                POSTGRES.getJdbcUrl(), POSTGRES.getUsername(), POSTGRES.getPassword())) {
            
            var stmt = conn.prepareStatement("SELECT id, first_name, last_name, email FROM customers WHERE id = ?");
            stmt.setInt(1, 1);
            var rs = stmt.executeQuery();
            
            assertThat(rs.next()).isTrue();
            
            Customer customer = new Customer(
                rs.getInt("id"),
                rs.getString("first_name"),
                rs.getString("last_name"),
                rs.getString("email")
            );
            
            // Verify customer data
            assertThat(customer.getId()).isEqualTo(1);
            assertThat(customer.getFirstName()).isEqualTo("John");
            assertThat(customer.getLastName()).isEqualTo("Doe");
            assertThat(customer.getEmail()).isEqualTo("<EMAIL>");
            
            LOG.info("Customer lookup test completed successfully: {}", customer);
        }
    }

    @Test
    void testProductLookupFunction() throws Exception {
        LOG.info("=== Testing Product Lookup Function ===");
        
        // Test synchronous product lookup
        try (Connection conn = DriverManager.getConnection(
                POSTGRES.getJdbcUrl(), POSTGRES.getUsername(), POSTGRES.getPassword())) {
            
            var stmt = conn.prepareStatement("SELECT id, name, description, weight FROM products WHERE id = ?");
            stmt.setInt(1, 1);
            var rs = stmt.executeQuery();
            
            assertThat(rs.next()).isTrue();
            
            Double weight = rs.getDouble("weight");
            if (rs.wasNull()) {
                weight = null;
            }
            Product product = new Product(
                rs.getInt("id"),
                rs.getString("name"),
                rs.getString("description"),
                weight
            );
            
            // Verify product data
            assertThat(product.getId()).isEqualTo(1);
            assertThat(product.getName()).isEqualTo("Test Product");
            assertThat(product.getDescription()).isEqualTo("A test product");
            assertThat(product.getWeight()).isEqualTo(1.5);
            
            LOG.info("Product lookup test completed successfully: {}", product);
        }
    }

    @Test
    void testOrderEnrichment() throws Exception {
        LOG.info("=== Testing Order Enrichment Logic ===");
        
        // Create test data
        // Constructor: Order(id, orderDateStr, purchaser, quantity, productId, deleted)
        Order initialOrder = new Order(1001L, "2023-05-22T20:30:00Z", 1, 2, 1, false);
        Customer customer = new Customer(1, "John", "Doe", "<EMAIL>");
        Product product = new Product(1, "Test Product", "A test product", 1.5);
        
        // Set enrichment data on order using wither methods
        final Order orderWithEnrichments = initialOrder.withCustomer(customer).withProduct(product);
        
        // Create enriched order
        EnrichedOrder enrichedOrder = new EnrichedOrder(orderWithEnrichments, customer, product);
        // The 'orderCount' field was removed from EnrichedOrder, assuming it was part of an older design.
        // If it's still needed, EnrichedOrder's constructor and fields would need to be updated.
        
        // Verify enrichment
        assertThat(enrichedOrder.getId()).isEqualTo(1001L);
        assertThat(enrichedOrder.getCustomerFirstName()).isEqualTo("John");
        assertThat(enrichedOrder.getCustomerLastName()).isEqualTo("Doe");
        assertThat(enrichedOrder.getCustomerEmail()).isEqualTo("<EMAIL>");
        assertThat(enrichedOrder.getProductName()).isEqualTo("Test Product");
        assertThat(enrichedOrder.getProductDescription()).isEqualTo("A test product");
        // assertThat(enrichedOrder.getOrderCount()).isEqualTo(1); // orderCount field removed
        
        LOG.info("Order enrichment test completed successfully: {}", enrichedOrder);
    }

    @Test
    void testDebeziumOrderDeserializer() throws Exception {
        LOG.info("=== Testing Debezium Order Deserializer ===");
        
        OrderEnrichmentJob.DebeziumOrderDeserializer deserializer = 
            new OrderEnrichmentJob.DebeziumOrderDeserializer();
        
        // Test with Debezium format
        String debeziumJson = """
            {
                "after": {
                    "id": 1001,
                    "order_date": "2023-05-22T20:30:00Z",
                    "purchaser": 1,
                    "quantity": 2,
                    "product_id": 1
                },
                "op": "c"
            }""";
        
        Order order = deserializer.map(debeziumJson);
        
        assertThat(order).isNotNull();
        assertThat(order.getId()).isEqualTo(1001L);
        assertThat(order.getPurchaser()).isEqualTo(1);
        assertThat(order.getQuantity()).isEqualTo(2);
        assertThat(order.getProductId()).isEqualTo(1);
        assertThat(order.isDeleted()).isFalse();
        
        LOG.info("Debezium deserializer test completed successfully: {}", order);
    }

    private void initializeDatabase() throws Exception {
        try (Connection conn = DriverManager.getConnection(
                POSTGRES.getJdbcUrl(), POSTGRES.getUsername(), POSTGRES.getPassword());
             Statement stmt = conn.createStatement()) {

            // Drop and recreate tables for clean state
            stmt.execute("DROP TABLE IF EXISTS customers CASCADE");
            stmt.execute("DROP TABLE IF EXISTS products CASCADE");

            // Create schema
            stmt.execute("""
                CREATE TABLE customers (
                    id INT PRIMARY KEY,
                    first_name TEXT,
                    last_name TEXT,
                    email TEXT
                )""");

            stmt.execute("""
                CREATE TABLE products (
                    id INT PRIMARY KEY,
                    name TEXT,
                    description TEXT,
                    weight DOUBLE PRECISION
                )""");

            // Insert test data
            stmt.execute("""
                INSERT INTO customers (id, first_name, last_name, email)
                VALUES (1, 'John', 'Doe', '<EMAIL>')""");

            stmt.execute("""
                INSERT INTO products (id, name, description, weight)
                VALUES (1, 'Test Product', 'A test product', 1.5)""");

            LOG.info("Database initialized with test data");
        }
    }
}
