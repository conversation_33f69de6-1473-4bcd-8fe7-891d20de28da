package com.goodrx.flink;

import com.goodrx.flink.model.EnrichedOrder;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testcontainers.containers.KafkaContainer;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.testcontainers.utility.DockerImageName;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.Statement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.Duration;
import java.util.List; // Added import
import java.util.Properties;

import static org.awaitility.Awaitility.await;
import static org.assertj.core.api.Assertions.assertThat;

/**
 * Simplified integration test using MiniCluster for better reliability.
 * Tests the complete OrderEnrichmentJob end-to-end with real Kafka and PostgreSQL.
 */
@Testcontainers
class OrderEnrichmentJobIntegrationTest {
    private static final Logger LOG = LoggerFactory.getLogger(OrderEnrichmentJobIntegrationTest.class);

    // Using simplified approach without MiniCluster for more reliable tests

    @Container
    private static final KafkaContainer KAFKA = new KafkaContainer(
        DockerImageName.parse("confluentinc/cp-kafka:7.4.0"))
        .withReuse(false);

    @Container
    private static final PostgreSQLContainer<?> POSTGRES = new PostgreSQLContainer<>("postgres:13")
        .withDatabaseName("inventory")
        .withUsername("postgres")
        .withPassword("postgres")
        .withReuse(false);

    private static final String ORDERS_TOPIC = "test-orders-integration";

    @BeforeEach
    void setUp() throws Exception {
        // Clear any previous test results
        com.goodrx.flink.enrichment.OrderGrpcEnrichmentJob.TEST_RESULTS.clear();
        
        // Initialize database
        initializeDatabase();
        
        LOG.info("Test setup complete - Kafka: {}, PostgreSQL: {}", 
            KAFKA.getBootstrapServers(), POSTGRES.getJdbcUrl());
    }

    @AfterEach
    void tearDown() {
        com.goodrx.flink.enrichment.OrderGrpcEnrichmentJob.TEST_RESULTS.clear();
    }

    @Test
    void shouldProcessOrderEndToEnd() throws Exception {
        LOG.info("=== Starting End-to-End Integration Test ===");

        // Send test message to Kafka
        sendMultipleTestOrdersToKafka();

        // Configure and run the OrderEnrichmentJob
        String[] args = {
            "--kafka.bootstrap.servers", KAFKA.getBootstrapServers(),
            "--orders.topic", ORDERS_TOPIC,
            "--jdbc.url", POSTGRES.getJdbcUrl(),
            "--jdbc.user", POSTGRES.getUsername(),
            "--jdbc.password", POSTGRES.getPassword(),
            "--use.test.sink", "true"
        };

        LOG.info("Performing pre-job database health check...");
        try (Connection conn = DriverManager.getConnection(
                POSTGRES.getJdbcUrl(), POSTGRES.getUsername(), POSTGRES.getPassword());
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery("SELECT 1")) {
            if (rs.next()) {
                LOG.info("Pre-job database health check successful. Result: {}", rs.getInt(1));
            } else {
                LOG.error("Pre-job database health check failed: Query returned no results.");
                throw new RuntimeException("Pre-job DB health check failed.");
            }
        } catch (SQLException e) { // Use imported SQLException
            LOG.error("Pre-job database health check failed with SQLException.", e);
            throw new RuntimeException("Pre-job DB health check failed.", e);
        }
        LOG.info("Pre-job database health check complete.");

        // Run the job in a separate thread
        Thread jobThread = new Thread(() -> {
            try {
                com.goodrx.flink.enrichment.OrderGrpcEnrichmentJob.main(args);
            } catch (Exception e) {
                LOG.error("Job execution failed", e);
                throw new RuntimeException(e);
            }
        });
        
        jobThread.setDaemon(true);
        jobThread.start();

        // Wait for the job to process the message
        await().atMost(Duration.ofSeconds(60)) // Increased timeout for more data
               .pollInterval(Duration.ofSeconds(3)) // Polling interval
               .untilAsserted(() -> {
                   LOG.info("Checking results. Current size: {}", com.goodrx.flink.enrichment.OrderGrpcEnrichmentJob.TEST_RESULTS.size());
                   assertThat(com.goodrx.flink.enrichment.OrderGrpcEnrichmentJob.TEST_RESULTS).hasSize(5); // Expect 5 orders (2001, 2002, 2003, 2004, 2005)
               });

        // Verify the enriched orders
        List<EnrichedOrder> results = com.goodrx.flink.enrichment.OrderGrpcEnrichmentJob.TEST_RESULTS;
        results.sort(java.util.Comparator.comparingLong(EnrichedOrder::getId)); // Sort by ID for consistent assertion order

        // Order 1: ID 2001, Purchaser 1 (John Doe), Product 101 (Laptop)
        EnrichedOrder order2001 = results.stream().filter(r -> r.getId() == 2001L).findFirst().orElseThrow(() -> new AssertionError("Order 2001 not found"));
        assertThat(order2001.getPurchaserId()).isEqualTo(1);
        assertThat(order2001.getCustomerFirstName()).isEqualTo("John");
        assertThat(order2001.getCustomerLastName()).isEqualTo("Doe");
        assertThat(order2001.getProductName()).isEqualTo("Laptop");
        LOG.info("Verified Order 2001: {}", order2001);

        // Order 2: ID 2002, Purchaser 2 (Jane Smith), Product 102 (Mouse)
        EnrichedOrder order2002 = results.stream().filter(r -> r.getId() == 2002L).findFirst().orElseThrow(() -> new AssertionError("Order 2002 not found"));
        assertThat(order2002.getPurchaserId()).isEqualTo(2);
        assertThat(order2002.getCustomerFirstName()).isEqualTo("Jane");
        assertThat(order2002.getCustomerLastName()).isEqualTo("Smith");
        assertThat(order2002.getProductName()).isEqualTo("Mouse");
        LOG.info("Verified Order 2002: {}", order2002);

        // Order 3: ID 2003, Purchaser 1 (John Doe), Product 103 (Keyboard)
        EnrichedOrder order2003 = results.stream().filter(r -> r.getId() == 2003L).findFirst().orElseThrow(() -> new AssertionError("Order 2003 not found"));
        assertThat(order2003.getPurchaserId()).isEqualTo(1);
        assertThat(order2003.getCustomerFirstName()).isEqualTo("John");
        assertThat(order2003.getCustomerLastName()).isEqualTo("Doe");
        assertThat(order2003.getProductName()).isEqualTo("Keyboard");
        LOG.info("Verified Order 2003: {}", order2003);

        // Order 4: ID 2004, Purchaser 2 (Jane Smith), Product 101 (Laptop)
        EnrichedOrder order2004 = results.stream().filter(r -> r.getId() == 2004L).findFirst().orElseThrow(() -> new AssertionError("Order 2004 not found"));
        assertThat(order2004.getPurchaserId()).isEqualTo(2);
        assertThat(order2004.getCustomerFirstName()).isEqualTo("Jane");
        assertThat(order2004.getCustomerLastName()).isEqualTo("Smith");
        assertThat(order2004.getProductName()).isEqualTo("Laptop");
        LOG.info("Verified Order 2004: {}", order2004);

        // Order 5: ID 2005, Purchaser 1 (John Doe), Product 999 (Non-existent)
        EnrichedOrder order2005 = results.stream().filter(r -> r.getId() == 2005L).findFirst().orElseThrow(() -> new AssertionError("Order 2005 not found"));
        assertThat(order2005.getPurchaserId()).isEqualTo(1);
        assertThat(order2005.getCustomerFirstName()).isEqualTo("John"); // Customer lookup should still work
        assertThat(order2005.getCustomerLastName()).isEqualTo("Doe");
        assertThat(order2005.getProductName()).isNull(); // Product lookup should fail, resulting in null
        assertThat(order2005.getProductDescription()).isNull();
        LOG.info("Verified Order 2005 (non-existent product): {}", order2005);

        LOG.info("All test assertions completed successfully.");
        
        // Clean up: Gracefully cancel the Flink job
        if (OrderEnrichmentJob.runningTestJobClient != null) {
            try {
                LOG.info("Attempting to cancel Flink job with ID: {}", OrderEnrichmentJob.runningTestJobClient.getJobID());
                // It's important to wait for the cancellation to complete.
                // The timeout here should be generous enough for Flink to shut down.
                OrderEnrichmentJob.runningTestJobClient.cancel().get(60, java.util.concurrent.TimeUnit.SECONDS);
                LOG.info("Flink job successfully cancelled.");
            } catch (Exception e) {
                LOG.warn("Error while cancelling Flink job or timeout occurred", e);
                // Fallback to interrupt if cancellation fails or times out,
                // though this might still lead to issues if Flink is stuck.
                jobThread.interrupt();
            }
        } else {
            LOG.warn("runningTestJobClient was null, attempting to interrupt job thread directly.");
            jobThread.interrupt(); // Fallback
        }

        // Wait for the job thread to finish
        try {
            jobThread.join(Duration.ofSeconds(30).toMillis());
            if (jobThread.isAlive()) {
                LOG.warn("Job thread is still alive after cancellation and join timeout. Forcing interrupt.");
                jobThread.interrupt();
            }
        } catch (InterruptedException e) {
            LOG.warn("Interrupted while waiting for job thread to join.");
            Thread.currentThread().interrupt();
        }
        LOG.info("Job thread finished.");
    }

    private void initializeDatabase() throws Exception {
        try (Connection conn = DriverManager.getConnection(
                POSTGRES.getJdbcUrl(), 
                POSTGRES.getUsername(), 
                POSTGRES.getPassword());
             Statement stmt = conn.createStatement()) {

            // Create schema
            stmt.execute("""
                CREATE TABLE IF NOT EXISTS customers (
                    id SERIAL PRIMARY KEY,
                    first_name VARCHAR(255) NOT NULL,
                    last_name VARCHAR(255) NOT NULL,
                    email VARCHAR(255) UNIQUE NOT NULL
                )""");

            stmt.execute("""
                CREATE TABLE IF NOT EXISTS products (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    description TEXT,
                    weight DOUBLE PRECISION
                )""");

            // Insert test data
            stmt.execute("""
                INSERT INTO customers (id, first_name, last_name, email)
                VALUES (1, 'John', 'Doe', '<EMAIL>'),
                       (2, 'Jane', 'Smith', '<EMAIL>')
                ON CONFLICT (id) DO NOTHING;""");

            stmt.execute("""
                INSERT INTO products (id, name, description, weight)
                VALUES (101, 'Laptop', 'High-end laptop', 1.2),
                       (102, 'Mouse', 'Wireless mouse', 0.2),
                       (103, 'Keyboard', 'Mechanical keyboard', 0.8)
                ON CONFLICT (id) DO NOTHING;""");

            LOG.info("Database initialized with test data");
        }
    }

    private void sendMultipleTestOrdersToKafka() throws Exception {
        Properties props = new Properties();
        props.put("bootstrap.servers", KAFKA.getBootstrapServers());
        props.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        props.put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");

        try (var producer = new org.apache.kafka.clients.producer.KafkaProducer<String, String>(props)) {
            String order1Json = """
                {
                    "after": {"id": 2001, "order_date": "2023-05-23T10:00:00Z", "purchaser": 1, "quantity": 1, "product_id": 101, "deleted": false},
                    "op": "c"
                }""";
            producer.send(new org.apache.kafka.clients.producer.ProducerRecord<>(ORDERS_TOPIC, "order2001", order1Json)).get();

            String order2Json = """
                {
                    "after": {"id": 2002, "order_date": "2023-05-23T10:05:00Z", "purchaser": 2, "quantity": 2, "product_id": 102, "deleted": false},
                    "op": "c"
                }""";
            producer.send(new org.apache.kafka.clients.producer.ProducerRecord<>(ORDERS_TOPIC, "order2002", order2Json)).get();

            String order3Json = """
                {
                    "after": {"id": 2003, "order_date": "2023-05-23T10:10:00Z", "purchaser": 1, "quantity": 3, "product_id": 103, "deleted": false},
                    "op": "c"
                }""";
            producer.send(new org.apache.kafka.clients.producer.ProducerRecord<>(ORDERS_TOPIC, "order2003", order3Json)).get();

            String order4DeletedJson = """
                {
                    "after": {"id": 2004, "order_date": "2023-05-23T10:15:00Z", "purchaser": 2, "quantity": 1, "product_id": 101, "deleted": true},
                    "op": "u" 
                }"""; // Using 'u' for update to represent a soft delete, or 'd' if hard delete
            producer.send(new org.apache.kafka.clients.producer.ProducerRecord<>(ORDERS_TOPIC, "order2004", order4DeletedJson)).get();

            String order5NonExistentProductJson = """
                {
                    "after": {"id": 2005, "order_date": "2023-05-23T10:20:00Z", "purchaser": 1, "quantity": 1, "product_id": 999, "deleted": false},
                    "op": "c"
                }""";
            producer.send(new org.apache.kafka.clients.producer.ProducerRecord<>(ORDERS_TOPIC, "order2005", order5NonExistentProductJson)).get();

            LOG.info("Multiple test orders sent to Kafka topic: {}", ORDERS_TOPIC);
        }
    }
}
