package com.goodrx.flink.enrichment;

import com.goodrx.flink.enrichment.OrderGrpcEnrichmentJob;
import com.goodrx.flink.enrichment.model.EnrichedOrderEvent;
import com.goodrx.flink.enrichment.model.OrderEvent;
// Assuming your generated gRPC classes are in a package like com.goodrx.protos.product
// import com.goodrx.protos.product.ProductRequest;
// import com.goodrx.protos.product.ProductResponse;
// import com.goodrx.protos.product.ProductServiceGrpc;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import io.grpc.Server;
import io.grpc.ServerServiceDefinition;
import io.grpc.inprocess.InProcessChannelBuilder;
import io.grpc.inprocess.InProcessServerBuilder;
import io.grpc.stub.StreamObserver;
import io.grpc.testing.GrpcCleanupRule;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.connector.kafka.source.reader.deserializer.KafkaRecordDeserializationSchema;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchemaBuilder;
import org.apache.flink.formats.json.JsonSerializationSchema;


import org.junit.Rule;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testcontainers.containers.KafkaContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.testcontainers.utility.DockerImageName;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;

import java.nio.file.Path;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Properties;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;

/**
 * Integration test for {@link OrderGrpcEnrichmentJob}.
 * Tests the end-to-end pipeline including Kafka consumption, gRPC enrichment, and Kafka sinking.
 */
@Testcontainers
public class OrderGrpcEnrichmentJobIntegrationTest {

    private static final Logger LOG = LoggerFactory.getLogger(OrderGrpcEnrichmentJobIntegrationTest.class);
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper().registerModule(new JavaTimeModule());

    private static final String INPUT_TOPIC = "test-grpc-orders-input-" + UUID.randomUUID();
    private static final String OUTPUT_TOPIC = "test-grpc-orders-output-" + UUID.randomUUID();
    private static final String CONSUMER_GROUP_ID = "test-grpc-enrichment-consumer-" + UUID.randomUUID();

    @Rule
    public final GrpcCleanupRule grpcCleanup = new GrpcCleanupRule();

    @Container
    public static final KafkaContainer KAFKA_CONTAINER = new KafkaContainer(
            DockerImageName.parse("confluentinc/cp-kafka:7.4.0"))
            .withReuse(true); // Set to true if you want Kafka to persist between test methods/classes

    private Server grpcServer;
    private String grpcServerName;
    private static MockProductService mockProductServiceInstance; // To control mock behavior

    // TODO: IMPORTANT - Replace these placeholder gRPC classes (com.goodrx.protos.product.*) 
    // with imports of your ACTUAL generated gRPC classes from your .proto files.
    // The MockProductService and its usage below will need to be updated accordingly.
    private static class MockProductService extends com.goodrx.flink.grpc.ProductServiceGrpc.ProductServiceImplBase { 
        @Override
        public void getProduct(com.goodrx.flink.grpc.ProductRequest request, 
                               StreamObserver<com.goodrx.flink.grpc.ProductResponse> responseObserver) {
            int productId = request.getProductId(); 
            LOG.info("######## MockProductService received request for productId: {} ########", productId);
            
            com.goodrx.flink.grpc.ProductResponse response;
            if (productId == 999) {
                responseObserver.onError(io.grpc.Status.INTERNAL.withDescription("Simulated gRPC error").asRuntimeException());
                return;
            }
            if (productId == 888) {
                 response = com.goodrx.flink.grpc.ProductResponse.newBuilder().setId(productId).setName("N/A - Not Found").build();
            } else {
                 response = com.goodrx.flink.grpc.ProductResponse.newBuilder().setId(productId).setName("Mock Product Name for " + productId).build(); // Simplified
            }
            responseObserver.onNext(response);
            responseObserver.onCompleted();
        }
    }

    @BeforeEach
    public void setUp() throws Exception {
        KAFKA_CONTAINER.start(); 

        grpcServerName = InProcessServerBuilder.generateName();
        mockProductServiceInstance = new MockProductService();

        grpcServer = grpcCleanup.register(InProcessServerBuilder
                .forName(grpcServerName).directExecutor()
                .addService(mockProductServiceInstance)
                .build().start());

        LOG.info("Kafka started at: {}", KAFKA_CONTAINER.getBootstrapServers());
        LOG.info("Mock gRPC server started in-process with name: {}", grpcServerName);

        CollectSink.values.clear();
    }

    @AfterEach
    public void tearDown() throws Exception {
        if (grpcServer != null && !grpcServer.isShutdown()) {
            grpcServer.shutdownNow();
            grpcServer.awaitTermination(5, TimeUnit.SECONDS);
        }
        CollectSink.values.clear();
        LOG.info("Mock gRPC server stopped.");
    }

    @Test
    public void shouldEnrichOrdersViaGrpcEndToEnd() throws Exception {
        LOG.info("=== Starting gRPC End-to-End Integration Test ===");
        LOG.info("Input Topic: {}, Output Topic: {}", INPUT_TOPIC, OUTPUT_TOPIC);

        try (KafkaProducer<String, String> producer = createKafkaProducer()) {
            OrderEvent order1 = new OrderEvent("order1", "user1", "product1", 1, 10.0, Instant.now().toEpochMilli());
            OrderEvent order2 = new OrderEvent("order2", "user2", "product2", 1, 20.0, Instant.now().toEpochMilli());
            OrderEvent orderError = new OrderEvent("orderError", "userError", "error_trigger", 1, 30.0, Instant.now().toEpochMilli());
            OrderEvent orderNotFound = new OrderEvent("orderNotFound", "userNotFound", "not_found", 1, 40.0, Instant.now().toEpochMilli());

            producer.send(new ProducerRecord<>(INPUT_TOPIC, order1.getOrderId(), OBJECT_MAPPER.writeValueAsString(order1))).get();
            producer.send(new ProducerRecord<>(INPUT_TOPIC, order2.getOrderId(), OBJECT_MAPPER.writeValueAsString(order2))).get();
            producer.send(new ProducerRecord<>(INPUT_TOPIC, orderError.getOrderId(), OBJECT_MAPPER.writeValueAsString(orderError))).get();
            producer.send(new ProducerRecord<>(INPUT_TOPIC, orderNotFound.getOrderId(), OBJECT_MAPPER.writeValueAsString(orderNotFound))).get();
            LOG.info("Sent 4 test messages to Kafka topic: {}", INPUT_TOPIC);
        }
        
        // Clear any previous results
        CollectSink.values.clear();
        
        // Register our CollectSink with the OrderGrpcEnrichmentJob's sink registry
        OrderGrpcEnrichmentJob.clearSinks();
        OrderGrpcEnrichmentJob.registerSink("enrichedOrderEventSink", new CollectSink());

        String[] jobArgs = {
            "--kafka.source.bootstrap.servers", KAFKA_CONTAINER.getBootstrapServers(),
            "--kafka.source.topic", INPUT_TOPIC,
            "--kafka.source.group.id", CONSUMER_GROUP_ID,
            "--kafka.sink.bootstrap.servers", KAFKA_CONTAINER.getBootstrapServers(),
            "--kafka.sink.topic", OUTPUT_TOPIC,
            "--product.service.host", grpcServerName,
            "--product.service.port", "-1",
            "--product.service.use.tls", "false",
            "--job.name", "TestOrderGrpcEnrichmentJob",
            "--use.test.sink", "true"
        };

        Thread jobThread = new Thread(() -> {
            try {
                OrderGrpcEnrichmentJob.main(jobArgs);
            } catch (Exception e) {
                LOG.error("Job execution failed in test thread", e);
            }
        });
        jobThread.setDaemon(true);
        jobThread.start();
        LOG.info("OrderGrpcEnrichmentJob started in a separate thread.");

        await().atMost(Duration.ofSeconds(90))
               .pollInterval(Duration.ofSeconds(3))
               .untilAsserted(() -> {
                   LOG.info("Checking results. Current size: {}", CollectSink.values.size());
                   assertThat(CollectSink.values).hasSize(4);
               });

        LOG.info("All 4 messages processed. Verifying content...");

        List<EnrichedOrderEvent> results = new ArrayList<>(CollectSink.values);
        results.sort((e1, e2) -> e1.getOrderId().compareTo(e2.getOrderId()));

        EnrichedOrderEvent enrichedOrder1 = results.stream().filter(e -> "order1".equals(e.getOrderId())).findFirst().orElseThrow();
        assertThat(enrichedOrder1.getProductName()).isEqualTo("Mock Product Name for product1");
        assertThat(enrichedOrder1.getProductId()).isEqualTo("product1");

        EnrichedOrderEvent enrichedOrder2 = results.stream().filter(e -> "order2".equals(e.getOrderId())).findFirst().orElseThrow();
        assertThat(enrichedOrder2.getProductName()).isEqualTo("Mock Product Name for product2");
        assertThat(enrichedOrder2.getProductId()).isEqualTo("product2");

        EnrichedOrderEvent enrichedOrderError = results.stream().filter(e -> "orderError".equals(e.getOrderId())).findFirst().orElseThrow();
        assertThat(enrichedOrderError.getProductName()).isEqualTo("N/A - Enrichment Error"); 
        assertThat(enrichedOrderError.getProductId()).isEqualTo("error_trigger");

        EnrichedOrderEvent enrichedOrderNotFound = results.stream().filter(e -> "orderNotFound".equals(e.getOrderId())).findFirst().orElseThrow();
        assertThat(enrichedOrderNotFound.getProductName()).isEqualTo("N/A - Not Found");
        assertThat(enrichedOrderNotFound.getProductId()).isEqualTo("not_found");

        LOG.info("All assertions passed.");

        if (jobThread.isAlive()) {
            LOG.info("Interrupting Flink job thread...");
            jobThread.interrupt();
            jobThread.join(Duration.ofSeconds(10).toMillis());
            if (jobThread.isAlive()) {
                LOG.warn("Flink job thread still alive after interrupt and join.");
            }
        }
        LOG.info("Test shouldEnrichOrdersViaGrpcEndToEnd finished.");
    }

    private KafkaProducer<String, String> createKafkaProducer() {
        Properties props = new Properties();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, KAFKA_CONTAINER.getBootstrapServers());
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        return new KafkaProducer<>(props);
    }

    public static class CollectSink implements SinkFunction<EnrichedOrderEvent> {
        public static final List<EnrichedOrderEvent> values = Collections.synchronizedList(new ArrayList<>());

        @Override
        public void invoke(EnrichedOrderEvent value, Context context) throws Exception {
            LOG.info("CollectSink received: {}", OBJECT_MAPPER.writeValueAsString(value));
            values.add(value);
            LOG.info("CollectSink current size: {}", values.size());
        }
    }

}
