package com.goodrx.flink;

import com.goodrx.flink.model.EnrichedOrder;
// import com.goodrx.flink.ProductLookupFunction; // Not strictly needed due to FQN usage
// import com.goodrx.flink.CustomerLookupFunction; // Not strictly needed due to FQN usage
import org.junit.jupiter.api.AfterEach;
import org.apache.flink.api.common.functions.MapFunction;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testcontainers.containers.KafkaContainer;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.testcontainers.utility.DockerImageName;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.time.Duration;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import org.apache.flink.streaming.api.datastream.AsyncDataStream;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;
import org.apache.flink.configuration.Configuration;
import static org.awaitility.Awaitility.await;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.fail;

/**
 * Main integration test that tests the job's main method directly.
 * This is the most realistic test as it runs the actual main method.
 */
@Testcontainers
public class OrderEnrichmentJobMainIntegrationTest {
    private static final Logger LOG = LoggerFactory.getLogger(OrderEnrichmentJobMainIntegrationTest.class);

    // Static list to collect orders after async lookups, populated by a dedicated sink
    private static final List<com.goodrx.flink.model.Order> ORDERS_AFTER_LOOKUPS_SINK_RESULTS = new CopyOnWriteArrayList<>();

    @Container
    private static final KafkaContainer KAFKA = new KafkaContainer(
        DockerImageName.parse("confluentinc/cp-kafka:7.4.0"));

    @Container
    private static final PostgreSQLContainer<?> POSTGRES = new PostgreSQLContainer<>("postgres:13")
        .withDatabaseName("inventory")
        .withUsername("postgres")
        .withPassword("postgres");

    private static final String ORDERS_TOPIC = "orders-main-test-" + UUID.randomUUID().toString().substring(0, 8);

    @BeforeEach
    void setUp() throws Exception {
        OrderEnrichmentJob.TEST_RESULTS.clear();
        OrderEnrichmentJobMainIntegrationTest.ORDERS_AFTER_LOOKUPS_SINK_RESULTS.clear(); // Clear before each test
        initializeDatabase();
        verifyDatabaseConnection();
    }
    
    /**
     * Verifies that the database connection works and contains the expected test data.
     * This helps catch issues with the database setup before running the actual test.
     */
    private void verifyDatabaseConnection() throws Exception {
        LOG.info("Verifying database connection and test data...");
        try (Connection conn = DriverManager.getConnection(
                POSTGRES.getJdbcUrl(), POSTGRES.getUsername(), POSTGRES.getPassword());
             Statement stmt = conn.createStatement()) {
            
            // Check customers table
            try (ResultSet rs = stmt.executeQuery("SELECT id, first_name, last_name FROM customers")) {
                boolean customersFound = false;
                while (rs.next()) {
                    customersFound = true;
                    LOG.info("Customer found: id={}, firstName={}, lastName={}", 
                            rs.getInt("id"), rs.getString("first_name"), rs.getString("last_name"));
                }
                if (!customersFound) {
                    LOG.error("NO CUSTOMERS FOUND IN DATABASE - this will cause test failures!");
                }
            }
            
            // Check products table
            try (ResultSet rs = stmt.executeQuery("SELECT id, name FROM products")) {
                boolean productsFound = false;
                while (rs.next()) {
                    productsFound = true;
                    LOG.info("Product found: id={}, name={}", rs.getInt("id"), rs.getString("name"));
                }
                if (!productsFound) {
                    LOG.error("NO PRODUCTS FOUND IN DATABASE - this will cause test failures!");
                }
            }
            
            LOG.info("Database verification complete.");
        } catch (Exception e) {
            LOG.error("DATABASE VERIFICATION FAILED", e);
            throw e;
        }
    }
    
    /**
     * Directly tests the database connection for customer lookup using the same pattern
     * as the CustomerLookupFunction in OrderEnrichmentJob.
     * 
     * @param jdbcUrl JDBC URL for database connection
     * @param username Database username
     * @param password Database password
     */
    private void testCustomerLookup(String jdbcUrl, String username, String password) {
        LOG.info("Testing direct customer lookup with JDBC URL: {}", jdbcUrl);
        
        try (Connection conn = DriverManager.getConnection(jdbcUrl, username, password);
             PreparedStatement ps = conn.prepareStatement(
                 "SELECT id, first_name, last_name, email FROM customers WHERE id = ?")) {
             
            // Test with customer ID 1 which should exist from our setup
            ps.setInt(1, 1);
            try (ResultSet rs = ps.executeQuery()) {
                if (rs.next()) {
                    LOG.info("CUSTOMER LOOKUP SUCCESS - Found customer: id={}, firstName={}, lastName={}, email={}",
                            rs.getInt("id"), 
                            rs.getString("first_name"),
                            rs.getString("last_name"),
                            rs.getString("email"));
                } else {
                    LOG.error("CUSTOMER LOOKUP FAILED - No customer found with ID 1 - this will cause test failures!");
                }
            }
        } catch (Exception e) {
            LOG.error("CUSTOMER LOOKUP FAILED - Database error", e);
        }
    }

    @AfterEach
    void tearDown() {
        OrderEnrichmentJob.TEST_RESULTS.clear();
    }

    // CustomCustomerLookupFunction has been replaced by the standalone CustomerLookupFunction class

    // CustomProductLookupFunction has been replaced by the standalone ProductLookupFunction class

    /**
     * Tests the Flink order enrichment pipeline in a "direct" manner, without relying on Kafka.
     * <p>
     * This integration test verifies the end-to-end flow for a single {@link com.goodrx.flink.model.Order} object:
     * <ol>
     *   <li>An {@code Order} is created and sourced directly into the Flink {@link org.apache.flink.streaming.api.environment.StreamExecutionEnvironment}.</li>
     *   <li>It passes through {@link com.goodrx.flink.CustomerLookupFunction} for asynchronous customer data enrichment from a Testcontainers PostgreSQL database.</li>
     *   <li>It then passes through {@link com.goodrx.flink.ProductLookupFunction} for asynchronous product data enrichment from the same database.</li>
     *   <li>A debug map ({@link DebugCaptureMapFunction}) captures the state of the {@code Order} after these lookups.</li>
     *   <li>The {@code Order} is keyed by purchaser ID.</li>
     *   <li>It's processed by {@link com.goodrx.flink.OrderEnrichmentFunction} to calculate the customer's order count and produce a final {@link com.goodrx.flink.model.EnrichedOrder}.</li>
     *   <li>The {@code EnrichedOrder} is collected by a test sink.</li>
     * </ol>
     * Assertions are made at two key stages:
     * <ul>
     *   <li>After the async lookups: Verifies that the {@code Order} object in the {@code ordersAfterLookups} list
     *       has its customer and product fields correctly populated.</li>
     *   <li>At the final sink: Verifies that the {@code EnrichedOrder} in {@code OrderEnrichmentJob.TEST_RESULTS}
     *       contains the correct combined information, including the order count.</li>
     * </ul>
     * The test uses a {@link org.testcontainers.containers.PostgreSQLContainer} to provide a real database
     * environment, initialized with test data before the pipeline runs. The Flink job itself
     * is executed in a separate thread to allow the main test thread to use Awaitility for polling results.
     * </p>
     *
     * @throws Exception if any error occurs during test setup, pipeline execution, or assertions.
     */
    @Test
    @Timeout(value = 2, unit = TimeUnit.MINUTES) // Overall test timeout
    void testSimpleDirectPipeline() throws Exception {
        LOG.info("=== Testing Direct Pipeline Without Kafka ===");
        OrderEnrichmentJob.TEST_RESULTS.clear();

        // --- ARRANGE --- 
        com.goodrx.flink.model.Order testOrder = new com.goodrx.flink.model.Order(
            1L, "2023-05-22T20:30:00Z", 1, 2, 101, false); // Use existing product ID 101
        LOG.info("Created test order: {}", testOrder);

        String jdbcUrl = POSTGRES.getJdbcUrl();
        String username = POSTGRES.getUsername();
        String password = POSTGRES.getPassword();
        LOG.info("Database connection details: URL={}, Username={}", jdbcUrl, username);

        testCustomerLookup(jdbcUrl, username, password); // Verify DB connection and data seeding

        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.getConfig().registerKryoType(java.time.Instant.class);
        env.setParallelism(1);



        // --- DEFINE PIPELINE (ACT) ---
        DataStream<com.goodrx.flink.model.Order> orderStream = env.fromElements(testOrder);
        LOG.info("Created order stream with source data");

        CustomerLookupFunction customerLookup = new CustomerLookupFunction(jdbcUrl, username, password);
        customerLookup.open(new Configuration()); // Manual open
        DataStream<com.goodrx.flink.model.Order> ordersWithCustomer = AsyncDataStream.unorderedWait(
            orderStream, customerLookup, 30, TimeUnit.SECONDS, 100);
        LOG.info("Added customer lookup function");

        ProductLookupFunction productLookup = new ProductLookupFunction(jdbcUrl, username, password);
        productLookup.open(new Configuration()); // Manual open
        DataStream<com.goodrx.flink.model.Order> ordersWithCustomerAndProduct = AsyncDataStream.unorderedWait(
            ordersWithCustomer, productLookup, 30, TimeUnit.SECONDS, 100);
        LOG.info("Added product lookup function");

        // Add a sink to collect orders after lookups
        ordersWithCustomerAndProduct.addSink(new OrderCaptureSink()).name("OrderCaptureSink");

        DataStream<com.goodrx.flink.model.Order> ordersWithDebug = ordersWithCustomerAndProduct; // Pass through for now

        DataStream<EnrichedOrder> enrichedOrdersStream = ordersWithDebug
            .process(new com.goodrx.flink.OrderEnrichmentFunction());
        LOG.info("Added order enrichment function");

        // Results will be collected in OrderEnrichmentJob.TEST_RESULTS by TestPipelineCollectSink
        enrichedOrdersStream.addSink(new TestPipelineCollectSink()).name("FinalTestSink");

        // --- EXECUTE ---
        LOG.info("Starting pipeline execution directly on main test thread...");
        try {
            env.execute("Direct Pipeline Test");
            LOG.info("Flink pipeline execution completed.");
        } catch (Exception e) {
            LOG.error("Pipeline execution failed", e);
            fail("Flink pipeline execution threw an exception: " + e.getMessage());
        }

        // --- ASSERT STAGE 1: After Async Lookups (after env.execute() completes) ---
        LOG.info("ASSERT_STAGE1: Checking ORDERS_AFTER_LOOKUPS_SINK_RESULTS. Final count: {}", ORDERS_AFTER_LOOKUPS_SINK_RESULTS.size());
        assertThat(ORDERS_AFTER_LOOKUPS_SINK_RESULTS).as("Should have one order after async lookups").hasSize(1);

        com.goodrx.flink.model.Order orderFromDebugMap = ORDERS_AFTER_LOOKUPS_SINK_RESULTS.get(0);
        LOG.info("ASSERT_STAGE1: Collected order after lookups (from debug map): ID {}, Customer: {}, Product: {}",
                 orderFromDebugMap.getId(), orderFromDebugMap.getCustomer(), orderFromDebugMap.getProduct());
        assertThat(orderFromDebugMap.getId()).isEqualTo(1L);
        assertThat(orderFromDebugMap.getCustomer()).as("Customer in debug map should not be null").isNotNull();
        assertThat(orderFromDebugMap.getCustomer().getFirstName()).isEqualTo("John");
        assertThat(orderFromDebugMap.getProduct()).as("Product in debug map should not be null").isNotNull();
        assertThat(orderFromDebugMap.getProduct().getName()).isEqualTo("Laptop"); // Product 101 is 'Laptop'

        // --- ASSERT STAGE 2: Final Enriched Order ---
        await().atMost(Duration.ofSeconds(10)) // Shorter timeout, data should be processed quickly if stage 1 passed
               .pollInterval(Duration.ofSeconds(1))
               .untilAsserted(() -> {
                   LOG.info("AWAIT_STAGE2: Checking OrderEnrichmentJob.TEST_RESULTS (final sink). Current count: {}", OrderEnrichmentJob.TEST_RESULTS.size());
                   assertThat(OrderEnrichmentJob.TEST_RESULTS).as("Should have one enriched order in final sink").hasSize(1);
               });

        EnrichedOrder finalEnrichedOrder = OrderEnrichmentJob.TEST_RESULTS.get(0);
        LOG.info("ASSERT_STAGE2: Final enriched order: {}", finalEnrichedOrder);
        assertThat(finalEnrichedOrder.getId()).isEqualTo(1L);
        assertThat(finalEnrichedOrder.getCustomerFirstName()).isEqualTo("John");
        assertThat(finalEnrichedOrder.getCustomerLastName()).isEqualTo("Doe");
        assertThat(finalEnrichedOrder.getProductName()).isEqualTo("Laptop"); // Corrected: Product 101 is 'Laptop'
        assertThat(finalEnrichedOrder.getProductDescription()).isEqualTo("High-performance laptop"); // Corrected: Product 101 description from DB

        LOG.info("Direct pipeline test completed successfully.");
        
        // --- Test Teardown ---
        // No explicit Flink execution thread to interrupt as env.execute() ran on main thread and has completed.
        LOG.info("Test teardown: Flink job already completed.");
    }

    /**
     * A static nested {@link MapFunction} designed for debugging purposes within the Flink stream.
     * <p>
     * This function intercepts {@link com.goodrx.flink.model.Order} objects flowing through the pipeline
     * at a specific point (typically after asynchronous lookups for customer and product data).
     * It adds a deep copy of each intercepted order to an external list ({@code captureList})
     * provided during its construction. This allows the main test thread to inspect the state
     * of orders at an intermediate stage of processing, which is crucial for verifying
     * the correctness of the preceding asynchronous enrichment steps.
     * </p>
     * <p>
     * The function logs details about the captured order, including its customer and product state,
     * and the size of the capture list before and after adding the new order. It returns the
     * original order unchanged to ensure it doesn't interfere with downstream processing.
     * </p>
     * <p>
     * Usage in test:
     * <pre>{@code
     * List<com.goodrx.flink.model.Order> ordersAfterLookups = new CopyOnWriteArrayList<>();
     * DataStream<com.goodrx.flink.model.Order> debugStream =
     *     ordersWithCustomerAndProduct.map(new DebugCaptureMapFunction(ordersAfterLookups));
     * // ... later, the main test thread can assert on the contents of ordersAfterLookups ...
     * }</pre>
     * </p>
     * Note: The list provided to the constructor ({@code captureList}) must be thread-safe if accessed
     * by multiple threads, which is typical in Flink tests (e.g., {@link java.util.concurrent.CopyOnWriteArrayList}).
     * <p>
     * Example of behavior:
     * <pre>{@code
     * // Input Order (after async lookups):
     * // Order orderIn = new Order(1L, "2023-01-01T10:00:00Z", 1, 1, 101, false);
     * // orderIn.setCustomer(new Customer(1, "John", "Doe", "<EMAIL>"));
     * // orderIn.setProduct(new Product(101, "Laptop", "Gaming Laptop", 1.5));
     *
     * // Action by DebugCaptureMapFunction:
     * // 1. Logs details of orderIn.
     * // 2. Adds a deep copy of orderIn to the external 'captureList'.
     * // 3. Returns orderIn unchanged.
     *
     * // Output Order (same as input):
     * // Order orderOut = orderIn;
     * // External 'captureList' now contains a copy of orderIn.
     * }</pre>
     */
    // Sink function to capture orders after async lookups
    private static class OrderCaptureSink implements SinkFunction<com.goodrx.flink.model.Order> {
        private static final long serialVersionUID = 1L;
        private static final Logger SINK_LOG = LoggerFactory.getLogger(OrderCaptureSink.class);

        @Override
        public void invoke(com.goodrx.flink.model.Order order, Context context) throws Exception {
            SINK_LOG.info("OrderCaptureSink: Capturing order ID: {}", order.getId());
            if (order.getCustomer() == null) {
                SINK_LOG.error("OrderCaptureSink: Order {} has NULL customer", order.getId());
            } else {
                SINK_LOG.info("OrderCaptureSink: Order {} has customer: {}", order.getId(), order.getCustomer().getFirstName());
            }
            if (order.getProduct() == null) {
                SINK_LOG.error("OrderCaptureSink: Order {} has NULL product", order.getId());
            } else {
                SINK_LOG.info("OrderCaptureSink: Order {} has product: {}", order.getId(), order.getProduct().getName());
            }
            ORDERS_AFTER_LOOKUPS_SINK_RESULTS.add(new com.goodrx.flink.model.Order(order)); // Store a copy
            SINK_LOG.info("OrderCaptureSink: Added order {} to ORDERS_AFTER_LOOKUPS_SINK_RESULTS. List size now: {}. Contents: {}", 
                          order.getId(), ORDERS_AFTER_LOOKUPS_SINK_RESULTS.size(), ORDERS_AFTER_LOOKUPS_SINK_RESULTS);
        }
    }

    // Static sink for this test pipeline to collect final EnrichedOrders
    private static class TestPipelineCollectSink implements SinkFunction<EnrichedOrder> {
        private static final long serialVersionUID = 1L;
        private static final Logger SINK_LOG = LoggerFactory.getLogger(TestPipelineCollectSink.class);

        @Override
        public void invoke(EnrichedOrder enrichedOrder, Context context) throws Exception {
            SINK_LOG.info("TestPipelineCollectSink: Capturing enriched order ID: {}", enrichedOrder.getId());
            OrderEnrichmentJob.TEST_RESULTS.add(enrichedOrder);
            SINK_LOG.info("TestPipelineCollectSink: Added enriched order {} to OrderEnrichmentJob.TEST_RESULTS. List size now: {}.", 
                          enrichedOrder.getId(), OrderEnrichmentJob.TEST_RESULTS.size());
        }
    }

    private void initializeDatabase() throws Exception {
        try (Connection conn = DriverManager.getConnection(
                POSTGRES.getJdbcUrl(), POSTGRES.getUsername(), POSTGRES.getPassword());
             Statement stmt = conn.createStatement()) {

            stmt.execute("DROP TABLE IF EXISTS customers CASCADE");
            stmt.execute("DROP TABLE IF EXISTS products CASCADE");

            stmt.execute("""
                CREATE TABLE customers (
                    id INT PRIMARY KEY,
                    first_name TEXT,
                    last_name TEXT,
                    email TEXT
                )""");

            stmt.execute("""
                CREATE TABLE products (
                    id INT PRIMARY KEY,
                    name TEXT,
                    description TEXT,
                    weight DOUBLE PRECISION
                )""");

            stmt.execute("""
                INSERT INTO customers (id, first_name, last_name, email)
                VALUES (1, 'John', 'Doe', '<EMAIL>')""");

            stmt.execute("""
                INSERT INTO products (id, name, description, weight)
                VALUES (101, 'Laptop', 'High-performance laptop', 1.5)""");
            stmt.execute("""
                INSERT INTO products (id, name, description, weight)
                VALUES (102, 'SuperWidget', 'Does super things', 0.5)""");
            stmt.execute("""
                INSERT INTO products (id, name, description, weight)
                VALUES (103, 'MegaGadget', 'The biggest gadget', 3.0)""");
        }
    }

    // This method was used in the Kafka-based test and is now removed as we're using a direct pipeline approach
}
