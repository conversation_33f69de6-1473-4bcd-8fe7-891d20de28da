package com.goodrx.flink;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.goodrx.flink.model.EnrichedOrder;
import com.goodrx.flink.model.Order;
import com.goodrx.flink.model.Product;
import com.github.tomakehurst.wiremock.WireMockServer;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.sink.SinkFunction;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.testcontainers.containers.KafkaContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.testcontainers.utility.DockerImageName;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;
import java.util.UUID;

import static com.github.tomakehurst.wiremock.client.WireMock.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;

/**
 * Integration test for Order Product Enrichment with comprehensive logging.
 * 
 * <p>This test demonstrates the complete flow of:
 * <ol>
 *   <li>Setting up test infrastructure (Kafka, WireMock)</li>
 *   <li>Configuring Flink streaming environment</li>
 *   <li>Processing orders with external API enrichment</li>
 *   <li>Validating enriched results</li>
 * </ol>
 * 
 * <p>All steps are logged in detail for debugging and monitoring purposes.
 */
@Testcontainers
public class OrderProductEnrichmentIntegrationTest {
    private static final Logger LOG = LoggerFactory.getLogger(OrderProductEnrichmentIntegrationTest.class);
    
    /** Kafka container for message streaming */
    @Container
    private static final KafkaContainer kafka = new KafkaContainer(DockerImageName.parse("confluentinc/cp-kafka:7.4.0"));
    
    /** WireMock server for mocking external product API */
    private static WireMockServer wireMock;
    
    /** Base URL for the mocked product API */
    private static String apiBaseUrl;
    
    /** Unique Kafka topic name for this test run */
    private static final String ORDERS_TOPIC = "test-orders-" + UUID.randomUUID().toString().substring(0, 8);
    
    /**
     * Sets up the WireMock server before all tests in this class.
     * 
     * <p>Initializes the mock product API server and configures default
     * product response stubs. This runs once before all test methods.
     * 
     * @throws Exception if WireMock server setup fails
     */
    @BeforeAll
    static void setupApi() throws Exception {
        LOG.info("=== Setting up WireMock API server ===");
        
        // Start WireMock server on random available port
        wireMock = new WireMockServer(0);
        wireMock.start();
        apiBaseUrl = "http://localhost:" + wireMock.port();
        
        LOG.info("✅ WireMock server started successfully at: {}", apiBaseUrl);
        
        // Configure default product API stub
        LOG.info("Configuring default product API stubs...");
        wireMock.stubFor(get(urlPathMatching("/api/products/.*"))
                .willReturn(aResponse()
                        .withStatus(200)
                        .withHeader("Content-Type", "application/json")
                        .withBody("{\"id\":101,\"name\":\"Widget\",\"description\":\"A test widget\"}")));
        
        LOG.info("✅ Default product API stubs configured");
        LOG.info("=== WireMock setup completed ===");
    }
    
    /**
     * Cleans up resources after all tests complete.
     * 
     * <p>Stops the WireMock server and performs any necessary cleanup.
     * This runs once after all test methods have completed.
     */
    @AfterAll
    static void teardownApi() {
        LOG.info("=== Starting test cleanup ===");
        
        if (wireMock != null && wireMock.isRunning()) {
            LOG.info("Stopping WireMock server...");
            wireMock.stop();
            LOG.info("✅ WireMock server stopped successfully");
        } else {
            LOG.warn("⚠️ WireMock server was not running during cleanup");
        }
        
        LOG.info("=== Test cleanup completed ===");
    }
    
    /**
     * Prepares test data before each test method execution.
     * 
     * <p>This method performs the following setup steps:
     * <ol>
     *   <li>Resets WireMock server state</li>
     *   <li>Clears the result collection sink</li>
     *   <li>Configures product-specific API stubs</li>
     *   <li>Creates Kafka topic for the test</li>
     *   <li>Produces test order message to Kafka</li>
     * </ol>
     * 
     * @throws Exception if test data setup fails
     */
    @BeforeEach
    void produceTestOrder() throws Exception {
        LOG.info("=== Setting up test data ===");
        
        // Step 1: Reset WireMock state
        LOG.info("Step 1: Resetting WireMock server state...");
        wireMock.resetAll();
        LOG.info("✅ WireMock server reset completed");
        
        // Step 2: Clear result collection
        LOG.info("Step 2: Clearing result collection sink...");
        CollectSink.clear();
        LOG.info("✅ Result sink cleared, current size: {}", CollectSink.size());
        
        // Step 3: Configure specific product API stub
        LOG.info("Step 3: Configuring product-specific API stub...");
        wireMock.stubFor(get(urlPathEqualTo("/api/products/101"))
                .willReturn(aResponse()
                        .withStatus(200)
                        .withHeader("Content-Type", "application/json")
                        .withBody("{\"id\":101,\"name\":\"Widget\",\"description\":\"A test widget\"}"))); 
        
        LOG.info("✅ Product API stub configured for product ID 101");
        LOG.info("   Total WireMock stubs configured: {}", wireMock.getStubMappings().size());
        
        try {
            // Step 4: Create Kafka topic
            LOG.info("Step 4: Creating Kafka topic...");
            LOG.info("   Topic name: {}", ORDERS_TOPIC);
            LOG.info("   Kafka bootstrap servers: {}", kafka.getBootstrapServers());
            
            TestUtils.createKafkaTopic(kafka, ORDERS_TOPIC);
            LOG.info("✅ Kafka topic created successfully: {}", ORDERS_TOPIC);
            
            // Step 5: Produce test order message
            LOG.info("Step 5: Producing test order message...");
            String testOrderJson = """
                {
                    "after": {
                        "id": 1,
                        "order_date": "2023-01-01T00:00:00Z",
                        "purchaser": 1,
                        "quantity": 1,
                        "product_id": 101
                    },
                    "op": "c"
                }""";
            
            LOG.info("   Test order JSON: {}", testOrderJson.replaceAll("\\s+", " "));
            
            TestUtils.sendTestOrder(kafka, ORDERS_TOPIC, testOrderJson);
            LOG.info("✅ Test order message sent to Kafka topic: {}", ORDERS_TOPIC);
            
        } catch (Exception e) {
            LOG.error("❌ Failed to prepare test data: {}", e.getMessage(), e);
            throw new RuntimeException("Test data setup failed", e);
        }
        
        LOG.info("=== Test data setup completed successfully ===");
    }

    /**
     * Tests the complete Flink order enrichment pipeline with product API integration.
     * 
     * <p>This comprehensive integration test validates:
     * <ol>
     *   <li>Flink StreamExecutionEnvironment setup and configuration</li>
     *   <li>Kafka source configuration and message consumption</li>
     *   <li>Debezium CDC message deserialization</li>
     *   <li>External product API calls via HTTP client</li>
     *   <li>Order enrichment with product data</li>
     *   <li>Result collection and verification</li>
     * </ol>
     * 
     * <p>The test uses real Kafka containers and mocked HTTP APIs to provide
     * a realistic testing environment while maintaining test isolation.
     * 
     * @throws Exception if any part of the integration test fails
     */
    @Test
    void flinkEnrichesWithApiProduct() throws Exception {
        LOG.info("==================== STARTING PRODUCT ENRICHMENT INTEGRATION TEST ====================");
        LOG.info("Test Configuration:");
        LOG.info("  - Kafka Bootstrap Servers: {}", kafka.getBootstrapServers());
        LOG.info("  - Test Topic: {}", ORDERS_TOPIC);
        LOG.info("  - WireMock API URL: {}", apiBaseUrl);
        LOG.info("  - Test Method: flinkEnrichesWithApiProduct()");
        
        // Step 1: Configure Flink execution environment
        LOG.info("=== Step 1: Configuring Flink Execution Environment ===");
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        env.setParallelism(1);
        env.enableCheckpointing(1000);
        LOG.info("✅ Flink environment configured:");
        LOG.info("   - Parallelism: 1");
        LOG.info("   - Checkpointing interval: 1000ms");
        
        // Step 2: Configure Kafka consumer properties
        LOG.info("=== Step 2: Configuring Kafka Consumer Properties ===");
        Properties kafkaProps = new Properties();
        String consumerGroupId = "test-group-" + UUID.randomUUID().toString().substring(0, 8);
        kafkaProps.setProperty("bootstrap.servers", kafka.getBootstrapServers());
        kafkaProps.setProperty("group.id", consumerGroupId);
        kafkaProps.setProperty("auto.offset.reset", "earliest");
        
        LOG.info("✅ Kafka consumer properties configured:");
        LOG.info("   - Bootstrap servers: {}", kafkaProps.getProperty("bootstrap.servers"));
        LOG.info("   - Consumer group ID: {}", consumerGroupId);
        LOG.info("   - Offset reset: {}", kafkaProps.getProperty("auto.offset.reset"));

        // Step 3: Create Kafka source
        LOG.info("=== Step 3: Creating Kafka Source ===");
        KafkaSource<String> source = KafkaSource.<String>builder()
                .setBootstrapServers(kafka.getBootstrapServers())
                .setTopics(ORDERS_TOPIC)
                .setStartingOffsets(OffsetsInitializer.earliest())
                .setValueOnlyDeserializer(new SimpleStringSchema())
                .setProperties(kafkaProps)
                .build();
        
        LOG.info("✅ Kafka source created successfully");
        LOG.info("   - Topics: [{}]", ORDERS_TOPIC);
        LOG.info("   - Starting offset: EARLIEST");
        LOG.info("   - Deserializer: SimpleStringSchema");

        // Step 4: Create order stream with deserialization
        LOG.info("=== Step 4: Creating Order Stream with Deserialization ===");
        DataStream<Order> orders = env.fromSource(
                source,
                WatermarkStrategy.noWatermarks(),
                "Kafka Source"
        ).map(new OrderDeserializer())
         .filter(order -> {
            boolean isValid = order != null && order.getProductId() != null && order.getProductId() > 0;
            if (isValid) {
                LOG.info("✅ Order passed validation: {}", order);
            } else {
                LOG.warn("⚠️ Order failed validation, filtering out: {}", order);
            }
            return isValid;
        });

        LOG.info("✅ Order stream created with deserialization and filtering");

        // Step 5: Create product enrichment stream
        LOG.info("=== Step 5: Creating Product Enrichment Stream ===");
        DataStream<EnrichedOrder> enrichedStream = orders.map(new ProductEnrichmentFunction(apiBaseUrl));

        LOG.info("✅ Product enrichment stream created");

        // Step 6: Add result collection sink
        LOG.info("=== Step 6: Adding Result Collection Sink ===");
        CollectSink.clear();
        enrichedStream.addSink(new CollectSink());
        LOG.info("✅ Result collection sink added, initial size: {}", CollectSink.size());

        // Step 7: Execute Flink job
        LOG.info("=== Step 7: Executing Flink Job ===");
        String jobName = "product-enrich-test-" + UUID.randomUUID().toString().substring(0, 8);
        LOG.info("Starting Flink job: {}", jobName);
        
        var job = env.executeAsync(jobName);
        LOG.info("✅ Flink job started successfully");
        
        try {
            // Step 8: Wait for results and verify
            LOG.info("=== Step 8: Waiting for Results ===");
            LOG.info("Waiting for enriched records (timeout: 30 seconds)...");
            
            await().atMost(Duration.ofSeconds(30))
                   .pollInterval(Duration.ofSeconds(2))
                   .untilAsserted(() -> {
                       int currentSize = CollectSink.size();
                       LOG.info("⏱️ Polling results - Current sink size: {}", currentSize);
                       
                       if (currentSize > 0) {
                           LOG.info("🎉 Found {} enriched record(s)!", currentSize);
                           List<EnrichedOrder> currentResults = CollectSink.getValues();
                           for (int i = 0; i < currentResults.size(); i++) {
                               LOG.info("   Record {}: {}", i + 1, currentResults.get(i));
                           }
                       }
                       
                       assertThat(CollectSink.getValues())
                           .as("Should have at least one enriched record")
                           .isNotEmpty();
                   });

            // Step 9: Verify enrichment results
            LOG.info("=== Step 9: Verifying Enrichment Results ===");
            List<EnrichedOrder> results = CollectSink.getValues();
            LOG.info("Total enriched records collected: {}", results.size());
            
            if (!results.isEmpty()) {
                EnrichedOrder firstResult = results.get(0);
                LOG.info("Verifying first enriched record: {}", firstResult);
                
                LOG.info("Checking product name enrichment...");
                assertThat(firstResult.getProductName())
                    .as("Product name should be enriched from API")
                    .isEqualTo("Widget");
                LOG.info("✅ Product name verification passed: {}", firstResult.getProductName());
                    
                LOG.info("Checking product description enrichment...");
                assertThat(firstResult.getProductDescription())
                    .as("Product description should be enriched from API")
                    .isEqualTo("A test widget");
                LOG.info("✅ Product description verification passed: {}", firstResult.getProductDescription());
                
                LOG.info("✅ All enrichment verifications passed successfully!");
            }
            
            LOG.info("🎉 PRODUCT ENRICHMENT INTEGRATION TEST COMPLETED SUCCESSFULLY! 🎉");
            
        } catch (Exception e) {
            LOG.error("❌ Test failed with exception: {}", e.getMessage(), e);
            LOG.error("Current sink state: {} records", CollectSink.size());
            if (CollectSink.size() > 0) {
                LOG.error("Available records: {}", CollectSink.getValues());
            }
            throw e;
        } finally {
            // Step 10: Cleanup
            LOG.info("=== Step 10: Cleaning up Flink Job ===");
            LOG.info("Cancelling Flink job: {}", jobName);
            job.cancel();
            LOG.info("✅ Flink job cancelled successfully");
        }
        
        LOG.info("==================== PRODUCT ENRICHMENT INTEGRATION TEST COMPLETED ====================");
    }
    
    /**
     * Serializable MapFunction for deserializing Kafka messages in Debezium CDC format.
     * 
     * <p>Handles the Debezium CDC message structure by extracting the order
     * data from the 'after' field and mapping it to an Order POJO.
     */
    public static class OrderDeserializer implements MapFunction<String, Order> {
        private static final long serialVersionUID = 1L;
        private static final Logger LOG = LoggerFactory.getLogger(OrderDeserializer.class);
        
        @Override
        public Order map(String orderJson) throws Exception {
            LOG.debug("🔄 Starting order deserialization...");
            
            try {
                ObjectMapper mapper = new ObjectMapper();
                var node = mapper.readTree(orderJson);
                var afterNode = node.get("after");
                
                if (afterNode != null) {
                    LOG.debug("Found 'after' node in CDC message");
                    
                    Order order = new Order(
                        afterNode.get("id").asLong(),
                        afterNode.get("order_date").asText(),
                        afterNode.get("purchaser").asInt(),
                        afterNode.get("quantity").asInt(),
                        afterNode.get("product_id").asInt(),
                        false // Assuming 'deleted' is false if not present in 'after' node
                    );
                    
                    LOG.debug("✅ Order deserialization successful: {}", order);
                    return order;
                } else {
                    LOG.warn("⚠️ No 'after' field found in CDC message: {}", orderJson);
                    return null;
                }
            } catch (Exception e) {
                LOG.error("❌ Order deserialization failed for message: {}", orderJson, e);
                return null;
            }
        }
    }
    
    /**
     * Serializable MapFunction for enriching orders with product information from external API.
     * 
     * <p>Makes an HTTP GET request to the product API (mocked by WireMock)
     * to retrieve product details and creates an enriched order object.
     */
    public static class ProductEnrichmentFunction implements MapFunction<Order, EnrichedOrder> {
        private static final long serialVersionUID = 1L;
        private static final Logger LOG = LoggerFactory.getLogger(ProductEnrichmentFunction.class);
        
        private final String apiBaseUrl;
        
        public ProductEnrichmentFunction(String apiBaseUrl) {
            this.apiBaseUrl = apiBaseUrl;
        }
        
        @Override
        public EnrichedOrder map(Order order) throws Exception {
            LOG.info("🔄 Starting product enrichment for order ID: {}", order.getId());
            LOG.info("   Product ID to lookup: {}", order.getProductId());
            
            try {
                // Make HTTP request to product API
                String productApiUrl = apiBaseUrl + "/api/products/" + order.getProductId();
                LOG.info("   Making HTTP GET request to: {}", productApiUrl);
                
                var client = HttpClient.newHttpClient();
                var request = HttpRequest.newBuilder()
                        .uri(new URI(productApiUrl))
                        .GET()
                        .build();
                        
                var response = client.send(request, HttpResponse.BodyHandlers.ofString());
                
                LOG.info("   API Response - Status: {}, Body: {}", response.statusCode(), response.body());
                
                if (response.statusCode() != 200) {
                    throw new RuntimeException("Product API call failed with status: " + response.statusCode());
                }
                
                // Parse product response
                LOG.info("   Parsing product API response...");
                var product = new ObjectMapper().readValue(response.body(), Product.class);
                LOG.info("   ✅ Product parsed successfully: {}", product);
                
                // Create enriched order
                LOG.info("   Creating enriched order...");
                EnrichedOrder enriched = new EnrichedOrder(order, null, product);
                LOG.info("   ✅ Enriched order created successfully: {}", enriched);
                
                return enriched;
            } catch (Exception e) {
                LOG.error("❌ Product enrichment failed for order {}: {}", order.getId(), e.getMessage(), e);
                throw new RuntimeException("Product enrichment failed", e);
            }
        }
    }
    
    /**
     * Thread-safe test sink that collects EnrichedOrder results for verification.
     * 
     * <p>This sink implementation provides thread-safe collection of results
     * during test execution, allowing verification of the enrichment pipeline output.
     * 
     * <p>All methods are synchronized to ensure thread safety in concurrent
     * Flink execution environments.
     */
    public static class CollectSink implements SinkFunction<EnrichedOrder> {
        private static final long serialVersionUID = 1L;
        private static final Object lock = new Object();
        private static final List<EnrichedOrder> values = new ArrayList<>();
        
        /**
         * Invoked for each enriched order record processed by the sink.
         * 
         * <p>Thread-safe method that adds the enriched order to the collection
         * and logs the addition for debugging purposes.
         * 
         * @param value the enriched order to collect
         * @param context the sink context (unused)
         */
        @Override
        public void invoke(EnrichedOrder value, Context context) {
            synchronized (lock) {
                values.add(value);
                LOG.info("📥 CollectSink: Added enriched record #{}: {}", values.size(), value);
            }
        }
        
        /**
         * Returns a thread-safe copy of all collected enriched orders.
         * 
         * @return immutable copy of collected results
         */
        public static List<EnrichedOrder> getValues() {
            synchronized (lock) {
                return new ArrayList<>(values);
            }
        }
        
        /**
         * Clears all collected results in a thread-safe manner.
         * 
         * <p>This method is typically called before test execution to ensure
         * clean state for result collection.
         */
        public static void clear() {
            synchronized (lock) {
                int previousSize = values.size();
                values.clear();
                LOG.info("🧹 CollectSink: Cleared {} previous records, current size: {}", 
                        previousSize, values.size());
            }
        }
        
        /**
         * Returns the current number of collected results in a thread-safe manner.
         * 
         * @return the number of collected enriched orders
         */
        public static int size() {
            synchronized (lock) {
                return values.size();
            }
        }
    }
}
