# Root logger option
log4j.rootLogger=INFO, stdout

# Your application classes - set to DEBUG to see all logging
log4j.logger.com.goodrx.flink=DEBUG

# Reduce noise from other components
log4j.logger.org.apache.flink=WARN
log4j.logger.org.apache.kafka=INFO
log4j.logger.org.testcontainers=INFO
log4j.logger.com.github.dockerjava=INFO

# Suppress verbose Flink Pekko/Akka dispatcher logs
log4j.logger.org.apache.pekko=WARN
log4j.logger.akka=WARN

# Console appender configuration
log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.Target=System.out
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
log4j.appender.stdout.layout.ConversionPattern=%d{HH:mm:ss.SSS} [%t] %-5p %c{1} - %m%n
