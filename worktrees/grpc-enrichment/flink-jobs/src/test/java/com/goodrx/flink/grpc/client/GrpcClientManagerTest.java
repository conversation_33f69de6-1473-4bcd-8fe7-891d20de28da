package com.goodrx.flink.grpc.client;

import io.grpc.ManagedChannel;
import io.grpc.inprocess.InProcessServerBuilder;
import io.grpc.testing.GrpcCleanupRule;
import org.junit.After;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.JUnit4;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.*;

/**
 * Unit tests for the {@link GrpcClientManager} class.
 * These tests cover channel creation, caching, shutdown, and configuration handling.
 */
@RunWith(JUnit4.class) // Use JUnit 4 runner
public class GrpcClientManagerTest {

    /**
     * JUnit 4 rule for managing the lifecycle of an in-process gRPC server and channels.
     * Ensures resources are cleaned up after each test.
     */
    @Rule // Use JUnit 4 @Rule
    public final GrpcCleanupRule grpcCleanup = new GrpcCleanupRule();

    private GrpcClientManager clientManager;
    private String serverName;
    // private Server server; // Unused, server lifecycle managed by grpcCleanup

    /**
     * Sets up the test environment before each test method.
     * Initializes an in-process gRPC server and the {@link GrpcClientManager}.
     *
     * @throws IOException if an I/O error occurs during server setup.
     */
    @Before // Use JUnit 4 @Before
    public void setUp() throws IOException {
        // Generate a unique server name for each test case.
        serverName = InProcessServerBuilder.generateName();

        // Create a dummy in-process server that isn't strictly necessary for testing manager logic
        // but useful for ensuring channels can connect.
        grpcCleanup.register(InProcessServerBuilder
                .forName(serverName).directExecutor().build().start());
        
        // Default config for the manager itself (can be overridden per channel)
        GrpcClientManager.GrpcClientConfig defaultManagerConfig = new GrpcClientManager.GrpcClientConfig("default-host", 12345, false);
        clientManager = new GrpcClientManager(defaultManagerConfig);
    }

    /**
     * Tears down the test environment after each test method.
     * Shuts down the {@link GrpcClientManager} and releases its resources.
     */
    @After // Use JUnit 4 @After
    public void tearDown() throws InterruptedException {
        if (clientManager != null) {
            clientManager.shutdownAll(); // Ensure all channels managed by this instance are closed
        }
        // server is shutdown by GrpcCleanupRule. Channels managed by clientManager are shut down in its own shutdown methods.
    }

    /**
     * Tests that {@link GrpcClientManager#getChannel(GrpcClientConfig)} creates a new channel
     * when a channel for the given configuration is not already in the cache.
     * Verifies that the created channel is active and targets the correct server.
     */
    @Test
    public void testGetChannel_createsAndCachesChannel() {
        String target = serverName; // Using in-process server name as target
        GrpcClientManager.GrpcClientConfig channelConfig = new GrpcClientManager.GrpcClientConfig(target, 0, false);

        ManagedChannel channel1 = clientManager.getChannel(channelConfig);
        assertNotNull("Channel should not be null", channel1);
        assertFalse("Channel should not be initially shutdown", channel1.isShutdown());
        assertFalse("Channel should not be initially terminated", channel1.isTerminated());

        ManagedChannel channel2 = clientManager.getChannel(channelConfig);
        assertSame("Should return the same cached channel instance for the same config", channel1, channel2);
    }

    /**
     * Tests that {@link GrpcClientManager#getChannel(GrpcClientConfig)} creates different channels
     * for different {@link GrpcClientConfig} instances (e.g., different ports or TLS settings).
     * Verifies that distinct channel instances are returned for distinct configurations.
     */
    @Test
    public void testGetChannel_differentConfigCreatesDifferentChannel() {
        String targetBase = serverName;
        GrpcClientManager.GrpcClientConfig config1 = new GrpcClientManager.GrpcClientConfig(targetBase, 0, false);
        GrpcClientManager.GrpcClientConfig config2 = new GrpcClientManager.GrpcClientConfig(targetBase, 0, true); // Different TLS setting

        ManagedChannel channel1 = clientManager.getChannel(config1);
        ManagedChannel channel2 = clientManager.getChannel(config2);

        assertNotNull("Channel1 should not be null", channel1);
        assertNotNull("Channel2 should not be null", channel2);
        assertNotSame("Different configs (even if same host/port due to other settings like TLS) should result in different channels", channel1, channel2);
    }
    
    /**
     * Tests that {@link GrpcClientManager#getChannel(GrpcClientConfig)} uses an in-process channel
     * when the host is specified as "in-process" (case-insensitive).
     * Verifies that an in-process channel is created.
     */
    @Test
    public void testGetChannel_usesInProcessChannelBuilderWhenTargetIsLocalhostAndPortIsZero() {
        // This test relies on the internal knowledge that port 0 with "localhost" or in-process name implies in-process
        // More robustly, we'd check the type of channel, but that's harder.
        // Here, we use the in-process server name directly.
        GrpcClientManager.GrpcClientConfig configInProcess = new GrpcClientManager.GrpcClientConfig(serverName, 0, false);
        ManagedChannel channel = clientManager.getChannel(configInProcess);
        assertNotNull("Channel should not be null", channel);
        // For in-process channels, the authority should match the server name used to create it.
        // This is a more robust check than instanceof or class name checking, given potential wrappers.
        assertEquals("In-process channel authority should be 'localhost' by default", "localhost", channel.authority());
        // System.out.println("DEBUG: In-process channel authority: " + channel.authority() + ", expected serverName: " + serverName + ", actual class: " + channel.getClass().getName());
    }


    /**
     * Tests that {@link GrpcClientManager#shutdownChannel(GrpcClientConfig)} removes the channel
     * from the cache and initiates its shutdown.
     * Verifies that the channel is shut down and a new channel is created on a subsequent request.
     */
    @Test
    public void testShutdownChannel_shutsDownSpecificChannel() throws InterruptedException {
        GrpcClientManager.GrpcClientConfig config = new GrpcClientManager.GrpcClientConfig(serverName, 0, false);
        ManagedChannel channel = clientManager.getChannel(config);
        assertFalse("Channel should not be initially shutdown", channel.isShutdown());

        clientManager.shutdownChannel(config);
        assertTrue("Channel should be shutdown after explicit call", channel.isShutdown());
        
        // Wait for termination
        boolean terminated = channel.awaitTermination(5, TimeUnit.SECONDS);
        assertTrue("Channel should terminate after shutdown", terminated);
        assertTrue("Channel should be terminated", channel.isTerminated());

        // Verify manager removes it from cache
        ManagedChannel newChannel = clientManager.getChannel(config);
        assertNotSame("A new channel should be created after the old one was shutdown and removed", channel, newChannel);
        assertFalse("New channel should not be initially shutdown", newChannel.isShutdown());
    }

    @Test
    public void testShutdownAll_shutsDownAllManagedChannels() throws InterruptedException {
        GrpcClientManager.GrpcClientConfig config1 = new GrpcClientManager.GrpcClientConfig(serverName, 0, false);
        GrpcClientManager.GrpcClientConfig config2 = new GrpcClientManager.GrpcClientConfig(InProcessServerBuilder.generateName(), 0, false); // Another in-process server

        ManagedChannel channel1 = clientManager.getChannel(config1);
        ManagedChannel channel2 = clientManager.getChannel(config2);

        assertFalse("Channel 1 should not be initially shutdown", channel1.isShutdown());
        assertFalse("Channel 2 should not be initially shutdown", channel2.isShutdown());

        clientManager.shutdownAll();

        assertTrue("Channel 1 should be shutdown", channel1.isShutdown());
        assertTrue("Channel 2 should be shutdown", channel2.isShutdown());

        assertTrue("Channel 1 should terminate", channel1.awaitTermination(5, TimeUnit.SECONDS));
        assertTrue("Channel 2 should terminate", channel2.awaitTermination(5, TimeUnit.SECONDS));
        
        // assertTrue(clientManager.getChannelsCacheSize() == 0, "Channels cache should be empty after shutdownAll"); // Method not available
    }
    
    @Test
    public void testChannelConfig_isUsePlaintext() {
        GrpcClientManager.GrpcClientConfig configNoTls = new GrpcClientManager.GrpcClientConfig("host", 123, false);
        GrpcClientManager.GrpcClientConfig configWithTls = new GrpcClientManager.GrpcClientConfig("host", 123, true);

        assertTrue("usePlaintext should be true when useTls is false (i.e. isUseTls() is false)", !configNoTls.isUseTls());
        assertTrue("configWithTls should use TLS (i.e. isUseTls() is true)", configWithTls.isUseTls());
    }
}
