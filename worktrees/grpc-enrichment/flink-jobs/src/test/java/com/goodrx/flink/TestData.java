package com.goodrx.flink;

import com.goodrx.flink.model.Customer;
import com.goodrx.flink.model.Order;
import com.goodrx.flink.model.Product;

import java.time.Instant;

/**
 * Test data factory for creating test objects
 */
public class TestData {
    
    public static Customer createTestCustomer() {
        Customer customer = new Customer();
        customer.setId(1);
        customer.setFirstName("John");
        customer.setLastName("Doe");
        customer.setEmail("<EMAIL>");
        return customer;
    }
    
    public static Product createTestProduct() {
        Product product = new Product();
        product.setId(101);
        product.setName("Test Product");
        product.setDescription("This is a test product");
        product.setWeight(1.5);
        return product;
    }
    
    public static Order createTestOrder() {
        Order order = new Order(
            1001L,                       // id
            Instant.now().toString(),    // orderDateStr
            1,                           // purchaser
            2,                           // quantity
            101,                         // productId
            false                        // deleted
        );
        order = order.withCustomer(createTestCustomer());
        order = order.withProduct(createTestProduct());
        return order;
    }
    
    public static String createDebeziumOrderJson() {
        return "{\n" +
               "  \"before\": null,\n" +
               "  \"after\": {\n" +
               "    \"id\": 1001,\n" +
               "    \"order_date\": \"2023-05-22T20:30:00Z\",\n" +
               "    \"purchaser\": 1,\n" +
               "    \"quantity\": 2,\n" +
               "    \"product_id\": 101\n" +
               "  },\n" +
               "  \"source\": {\n" +
               "    \"version\": \"1.9.7.Final\",\n" +
               "    \"connector\": \"postgresql\",\n" +
               "    \"name\": " + "\"dbserver1\",\n" +
               "    \"ts_ms\": 1621720800000,\n" +
               "    \"snapshot\": \"false\",\n" +
               "    \"db\": \"inventory\",\n" +
               "    \"sequence\": \"[\\\"2482337154808\\\",\\\"2482337154808\\\"]\",\n" +
               "    \"schema\": \"public\",\n" +
               "    \"table\": \"orders\",\n" +
               "    \"txId\": 12345,\n" +
               "    \"lsn\": 2482337154808,\n" +
               "    \"xmin\": null\n" +
               "  },\n" +
               "  \"op\": \"c\",\n" +
               "  \"ts_ms\": 1621720800000,\n" +
               "  \"transaction\": null\n" +
               "}";
    }
}
