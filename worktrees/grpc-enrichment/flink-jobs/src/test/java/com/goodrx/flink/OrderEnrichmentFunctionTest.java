package com.goodrx.flink;

import com.goodrx.flink.model.Customer;
import com.goodrx.flink.model.EnrichedOrder;
import com.goodrx.flink.model.Order;
import com.goodrx.flink.model.Product;
import org.apache.flink.streaming.runtime.streamrecord.StreamRecord;
import org.apache.flink.streaming.util.OneInputStreamOperatorTestHarness; // New Harness
import org.apache.flink.streaming.util.ProcessFunctionTestHarnesses;   // New Harness Factory
// import org.apache.flink.streaming.util.TestHarnessUtil; // Not strictly needed for these assertions
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Unit tests for the {@link OrderEnrichmentFunction}.
 * <p>
 * These tests use the {@link ProcessOperatorTestHarness} to verify the behavior
 * of the {@code OrderEnrichmentFunction}. This includes the transformation of an input {@link Order}
 * (assumed to be pre-enriched with Customer and Product details) into a final
 * {@link EnrichedOrder}.
 * </p>
 * <p>
 * Test Setup Example:
 * <pre>{@code
 * // 1. Instantiate the function under test
 * OrderEnrichmentFunction functionToTest = new OrderEnrichmentFunction();
 *
 * // 2. Wrap it in a ProcessOperator
 * ProcessOperator<Order, EnrichedOrder> operator =
 *     new ProcessOperator<>(functionToTest);
 *
 * // 3. Create the test harness, providing the operator and input type info
 * ProcessOperatorTestHarness<Order, EnrichedOrder> harness =
 *     new ProcessOperatorTestHarness<>(
 *         operator,
 *         TypeInformation.of(Order.class)
 *     );
 * harness.open(); // Initializes the function and its state
 *
 * // 4. Prepare an input Order (customer and product should be set by prior pipeline steps)
 * Customer customer = new Customer(1, "Jane", "Doe", "<EMAIL>");
 * Product product = new Product(101, "Thingamajig", "Useful item", 1.0);
 * // Use string date constructor for Order
 * Order inputOrder = new Order(1L, "2023-10-26T10:00:00Z", 1, 1, 101, false)
 *                      .withCustomer(customer)
 *                      .withProduct(product);
 *
 * // 5. Process the input element
 * harness.processElement(new StreamRecord<>(inputOrder, 0L)); // timestamp 0
 *
 * // 6. Retrieve and assert output
 * ConcurrentLinkedQueue<Object> outputQueue = harness.getRecordOutput();
 * EnrichedOrder result = (EnrichedOrder) ((StreamRecord) outputQueue.poll()).getValue();
 *
 * // Example Assertions:
 * // assertThat(result.getId()).isEqualTo(1L);
 * // assertThat(result.getCustomerFirstName()).isEqualTo("Jane");
 * // assertThat(result.getProductName()).isEqualTo("Thingamajig");
 * // assertThat(result.getCustomerFirstName()).isEqualTo("Jane"); // Example assertion
 *
 * // 7. Close the harness
 * harness.close();
 * }</pre>
 * </p>
 */
public class OrderEnrichmentFunctionTest {

    private OneInputStreamOperatorTestHarness<Order, EnrichedOrder> testHarness;
    private OrderEnrichmentFunction orderEnrichmentFunction;

    @BeforeEach
    void setUp() throws Exception {
        orderEnrichmentFunction = new OrderEnrichmentFunction();
        // The ProcessFunctionTestHarnesses factory method typically infers types.
        // If OrderEnrichmentFunction is a KeyedProcessFunction, this will need adjustment.
        // Assuming it's a non-keyed ProcessFunction for now.
        testHarness = ProcessFunctionTestHarnesses
                .forProcessFunction(orderEnrichmentFunction);
        // TypeInformation.of(Order.class) is no longer explicitly passed here.
        // The ProcessOperator is also not explicitly created.
        testHarness.open();
    }

    @AfterEach
    void tearDown() throws Exception {
        if (testHarness != null) {
            testHarness.close();
        }
    }

    private String nowStr() { return Instant.now().toString(); }
    private String laterStr(long seconds) { return Instant.now().plusSeconds(seconds).toString(); }


    /**
     * Tests that a single order for a customer is correctly enriched and the order count is 1.
     * Input: Order(id=1, purchaserId=1, customer="John Doe", product="Laptop")
     * Output: EnrichedOrder(id=1, customerFirstName="John", productName="Laptop", orderCount=1)
     */
    @Test
    void testSingleOrderEnrichment() throws Exception {
        Customer customer1 = new Customer(1, "John", "Doe", "<EMAIL>");
        Product product1 = new Product(101, "Laptop", "High-end laptop", 1.5);
        Order order1 = new Order(1L, nowStr(), 1, 1, 101, false)
                            .withCustomer(customer1)
                            .withProduct(product1);

        testHarness.processElement(new StreamRecord<>(order1, 0L));

        List<EnrichedOrder> output = testHarness.extractOutputValues();
        assertThat(output).hasSize(1);
        EnrichedOrder enrichedOrder1 = output.get(0);

        assertThat(enrichedOrder1.getId()).isEqualTo(order1.getId());
        assertThat(enrichedOrder1.getCustomerFirstName()).isEqualTo(customer1.getFirstName());
        assertThat(enrichedOrder1.getProductName()).isEqualTo(product1.getName());
        assertThat(enrichedOrder1.getProductDescription()).isEqualTo(product1.getDescription());
    }

    /**
     * Tests that multiple orders for the same customer result in the correct enrichment.
     * Input Order 1: Order(id=10, purchaserId=1, customer="Jane Smith", product="Mouse"), timestamp 0
     * Input Order 2: Order(id=11, purchaserId=1, customer="Jane Smith", product="Keyboard"), timestamp 10
     * Output EnrichedOrder 1: (id=10, customer="Jane", product="Mouse")
     * Output EnrichedOrder 2: (id=11, customer="Jane", product="Keyboard")
     */
    @Test
    void testMultipleOrdersSameCustomer() throws Exception {
        Customer customer1 = new Customer(1, "Jane", "Smith", "<EMAIL>");
        Product productA = new Product(201, "Mouse", "Wireless mouse", 0.2);
        Product productB = new Product(202, "Keyboard", "Mechanical keyboard", 0.8);

        Order order1Cust1 = new Order(10L, nowStr(), 1, 1, 201, false)
                                .withCustomer(customer1)
                                .withProduct(productA);
        Order order2Cust1 = new Order(11L, laterStr(10), 1, 2, 202, false)
                                .withCustomer(customer1)
                                .withProduct(productB);

        testHarness.processElement(new StreamRecord<>(order1Cust1, 0L));
        testHarness.processElement(new StreamRecord<>(order2Cust1, 10L));

        List<EnrichedOrder> output = testHarness.extractOutputValues();
        assertThat(output).hasSize(2);

        EnrichedOrder enriched1 = output.get(0);
        assertThat(enriched1.getId()).isEqualTo(order1Cust1.getId());
        assertThat(enriched1.getCustomerFirstName()).isEqualTo(customer1.getFirstName());
        assertThat(enriched1.getProductName()).isEqualTo(productA.getName());

        EnrichedOrder enriched2 = output.get(1);
        assertThat(enriched2.getId()).isEqualTo(order2Cust1.getId());
        assertThat(enriched2.getCustomerFirstName()).isEqualTo(customer1.getFirstName());
        assertThat(enriched2.getProductName()).isEqualTo(productB.getName());
    }

    /**
     * Tests that order counts are maintained independently for different customers.
     * Input Order 1: Order(id=20, purchaserId=1, customer="Alice", product="Monitor"), ts 0
     * Input Order 2: Order(id=21, purchaserId=2, customer="Bob", product="Webcam"), ts 5
     * Input Order 3: Order(id=22, purchaserId=1, customer="Alice", product="Monitor"), ts 10
     * Output for Order 20: (id=20, customer="Alice", product="Monitor")
     * Output for Order 21: (id=21, customer="Bob", product="Webcam")
     * Output for Order 22: (id=22, customer="Alice", product="Monitor")
     */
    @Test
    void testOrdersDifferentCustomers() throws Exception {
        Customer customer1 = new Customer(1, "Alice", "Wonder", "<EMAIL>");
        Customer customer2 = new Customer(2, "Bob", "Builder", "<EMAIL>");
        Product productX = new Product(301, "Monitor", "4K Monitor", 3.0);
        Product productY = new Product(302, "Webcam", "HD Webcam", 0.3);

        Order order1Cust1 = new Order(20L, nowStr(), 1, 1, 301, false)
                                .withCustomer(customer1)
                                .withProduct(productX);
        Order order1Cust2 = new Order(21L, laterStr(5), 2, 1, 302, false)
                                .withCustomer(customer2)
                                .withProduct(productY);
        Order order2Cust1 = new Order(22L, laterStr(10), 1, 3, 301, false)
                                .withCustomer(customer1)
                                .withProduct(productX);
        
        testHarness.processElement(new StreamRecord<>(order1Cust1, 0L));
        testHarness.processElement(new StreamRecord<>(order1Cust2, 5L));
        testHarness.processElement(new StreamRecord<>(order2Cust1, 10L));

        List<EnrichedOrder> results = testHarness.extractOutputValues();
        assertThat(results).hasSize(3);

        EnrichedOrder eo1c1 = results.stream().filter(r -> r.getId() == 20L && r.getPurchaserId() == 1).findFirst().orElseThrow(() -> new AssertionError("Order 20 for customer 1 not found"));
        assertThat(eo1c1.getCustomerFirstName()).isEqualTo("Alice");
        assertThat(eo1c1.getProductName()).isEqualTo(productX.getName());

        EnrichedOrder eo1c2 = results.stream().filter(r -> r.getId() == 21L && r.getPurchaserId() == 2).findFirst().orElseThrow(() -> new AssertionError("Order 21 for customer 2 not found"));
        assertThat(eo1c2.getCustomerFirstName()).isEqualTo("Bob");
        assertThat(eo1c2.getProductName()).isEqualTo(productY.getName());

        EnrichedOrder eo2c1 = results.stream().filter(r -> r.getId() == 22L && r.getPurchaserId() == 1).findFirst().orElseThrow(() -> new AssertionError("Order 22 for customer 1 not found"));
        assertThat(eo2c1.getCustomerFirstName()).isEqualTo("Alice");
        assertThat(eo2c1.getProductName()).isEqualTo(productX.getName());
    }
    
    /**
     * Tests behavior when an input Order is missing customer details.
     * The function should still process the order.
     * Input: Order(id=30, purchaserId=1, product="Stylus", customer=null)
     * Output: EnrichedOrder(id=30, customerFirstName=null, productName="Stylus")
     */
    @Test
    void testOrderMissingCustomer() throws Exception {
        Product product1 = new Product(401, "Stylus", "Digital pen", 0.1);
        Order orderNoCustomer = new Order(30L, nowStr(), 1, 1, 401, false)
                                    .withProduct(product1); // Customer is implicitly null

        testHarness.processElement(new StreamRecord<>(orderNoCustomer, 0L));

        List<EnrichedOrder> output = testHarness.extractOutputValues();
        assertThat(output).hasSize(1);
        EnrichedOrder enriched = output.get(0);

        assertThat(enriched.getId()).isEqualTo(30L);
        assertThat(enriched.getCustomerFirstName()).isNull();
        assertThat(enriched.getCustomerLastName()).isNull();
        assertThat(enriched.getProductName()).isEqualTo(product1.getName());
    }

    /**
     * Tests behavior when an input Order is missing product details.
     * The function should still process the order.
     * Input: Order(id=40, purchaserId=1, customer="Charlie Brown", product=null)
     * Output: EnrichedOrder(id=40, customerFirstName="Charlie", productName=null)
     */
    @Test
    void testOrderMissingProduct() throws Exception {
        Customer customer1 = new Customer(1, "Charlie", "Brown", "<EMAIL>");
        Order orderNoProduct = new Order(40L, nowStr(), 1, 5, 501, false) 
                                    .withCustomer(customer1); // Product is implicitly null

        testHarness.processElement(new StreamRecord<>(orderNoProduct, 0L));

        List<EnrichedOrder> output = testHarness.extractOutputValues();
        assertThat(output).hasSize(1);
        EnrichedOrder enriched = output.get(0);

        assertThat(enriched.getId()).isEqualTo(40L);
        assertThat(enriched.getCustomerFirstName()).isEqualTo(customer1.getFirstName());
        assertThat(enriched.getProductName()).isNull();
        assertThat(enriched.getProductDescription()).isNull();
        // No orderCount to assert
    }
}
