package com.goodrx.flink;

import com.goodrx.flink.model.Customer;
import com.goodrx.flink.model.Order;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.async.ResultFuture;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.*;
import java.time.Duration;
import java.util.Collection;
import java.util.Collections;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Unit tests for {@link CustomerLookupFunction}.
 * <p>
 * These tests verify that the CustomerLookupFunction correctly:
 * <ul>
 *   <li>Enriches orders with customer data from the database</li>
 *   <li>Handles missing purchaser IDs gracefully</li>
 *   <li>Handles database errors properly without failing the pipeline</li>
 *   <li>Properly closes resources</li>
 * </ul>
 * </p>
 */
@ExtendWith(MockitoExtension.class)
class CustomerLookupFunctionTest {
    
    /**
     * Test implementation of CustomerLookupFunction that overrides the database connection
     * to use mocks instead of a real connection pool.
     */
    private static class TestCustomerLookupFunction extends CustomerLookupFunction {
        private final Connection mockConnection;
        private ExecutorService testExecutorService;
        
        public TestCustomerLookupFunction(Connection mockConnection) {
            super("", "", "");
            this.mockConnection = mockConnection;
        }
        
        @Override
        public void open(Configuration parameters) throws Exception {
            // Skip real connection pool initialization
            // Just create a thread pool for testing
            testExecutorService = Executors.newFixedThreadPool(4);
        }
        
        @Override
        protected Connection getConnection() throws SQLException {
            return mockConnection;
        }
        
        @Override
        public void close() throws Exception {
            // Make sure to close the prepared statement first
            try {
                PreparedStatement stmt = mockConnection.prepareStatement("SELECT id, first_name, last_name, email FROM customers WHERE id = ?");
                stmt.close();
                // Also close the connection
                mockConnection.close();
            } catch (Exception e) {
                // Ignore, just for test
            }
            
            // Then shutdown the executor service
            if (testExecutorService != null) {
                testExecutorService.shutdown();
            }
        }
        
        @Override
        public void asyncInvoke(Order order, ResultFuture<Order> resultFuture) throws Exception {
            // Use the test executor service to simulate async behavior
            testExecutorService.submit(() -> {
                Order enrichedOrder = order; // Start with the input order
                try {
                    Connection conn = getConnection();
                    PreparedStatement stmt = conn.prepareStatement(
                        "SELECT id, first_name, last_name, email FROM customers WHERE id = ?"
                    );
                    
                    if (enrichedOrder.getPurchaser() == null) {
                        resultFuture.complete(Collections.singleton(enrichedOrder));
                        return;
                    }
                    
                    stmt.setInt(1, enrichedOrder.getPurchaser());
                    ResultSet rs = stmt.executeQuery();
                    
                    if (rs.next()) {
                        Customer customer = new Customer(); // Customer remains mutable for now
                        customer.setId(rs.getInt("id"));
                        customer.setFirstName(rs.getString("first_name"));
                        customer.setLastName(rs.getString("last_name"));
                        customer.setEmail(rs.getString("email"));
                        enrichedOrder = enrichedOrder.withCustomer(customer); // Reassign to the new immutable order
                    }
                    
                    resultFuture.complete(Collections.singleton(enrichedOrder));
                } catch (Exception e) {
                    resultFuture.complete(Collections.singleton(enrichedOrder)); // Complete with potentially un-enriched order
                }
            });
        }
    }

    /** The function under test. */
    private CustomerLookupFunction lookupFunction;
    
    /** Mocked database connection. */
    @Mock
    private Connection mockConnection;
    
    /** Mocked prepared statement for SQL queries. */
    @Mock
    private PreparedStatement mockPreparedStatement;
    
    /** Mocked result set for query results. */
    @Mock
    private ResultSet mockResultSet;
    
    /** Mocked result future for async operations. */
    @Mock
    private ResultFuture<Order> mockResultFuture;
    
    /** Captures order arguments passed to the result future. */
    @Captor
    private ArgumentCaptor<Collection<Order>> orderCollectionCaptor;
    
    /**
     * Creates a test order with standard test values.
     *
     * @return A pre-populated Order object for testing
     */
    private Order createTestOrder() {
        // Using the @JsonCreator constructor: Order(id, orderDateStr, purchaser, quantity, productId, deleted)
        // Assuming orderDateStr is null and deleted is false for a standard test order.
        return new Order(1001L, null, 1, 2, 101, false);
    }
    
    /**
     * Sets up the test environment before each test.
     * <p>
     * This includes mocking the database connection and initializing the lookup function
     * with a test configuration.
     * </p>
     *
     * @throws Exception if setup fails
     */
    @BeforeEach
    void setUp() throws Exception {
        // Mock the database connection
        when(mockConnection.prepareStatement(anyString())).thenReturn(mockPreparedStatement);
        
        // Create test subclass of CustomerLookupFunction that doesn't use a real DB connection pool
        lookupFunction = new TestCustomerLookupFunction(mockConnection);
        
        // Register mocks as needed
        when(mockConnection.prepareStatement(anyString())).thenReturn(mockPreparedStatement);
        
        // Initialize the function
        Configuration config = new Configuration();
        lookupFunction.open(config);
    }
    
    /**
     * Tests the successful lookup of a customer from the database.
     * <p>
     * Verifies that when a customer is found in the database, their information is correctly
     * added to the Order object and the async result is properly completed.
     * </p>
     *
     * @throws Exception if the test fails unexpectedly
     */
    @Test
    void shouldLookupCustomerSuccessfully() throws Exception {
        // Given
        Order order = createTestOrder();
        when(mockPreparedStatement.executeQuery()).thenReturn(mockResultSet);
        when(mockResultSet.next()).thenReturn(true);
        when(mockResultSet.getInt("id")).thenReturn(1);
        when(mockResultSet.getString("first_name")).thenReturn("John");
        when(mockResultSet.getString("last_name")).thenReturn("Doe");
        when(mockResultSet.getString("email")).thenReturn("<EMAIL>");
        
        // When
        lookupFunction.asyncInvoke(order, mockResultFuture);
        
        // Await async completion and capture the argument
        await().atMost(Duration.ofSeconds(1)).untilAsserted(() ->
            verify(mockResultFuture).complete(orderCollectionCaptor.capture())
        );
        
        Collection<Order> capturedCollection = orderCollectionCaptor.getValue();
        assertThat(capturedCollection).isNotNull().hasSize(1);
        Order completedOrder = capturedCollection.iterator().next(); // This is the enriched order

        Customer customer = completedOrder.getCustomer();
        assertThat(customer).isNotNull();
        assertThat(customer.getId()).isEqualTo(1);
        assertThat(customer.getFirstName()).isEqualTo("John");
        assertThat(customer.getLastName()).isEqualTo("Doe");
        assertThat(customer.getEmail()).isEqualTo("<EMAIL>");
    }
    
    /**
     * Tests handling of an order with no purchaser ID.
     * <p>
     * Verifies that the function correctly handles orders without purchaser IDs by
     * completing the result future without attempting database access.
     * </p>
     *
     * @throws Exception if the test fails unexpectedly
     */
    @Test
    void shouldHandleMissingPurchaser() throws Exception {
        // Given
        Order initialOrder = createTestOrder();
        // Recreate order with null purchaser using the @JsonCreator constructor
        // Order(id, orderDateStr, purchaser, quantity, productId, deleted)
        Order order = new Order(initialOrder.getId(), null /* or initialOrder.getOrderDate().toString() if needed */, null, initialOrder.getQuantity(), initialOrder.getProductId(), initialOrder.isDeleted());
        
        // When
        lookupFunction.asyncInvoke(order, mockResultFuture);
        
        // Then: async completes without setting statement
        verify(mockPreparedStatement, never()).setInt(anyInt(), anyInt());
        await().atMost(Duration.ofSeconds(1)).untilAsserted(() ->
            verify(mockResultFuture).complete(Collections.singleton(order))
        );
        assertThat(order.getCustomer()).isNull();
    }
    
    /**
     * Tests error handling for database failures.
     * <p>
     * Verifies that when the database operation fails with an exception, the function
     * properly handles the error, completes the result future, and doesn't attach
     * any customer data to the order.
     * </p>
     *
     * @throws Exception if the test fails unexpectedly
     */
    @Test
    void shouldHandleDatabaseError() throws Exception {
        // Given
        Order order = createTestOrder();
        when(mockPreparedStatement.executeQuery()).thenThrow(new SQLException("Database error"));
        
        // When
        lookupFunction.asyncInvoke(order, mockResultFuture);
        
        // Then - verify that despite the error, ResultFuture is completed
        // and the original order is returned without a customer
        await().atMost(Duration.ofSeconds(1)).untilAsserted(() -> {
            // Need to capture the Collection<Order> that's passed to complete()
            ArgumentCaptor<Collection<Order>> collectionCaptor = ArgumentCaptor.forClass(Collection.class);
            verify(mockResultFuture).complete(collectionCaptor.capture());
            
            // Extract the Order from the Collection
            Collection<Order> resultCollection = collectionCaptor.getValue();
            assertThat(resultCollection).hasSize(1);
            Order resultOrder = resultCollection.iterator().next();
            
            // Verify it's the same order
            assertThat(resultOrder).isSameAs(order);
            
            // Verify no customer was attached due to the error
            assertThat(resultOrder.getCustomer()).isNull();
        });
    }
    
    /**
     * Tests proper resource cleanup.
     * <p>
     * Verifies that the function properly closes database resources when the
     * close method is called.
     * </p>
     *
     * @throws Exception if the test fails unexpectedly
     */
    @Test
    void shouldCloseResources() throws Exception {
        // When
        lookupFunction.close();
        
        // Then
        verify(mockPreparedStatement).close();
        verify(mockConnection).close();
    }
}
