package com.goodrx.flink.enrichment;

import com.goodrx.flink.enrichment.model.OrderEvent;
import org.apache.flink.streaming.api.operators.ProcessOperator;
import org.apache.flink.streaming.util.OneInputStreamOperatorTestHarness;
import org.apache.flink.util.OutputTag;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.util.List;
import java.util.stream.Collectors;

public class OrderGrpcEnrichmentJobTest {

    @Test
    public void testJsonToOrderEventParser_validJson() throws Exception {
        final OutputTag<String> malformedOutputTag = new OutputTag<String>("malformed-test"){};
        OrderGrpcEnrichmentJob.JsonToOrderEventParser parser = 
            new OrderGrpcEnrichmentJob.JsonToOrderEventParser(malformedOutputTag);

        // Using OneInputStreamOperatorTestHarness for non-keyed ProcessFunction
        OneInputStreamOperatorTestHarness<String, OrderEvent> harness = 
            new OneInputStreamOperatorTestHarness<>(new ProcessOperator<>(parser));

        harness.open();

        String validJson = "{\"orderId\":\"o1\",\"customerId\":\"c1\",\"productId\":\"p1\",\"quantity\":10,\"price\":99.99,\"eventTimestamp\":1678886400000}";
        harness.processElement(validJson, 0L);

        List<OrderEvent> mainOutputValid = harness.extractOutputValues();
        java.util.Queue<org.apache.flink.streaming.runtime.streamrecord.StreamRecord<String>> sideOutputQueueValid = harness.getSideOutput(malformedOutputTag);
        List<String> sideOutputListValid = new java.util.ArrayList<>();
        if (sideOutputQueueValid != null) {
            sideOutputListValid = sideOutputQueueValid.stream()
                                        .map(org.apache.flink.streaming.runtime.streamrecord.StreamRecord::getValue)
                                        .collect(Collectors.toList());
        }

        assertEquals(1, mainOutputValid.size(), "Should have one valid OrderEvent");
        OrderEvent event = mainOutputValid.get(0);
        assertEquals("o1", event.getOrderId());
        assertEquals("c1", event.getCustomerId());
        assertEquals("p1", event.getProductId());
        assertEquals(10, event.getQuantity());
        assertEquals(99.99, event.getPrice(), 0.001);
        assertEquals(1678886400000L, event.getEventTimestamp());

        assertTrue(sideOutputListValid.isEmpty(), "Side output for malformed records should be empty");

        harness.close();
    }

    @Test
    public void testJsonToOrderEventParser_invalidJson() throws Exception {
        final OutputTag<String> malformedOutputTag = new OutputTag<String>("malformed-test"){};
        OrderGrpcEnrichmentJob.JsonToOrderEventParser parser = 
            new OrderGrpcEnrichmentJob.JsonToOrderEventParser(malformedOutputTag);

        OneInputStreamOperatorTestHarness<String, OrderEvent> harness = 
            new OneInputStreamOperatorTestHarness<>(new ProcessOperator<>(parser));
        
        harness.open();

        String invalidJson = "{\"orderId\":\"o2\", \"malformed_field\": ...}";
        harness.processElement(invalidJson, 0L);

        List<OrderEvent> mainOutputInvalid = harness.extractOutputValues();
        java.util.Queue<org.apache.flink.streaming.runtime.streamrecord.StreamRecord<String>> sideOutputQueueForInvalid = harness.getSideOutput(malformedOutputTag);
        List<String> sideOutputListInvalid = new java.util.ArrayList<>();
        if (sideOutputQueueForInvalid != null) {
            sideOutputListInvalid = sideOutputQueueForInvalid.stream()
                                        .map(org.apache.flink.streaming.runtime.streamrecord.StreamRecord::getValue)
                                        .collect(Collectors.toList());
        }

        assertTrue(mainOutputInvalid.isEmpty(), "Main output should be empty for invalid JSON");
        assertEquals(1, sideOutputListInvalid.size(), "Should have one malformed record in side output");
        assertEquals(invalidJson, sideOutputListInvalid.get(0));

        harness.close();
    }
}
