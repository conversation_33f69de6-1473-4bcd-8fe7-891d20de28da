package com.goodrx.flink;

import com.goodrx.flink.model.Order;
import com.goodrx.flink.model.Product;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.async.ResultFuture;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.sql.*;
import java.time.Duration;
import java.util.Collection;
import java.util.Collections;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import static org.awaitility.Awaitility.await;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ProductLookupFunctionTest {

    /**
     * Test implementation of ProductLookupFunction that overrides the database connection
     * to use mocks instead of a real connection pool.
     */
    private static class TestProductLookupFunction extends ProductLookupFunction {
        private final Connection mockConnection;

        public TestProductLookupFunction(Connection mockConnection) {
            super("", "", "");
            this.mockConnection = mockConnection;
        }

        @Override
        public void open(Configuration parameters) throws Exception {
            this.executorService = Executors.newSingleThreadExecutor();
        }

        @Override
        protected Connection getConnection() throws SQLException {
            return mockConnection;
        }

        @Override
        public void close() throws Exception {
            if (this.executorService != null) {
                this.executorService.shutdown();
                if (!this.executorService.awaitTermination(5, TimeUnit.SECONDS)) {
                    this.executorService.shutdownNow();
                }
            }
        }

        @Override
        public void asyncInvoke(Order order, ResultFuture<Order> resultFuture) throws Exception {
            executorService.submit(() -> {
                Order enrichedOrder = order;
                Connection conn = null;
                PreparedStatement stmt = null;
                ResultSet rs = null;
                try {
                    conn = getConnection();
                    stmt = conn.prepareStatement(
                        "SELECT id, name, description, weight FROM products WHERE id = ?"
                    );
                    
                    if (enrichedOrder.getProductId() == null) {
                        resultFuture.complete(Collections.singleton(enrichedOrder));
                        return;
                    }
                    
                    stmt.setInt(1, enrichedOrder.getProductId());
                    rs = stmt.executeQuery();
                    
                    if (rs.next()) {
                        Product product = new Product(); // Product remains mutable for now
                        product.setId(rs.getInt("id"));
                        product.setName(rs.getString("name"));
                        product.setDescription(rs.getString("description"));
                        product.setWeight(rs.getDouble("weight"));
                        enrichedOrder = enrichedOrder.withProduct(product); // Reassign to the new immutable order
                    }
                    
                    resultFuture.complete(Collections.singleton(enrichedOrder));
                } catch (Exception e) {
                    // In case of an exception, still try to complete the future with the original/partially enriched order
                    resultFuture.complete(Collections.singleton(enrichedOrder)); 
                } finally {
                    // Ensure resources are closed even if exceptions occur
                    if (rs != null) { // Assuming rs is declared if used
                        try {
                            rs.close();
                        } catch (SQLException e) {
                            // log or ignore in test
                        }
                    }
                    if (stmt != null) {
                        try {
                            stmt.close();
                        } catch (SQLException e) {
                            // log or ignore in test
                        }
                    }
                    if (conn != null) {
                        try {
                            conn.close();
                        } catch (SQLException e) {
                            // log or ignore in test
                        }
                    }
                }
            });
        }
    }

    /** The function under test. */
    private ProductLookupFunction lookupFunction;
    
    @Mock
    private Connection mockConnection;
    
    @Mock
    private PreparedStatement mockPreparedStatement;
    
    @Mock
    private ResultSet mockResultSet;
    
    @Mock
    private ResultFuture<Order> mockResultFuture;
    
    @Captor
    private ArgumentCaptor<Collection<Order>> orderCollectionCaptor;
    
    private Order createTestOrder() {
        // Using the @JsonCreator constructor: Order(id, orderDateStr, purchaser, quantity, productId, deleted)
        // Assuming orderDateStr is null and deleted is false for a standard test order.
        return new Order(1001L, null, 1, 2, 101, false);
    }
    
    /**
     * Sets up the test environment before each test.
     * <p>
     * This includes:
     * <ul>
     *   <li>Configuring mock database connection and statement behavior</li>
     *   <li>Creating the lookup function with a mocked connection</li>
     *   <li>Initializing the function with a Configuration</li>
     * </ul>
     * </p>
     *
     * @throws Exception if initialization fails
     */
    @BeforeEach
    void setUp() throws Exception {
        // Mock the database connection
        when(mockConnection.prepareStatement(anyString())).thenReturn(mockPreparedStatement);
        
        // Create test subclass of ProductLookupFunction
        lookupFunction = new TestProductLookupFunction(mockConnection);
        
        // Initialize the function
        Configuration config = new Configuration();
        lookupFunction.open(config);
    }
    
    @Test
    void shouldLookupProductSuccessfully() throws Exception {
        // Given
        Order order = createTestOrder();
        when(mockPreparedStatement.executeQuery()).thenReturn(mockResultSet);
        when(mockResultSet.next()).thenReturn(true);
        when(mockResultSet.getInt("id")).thenReturn(101);
        when(mockResultSet.getString("name")).thenReturn("Test Product");
        when(mockResultSet.getString("description")).thenReturn("Test Description");
        when(mockResultSet.getDouble("weight")).thenReturn(1.5);
        
        // When
        lookupFunction.asyncInvoke(order, mockResultFuture);
        
        // Then: await async completion and verify interactions
        await().atMost(Duration.ofSeconds(5)).untilAsserted(() -> {
            // Verify setInt was called
            verify(mockPreparedStatement).setInt(1, order.getProductId());
            // Capture the collection of orders passed to complete()
            verify(mockResultFuture).complete(orderCollectionCaptor.capture());
        });
        
        Collection<Order> capturedCollection = orderCollectionCaptor.getValue();
        assertThat(capturedCollection).isNotNull().hasSize(1);
        Order completedOrder = capturedCollection.iterator().next();
        Product product = completedOrder.getProduct();
        assertThat(product).isNotNull();
        assertThat(product.getId()).isEqualTo(101);
        assertThat(product.getName()).isEqualTo("Test Product");
        assertThat(product.getDescription()).isEqualTo("Test Description");
        assertThat(product.getWeight()).isEqualTo(1.5);
    }
    
    @Test
    void shouldHandleMissingProductId() throws Exception {
        // Given
        Order initialOrder = createTestOrder();
        // Recreate order with null productId using the @JsonCreator constructor
        // Order(id, orderDateStr, purchaser, quantity, productId, deleted)
        Order order = new Order(initialOrder.getId(), null /* or initialOrder.getOrderDate().toString() if needed */, initialOrder.getPurchaser(), initialOrder.getQuantity(), null, initialOrder.isDeleted());
        
        // When
        lookupFunction.asyncInvoke(order, mockResultFuture);
        
        // Then: no parameter, await completion
        verify(mockPreparedStatement, never()).setInt(anyInt(), anyInt());
        await().atMost(Duration.ofSeconds(1)).untilAsserted(() ->
            verify(mockResultFuture).complete(orderCollectionCaptor.capture())
        );
        Collection<Order> capturedCollection = orderCollectionCaptor.getValue();
        assertThat(capturedCollection).isNotNull().hasSize(1);
        Order completedOrder = capturedCollection.iterator().next();
        assertThat(completedOrder.getProduct()).isNull(); // Product should be null
        assertThat(completedOrder.getId()).isEqualTo(order.getId()); // Other fields should match original
    }
    
    @Test
    void shouldHandleDatabaseError() throws Exception {
        // Given
        Order order = createTestOrder();
        when(mockPreparedStatement.executeQuery()).thenThrow(new SQLException("Database error"));
        
        // When/Then
        lookupFunction.asyncInvoke(order, mockResultFuture);
        await().atMost(Duration.ofSeconds(1)).untilAsserted(() ->
            verify(mockResultFuture).complete(orderCollectionCaptor.capture())
        );
        Collection<Order> capturedCollection = orderCollectionCaptor.getValue();
        assertThat(capturedCollection).isNotNull().hasSize(1);
        Order completedOrder = capturedCollection.iterator().next();
        assertThat(completedOrder.getProduct()).isNull(); // Product should be null due to error
        assertThat(completedOrder.getId()).isEqualTo(order.getId()); // Other fields should match original
    }
    
    @Test
    void shouldCloseResources() throws Exception {
        // Given: An order that would cause asyncInvoke to run and use the prepared statement
        Order order = createTestOrder(); // Assuming this creates an order with a productId

        // When: Invoke the async function to ensure resources are used
        lookupFunction.asyncInvoke(order, mockResultFuture);

        // And: Wait for the async operation to complete (including finally blocks)
        // We need to ensure the lambda in asyncInvoke has finished.
        await().atMost(Duration.ofSeconds(5)).untilAsserted(() ->
            verify(mockResultFuture, atLeastOnce()).complete(any()) // Ensure async part ran
        );
        
        // And When: The function itself is closed (TestProductLookupFunction.close mainly handles executor)
        lookupFunction.close();
        
        // Then: Verify that the statement and connection obtained and used within asyncInvoke were closed.
        verify(mockPreparedStatement, times(1)).close(); // Closed in asyncInvoke's finally block
        verify(mockConnection, times(1)).close();      // Closed in asyncInvoke's finally block
    }
}
