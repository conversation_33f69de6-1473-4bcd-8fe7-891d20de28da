package com.goodrx.flink;

import com.goodrx.flink.model.Order;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

class DebeziumOrderDeserializerTest {
    
    private static final Logger LOG = LoggerFactory.getLogger(DebeziumOrderDeserializerTest.class);
    private OrderEnrichmentJob.DebeziumOrderDeserializer deserializer;
    
    @BeforeEach
    void setUp() {
        deserializer = new OrderEnrichmentJob.DebeziumOrderDeserializer();
    }
    
    @Test
    void shouldDeserializeValidOrder() throws Exception {
        // Given
        String json = TestData.createDebeziumOrderJson();
        
        // When
        Order order = deserializer.map(json);
        
        // Then
        assertThat(order).isNotNull();
        assertThat(order.getId()).isEqualTo(1001L);
        assertThat(order.getPurchaser()).isEqualTo(1);
        assertThat(order.getQuantity()).isEqualTo(2);
        assertThat(order.getProductId()).isEqualTo(101);
        assertThat(order.getOrderDate()).isNotNull();
        assertThat(order.isDeleted()).isFalse();
    }
    
    @Test
    void shouldHandleDeletedOperation() throws Exception {
        // Given
        String json = TestData.createDebeziumOrderJson();
        // Change operation to delete
        json = json.replace("\"op\": \"c\"", "\"op\": \"d\"");
        
        // When
        Order order = deserializer.map(json);
        
        // Then
        assertThat(order).isNotNull();
        assertThat(order.isDeleted()).isTrue();
    }
    
    @Test
    void shouldHandleMissingAfterField() throws Exception {
        // Given
        String invalidJson = "{\"op\":\"c\"}";
        
        // When
        Order order = deserializer.map(invalidJson);
        
        // Then
        assertThat(order).isNull();
    }
    
    @Test
    void shouldHandleInvalidJson() {
        // Given
        String invalidJson = "{invalid-json";
        
        // When/Then
        assertThatThrownBy(() -> deserializer.map(invalidJson))
            .isInstanceOf(Exception.class);
    }
}
