---
apiVersion: flink.apache.org/v1beta1
kind: FlinkDeployment
metadata:
  name: order-enrichment
  namespace: flink
spec:
  image: ************.dkr.ecr.us-west-2.amazonaws.com/flink-jobs:latest
  flinkVersion: v1_17
  serviceAccount: flink
  jobManager:
    resource:
      memory: "2048m"
      cpu: 1
    podTemplate:
      spec:
        containers:
        - name: flink-main-container
          env:
          - name: FLINK_PROPERTIES
            value: |
              jobmanager.memory.process.size: 2048m
              taskmanager.memory.process.size: 2048m
              state.backend: rocksdb
              state.checkpoints.dir: file:///tmp/flink-checkpoints
              state.savepoints.dir: file:///tmp/flink-savepoints
              execution.checkpointing.interval: 10s
              execution.checkpointing.mode: EXACTLY_ONCE
  taskManager:
    resource:
      memory: "2048m"
      cpu: 1
  job:
    jarURI: local:///opt/flink/usrlib/flink-jobs.jar
    parallelism: 2
    upgradeMode: stateless
    state: running
