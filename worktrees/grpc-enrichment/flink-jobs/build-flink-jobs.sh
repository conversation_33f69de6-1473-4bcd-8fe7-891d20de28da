#!/bin/bash
set -e

# Build the Flink jobs

docker buildx build --platform linux/amd64 -t 739368017290.dkr.ecr.us-west-2.amazonaws.com/flink-jobs:latest --push .

# Ensure ECR repository exists (create if missing, continue on error)
aws ecr describe-repositories --repository-names flink-jobs --region us-west-2 > /dev/null 2>&1 || \
  aws ecr create-repository --repository-name flink-jobs --region us-west-2 > /dev/null 2>&1 || true
# Tag and push to ECR
aws ecr get-login-password --region us-west-2 | docker login --username AWS --password-stdin 739368017290.dkr.ecr.us-west-2.amazonaws.com
docker tag flink-jobs:latest 739368017290.dkr.ecr.us-west-2.amazonaws.com/flink-jobs:latest
docker push 739368017290.dkr.ecr.us-west-2.amazonaws.com/flink-jobs:latest

echo "Flink jobs built and pushed successfully!"
