---
# Source: flink-platform/templates/order-enrichment-job.yaml
apiVersion: flink.apache.org/v1beta1
kind: FlinkDeployment
metadata:
  name: order-enrichment
  namespace: flink
spec:
  image: ************.dkr.ecr.us-west-2.amazonaws.com/flink-jobs:latest
  flinkVersion: v1_17
  serviceAccount: flink
  jobManager:
    resource:
      memory: "2048m"
      cpu: 1
  taskManager:
    resource:
      memory: "2048m"
      cpu: 1
  flinkConfiguration:
    jobmanager.memory.process.size: 2048m
    taskmanager.memory.process.size: 2048m
    taskmanager.numberOfTaskSlots: "2"
    state.backend: rocksdb
    state.checkpoints.dir: s3://arch-sink-checkpoints/flink/checkpoints  # now using S3 for checkpoints
    state.savepoints.dir: s3://arch-sink-savepoints/flink/savepoints    # now using S3 for savepoints
    execution.checkpointing.interval: 10s
    execution.checkpointing.mode: EXACTLY_ONCE
  job:
    jarURI: local:///opt/flink/usrlib/flink-jobs.jar
    parallelism: 2
    upgradeMode: savepoint
    state: running
---
# Source: flink-platform/templates/session.yaml
apiVersion: flink.apache.org/v1beta1
kind: FlinkDeployment
metadata:
  name: flink-session
  namespace: flink
spec:
  image: apache/flink:1.17.1
  flinkVersion: v1_17
  serviceAccount: flink
  jobManager:
    replicas: 1
    resource:
      memory: "2Gi"
      cpu: 1
    podTemplate:
      spec:
        containers:
          - name: jobmanager
            resources:
              requests:
                memory: "2Gi"
                cpu: 1
              limits:
                memory: "2Gi"
                cpu: 2
  taskManager:
    replicas: 2
    resource:
      memory: "2Gi"
      cpu: 1
    podTemplate:
      spec:
        containers:
          - name: taskmanager
            resources:
              requests:
                memory: "2Gi"
                cpu: 1
              limits:
                memory: "4Gi"
                cpu: 2
  flinkConfiguration:
    jobmanager.memory.process.size: 2048m
    taskmanager.memory.process.size: 2048m
    taskmanager.numberOfTaskSlots: "2"
    state.backend: rocksdb
    state.checkpoints.dir: s3://arch-sink-checkpoints/flink/checkpoints  # now using S3 for checkpoints
    state.savepoints.dir: s3://arch-sink-savepoints/flink/savepoints    # now using S3 for savepoints
    execution.checkpointing.interval: 10s
    execution.checkpointing.mode: EXACTLY_ONCE
    execution.checkpointing.timeout: 5min
    restart-strategy: fixed-delay
    restart-strategy.fixed-delay.attempts: "3"
    restart-strategy.fixed-delay.delay: 10s
---
# Source: flink-platform/templates/example-job.yaml
apiVersion: flink.apache.org/v1beta1
kind: FlinkSessionJob
metadata:
  name: event-enrichment
  namespace: flink
spec:
  deploymentName: flink-session
  job:
    jarURI: local:///opt/flink/examples/streaming/StateMachineExample.jar
    parallelism: 2
    upgradeMode: stateless
    state: running
