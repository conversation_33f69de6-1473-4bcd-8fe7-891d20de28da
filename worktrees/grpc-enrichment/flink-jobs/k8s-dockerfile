FROM maven:3.8.4-openjdk-17-slim AS builder
WORKDIR /app
COPY pom.xml .
COPY src ./src
RUN mvn clean package

FROM flink:1.18.1-java17

# Add S3 filesystem plugin - note that the directory MUST be named 's3' for Flink to find it
RUN mkdir -p /opt/flink/plugins/s3 && \
    curl -L https://repo1.maven.org/maven2/org/apache/flink/flink-s3-fs-hadoop/1.18.1/flink-s3-fs-hadoop-1.18.1.jar \
    -o /opt/flink/plugins/s3/flink-s3-fs-hadoop-1.18.1.jar

# Create a writable directory for Flink configuration
RUN mkdir -p /writable-conf && \
    cp -r /opt/flink/conf/* /writable-conf/ && \
    chmod -R 777 /writable-conf/

# Create directories for checkpoints and savepoints
RUN mkdir -p /tmp/flink-checkpoints /tmp/flink-savepoints && \
    chmod -R 777 /tmp/flink-checkpoints /tmp/flink-savepoints

# Copy our application jar
COPY --from=builder /app/target/flink-jobs-1.0-SNAPSHOT.jar /opt/flink/usrlib/flink-jobs.jar

# Create a custom entrypoint script
RUN echo '#!/bin/bash \n\
set -e \n\
\n\
# Use writable configuration directory \n\
export FLINK_CONF_DIR=/writable-conf \n\
\n\
# Process environment variables for configuration \n\
if [ -n "${FLINK_PROPERTIES}" ]; then \n\
    echo "${FLINK_PROPERTIES}" >> "$FLINK_CONF_DIR/flink-conf.yaml" \n\
    echo "Appended FLINK_PROPERTIES to configuration." \n\
fi \n\
\n\
# Set the JobManager RPC address \n\
echo "jobmanager.rpc.address: $(hostname -f)" >> "$FLINK_CONF_DIR/flink-conf.yaml" \n\
echo "blob.server.port: 6124" >> "$FLINK_CONF_DIR/flink-conf.yaml" \n\
echo "query.server.port: 6125" >> "$FLINK_CONF_DIR/flink-conf.yaml" \n\
\n\
# Print the configuration for debugging \n\
echo "Using Flink configuration directory: $FLINK_CONF_DIR" \n\
echo "Configuration contents:" \n\
cat "$FLINK_CONF_DIR/flink-conf.yaml" \n\
\n\
# Start Flink JobManager \n\
echo "Starting Flink JobManager with writable config..." \n\
exec /opt/flink/bin/kubernetes-jobmanager.sh kubernetes-application \n\
' > /custom-entrypoint.sh && chmod +x /custom-entrypoint.sh

# Use our custom entrypoint
ENTRYPOINT ["/custom-entrypoint.sh"]
