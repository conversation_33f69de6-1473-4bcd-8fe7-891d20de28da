---
# Source: flink-platform/templates/order-enrichment-job.yaml
apiVersion: flink.apache.org/v1beta1
kind: FlinkDeployment
metadata:
  name: order-enrichment
  namespace: flink
spec:
  image: ************.dkr.ecr.us-west-2.amazonaws.com/flink-jobs:latest
  flinkVersion: v1_17
  serviceAccount: flink
  flinkConfiguration:
    jobmanager.memory.process.size: 2048m
    taskmanager.memory.process.size: 2048m
    taskmanager.numberOfTaskSlots: "2"
    state.backend: rocksdb
    state.checkpoints.dir: s3://arch-sink-checkpoints/flink/checkpoints
    state.savepoints.dir: s3://arch-sink-savepoints/flink/savepoints
    execution.checkpointing.interval: 10s
    execution.checkpointing.mode: EXACTLY_ONCE
    s3.endpoint: s3.us-west-2.amazonaws.com
    s3.path.style.access: "false"
    s3.region: us-west-2
  podTemplate:
    spec:
      # Add an init container to copy the config to a writable location
      initContainers:
        - name: copy-flink-config
          image: busybox:1.35.0
          command: ["sh", "-c", "mkdir -p /writable-conf && cp -r /opt/flink/conf/* /writable-conf/ && chmod -R 777 /writable-conf/"]
          volumeMounts:
            - name: flink-config-volume
              mountPath: /opt/flink/conf
            - name: writable-conf-volume
              mountPath: /writable-conf
      containers:
        - name: flink-main-container
          volumeMounts:
            - name: writable-conf-volume
              mountPath: /opt/flink/conf
      volumes:
        - name: flink-config-volume
          emptyDir: {}
        - name: writable-conf-volume
          emptyDir: {}
  jobManager:
    resource:
      memory: "2048m"
      cpu: 1
  taskManager:
    resource:
      memory: "2048m"
      cpu: 1
  job:
    jarURI: local:///opt/flink/usrlib/flink-jobs.jar
    parallelism: 2
    upgradeMode: stateless
    state: running
