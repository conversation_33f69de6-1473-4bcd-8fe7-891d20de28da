FROM maven:3.8.4-openjdk-17-slim AS builder
WORKDIR /app
COPY pom.xml .
COPY src ./src
RUN mvn clean package

FROM flink:1.18.1-java17

# Add S3 filesystem plugin - note that the directory MUST be named 's3' for Flink to find it
RUN mkdir -p /opt/flink/plugins/s3 && \
    curl -L https://repo1.maven.org/maven2/org/apache/flink/flink-s3-fs-hadoop/1.18.1/flink-s3-fs-hadoop-1.18.1.jar \
    -o /opt/flink/plugins/s3/flink-s3-fs-hadoop-1.18.1.jar

# Copy our custom entrypoint script
COPY custom-entrypoint.sh /custom-entrypoint.sh
RUN chmod +x /custom-entrypoint.sh

COPY --from=builder /app/target/flink-jobs-1.0-SNAPSHOT.jar /opt/flink/usrlib/flink-jobs.jar

# Create directories for checkpoints and savepoints
RUN mkdir -p /tmp/flink-checkpoints /tmp/flink-savepoints && \
    chmod -R 777 /tmp/flink-checkpoints /tmp/flink-savepoints

# Use our custom entrypoint
ENTRYPOINT ["/custom-entrypoint.sh"]

