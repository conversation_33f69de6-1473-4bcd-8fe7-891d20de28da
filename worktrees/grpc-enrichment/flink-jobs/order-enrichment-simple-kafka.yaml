---
apiVersion: flink.apache.org/v1beta1
kind: FlinkDeployment
metadata:
  name: order-enrichment
  namespace: flink
spec:
  image: ************.dkr.ecr.us-west-2.amazonaws.com/flink-jobs:latest
  flinkVersion: v1_17
  serviceAccount: flink
  jobManager:
    resource:
      memory: "2048m"
      cpu: 1
  taskManager:
    resource:
      memory: "2048m"
      cpu: 1
  flinkConfiguration:
    taskmanager.numberOfTaskSlots: "2"
    # Kafka configuration
    bootstrap.servers: "kafka.confluent.svc.cluster.local:9092"
    logger.kafka.name: "org.apache.kafka"
    logger.kafka.level: "INFO"
    # Default Flink checkpointing configuration
    state.backend: filesystem
    state.checkpoints.dir: file:///tmp/flink-checkpoints
    state.savepoints.dir: file:///tmp/flink-savepoints
    execution.checkpointing.interval: 10s
    execution.checkpointing.mode: EXACTLY_ONCE
  job:
    jarURI: local:///opt/flink/usrlib/flink-jobs.jar
    parallelism: 1
    upgradeMode: stateless
    state: running
