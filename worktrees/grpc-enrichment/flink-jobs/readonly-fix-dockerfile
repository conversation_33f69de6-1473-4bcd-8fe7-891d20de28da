FROM maven:3.8.4-openjdk-17-slim AS builder
WORKDIR /app
COPY pom.xml .
COPY src ./src
RUN mvn clean package

FROM flink:1.18.1-java17

# Add S3 filesystem plugin - note that the directory MUST be named 's3' for Flink to find it
RUN mkdir -p /opt/flink/plugins/s3 && \
    curl -L https://repo1.maven.org/maven2/org/apache/flink/flink-s3-fs-hadoop/1.18.1/flink-s3-fs-hadoop-1.18.1.jar \
    -o /opt/flink/plugins/s3/flink-s3-fs-hadoop-1.18.1.jar

# Copy our application jar
COPY --from=builder /app/target/flink-jobs-1.0-SNAPSHOT.jar /opt/flink/usrlib/flink-jobs.jar

# Create writable directories
RUN mkdir -p /writable-conf /tmp/flink-checkpoints /tmp/flink-savepoints && \
    cp -r /opt/flink/conf/* /writable-conf/ && \
    chmod -R 777 /writable-conf/ /tmp/flink-checkpoints /tmp/flink-savepoints

# Create a completely new entrypoint script that doesn't try to modify the read-only files
COPY <<EOF /custom-entrypoint.sh
#!/bin/bash
set -e

# Use writable configuration directory
export FLINK_CONF_DIR=/writable-conf

# Process environment variables for configuration
if [ -n "\${FLINK_PROPERTIES}" ]; then
    echo "\${FLINK_PROPERTIES}" >> "\$FLINK_CONF_DIR/flink-conf.yaml"
    echo "Appended FLINK_PROPERTIES to configuration."
fi

# Set the JobManager RPC address
echo "jobmanager.rpc.address: \$(hostname -f)" >> "\$FLINK_CONF_DIR/flink-conf.yaml"
echo "blob.server.port: 6124" >> "\$FLINK_CONF_DIR/flink-conf.yaml"
echo "query.server.port: 6125" >> "\$FLINK_CONF_DIR/flink-conf.yaml"

# Print the configuration for debugging
echo "Using Flink configuration directory: \$FLINK_CONF_DIR"
echo "Configuration contents:"
cat "\$FLINK_CONF_DIR/flink-conf.yaml"

# Start Flink JobManager directly without using the original entrypoint
echo "Starting Flink JobManager with writable config..."
exec /opt/flink/bin/kubernetes-jobmanager.sh kubernetes-application
EOF

RUN chmod +x /custom-entrypoint.sh

# Use our custom entrypoint
ENTRYPOINT ["/custom-entrypoint.sh"]
