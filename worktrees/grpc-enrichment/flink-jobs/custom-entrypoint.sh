#!/bin/bash
set -e

# Create a writable directory for Flink configuration
mkdir -p /tmp/flink-conf

# Copy the mounted configuration to the writable directory
cp -r /opt/flink/conf/* /tmp/flink-conf/

# Set environment variables for the Flink configuration
export FLINK_CONF_DIR=/tmp/flink-conf

# Process environment variables for configuration
if [ -n "${FLINK_PROPERTIES}" ]; then
    echo "${FLINK_PROPERTIES}" >> "$FLINK_CONF_DIR/flink-conf.yaml"
    echo "Appended FLINK_PROPERTIES to configuration."
fi

# Set the JobManager RPC address
echo "jobmanager.rpc.address: $(hostname -f)" >> "$FLINK_CONF_DIR/flink-conf.yaml"
echo "blob.server.port: 6124" >> "$FLINK_CONF_DIR/flink-conf.yaml"
echo "query.server.port: 6125" >> "$FLINK_CONF_DIR/flink-conf.yaml"

# Create directories for checkpoints and savepoints
mkdir -p /tmp/flink-checkpoints
mkdir -p /tmp/flink-savepoints
chmod -R 777 /tmp/flink-checkpoints
chmod -R 777 /tmp/flink-savepoints

# Print the configuration for debugging
echo "Using Flink configuration directory: $FLINK_CONF_DIR"
echo "Configuration contents:"
cat "$FLINK_CONF_DIR/flink-conf.yaml"

# Start Flink JobManager
echo "Starting Flink JobManager with writable config..."
exec /opt/flink/bin/kubernetes-jobmanager.sh kubernetes-application
