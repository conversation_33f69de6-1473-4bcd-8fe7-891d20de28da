apiVersion: batch/v1
kind: Job
metadata:
  name: configure-connector
  namespace: {{ .Values.global.namespace }}
  annotations:
    "helm.sh/hook": post-install,post-upgrade
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
spec:
  template:
    spec:
      containers:
      - name: configure-connector
        image: curlimages/curl
        command:
        - /bin/sh
        - -c
        - |
          until curl -s http://{{ .Values.connect.name }}:8083/connectors; do
            echo "Waiting for Connect to be ready..."
            sleep 10
          done
          
          curl -X POST -H "Content-Type: application/json" --data '{
            "name": "{{ .Values.connector.name }}",
            "config": {
              "connector.class": "io.debezium.connector.postgresql.PostgresConnector",
              "tasks.max": "1",
              "database.hostname": "{{ .Values.connector.config.database.hostname }}",
              "database.port": "{{ .Values.connector.config.database.port }}",
              "database.user": "{{ .Values.connector.config.database.user }}",
              "database.password": "{{ .Values.connector.config.database.password }}",
              "database.dbname": "{{ .Values.connector.config.database.dbname }}",
              "database.server.name": "{{ .Values.connector.config.topic.prefix }}",
              "table.include.list": "public.*",
              "plugin.name": "pgoutput",
              "slot.name": "debezium",
              "publication.name": "dbz_publication",
              "topic.prefix": "{{ .Values.connector.config.topic.prefix }}",
              "topic.creation.enable": "true",
              "topic.creation.default.replication.factor": {{ .Values.connector.config.topic.replicationFactor }},
              "topic.creation.default.partitions": {{ .Values.connector.config.topic.partitions }}
            }
          }' http://{{ .Values.connect.name }}:8083/connectors
      restartPolicy: OnFailure
