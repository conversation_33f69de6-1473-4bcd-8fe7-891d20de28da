apiVersion: platform.confluent.io/v1beta1
kind: Connect
metadata:
  name: {{ .Values.connect.name }}
  namespace: {{ .Values.global.namespace }}
spec:
  replicas: {{ .Values.connect.replicas }}
  image:
    application: {{ .Values.global.registry.account }}.dkr.ecr.{{ .Values.global.registry.region }}.amazonaws.com/{{ .Values.connect.image.repository }}:{{ .Values.connect.image.tag }}
    init: confluentinc/confluent-init-container:2.11.0
  dependencies:
    kafka:
      bootstrapEndpoint: kafka:9092
  resources:
    {{- toYaml .Values.connect.resources | nindent 4 }}
  configOverrides:
    server:
      - "key.converter=org.apache.kafka.connect.storage.StringConverter"
      - "value.converter=org.apache.kafka.connect.storage.StringConverter"
      - "group.id={{ .Values.connect.name }}"
      - "config.storage.topic=connect-configs"
      - "offset.storage.topic=connect-offsets"
      - "status.storage.topic=connect-status"
