---
apiVersion: platform.confluent.io/v1beta1
kind: Zookeeper
metadata:
  name: zookeeper
  namespace: {{ .Values.global.namespace }}
spec:
  replicas: {{ .Values.platform.zookeeper.replicas }}
  image:
    application: confluentinc/cp-zookeeper:7.9.0
    init: confluentinc/confluent-init-container:2.11.0
  dataVolumeCapacity: {{ .Values.platform.zookeeper.storage.size }}
  logVolumeCapacity: {{ .Values.platform.zookeeper.storage.size }}
---
apiVersion: platform.confluent.io/v1beta1
kind: Kafka
metadata:
  name: kafka
  namespace: {{ .Values.global.namespace }}
spec:
  replicas: {{ .Values.platform.kafka.replicas }}
  image:
    application: confluentinc/cp-server:7.9.0
    init: confluentinc/confluent-init-container:2.11.0
  dataVolumeCapacity: {{ .Values.platform.kafka.storage.size }}
  configOverrides:
    server:
      - "auto.create.topics.enable=true"
      - "delete.topic.enable=true"
  resources:
    {{- toYaml .Values.platform.kafka.resources | nindent 4 }}
