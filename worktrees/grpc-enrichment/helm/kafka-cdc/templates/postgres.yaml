{{- if .Values.postgres.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: {{ .Values.global.namespace }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: {{ .Values.postgres.image.repository }}:{{ .Values.postgres.image.tag }}
        env:
        - name: POSTGRES_USER
          value: {{ .Values.postgres.credentials.username }}
        - name: POSTGRES_PASSWORD
          value: {{ .Values.postgres.credentials.password }}
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        ports:
        - containerPort: 5432
        volumeMounts:
        - name: postgres-data
          mountPath: /var/lib/postgresql/data
        resources:
          {{- toYaml .Values.postgres.resources | nindent 10 }}
      volumes:
      - name: postgres-data
        persistentVolumeClaim:
          claimName: postgres-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: postgres
  namespace: {{ .Values.global.namespace }}
spec:
  selector:
    app: postgres
  ports:
  - port: 5432
    targetPort: 5432
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-pvc
  namespace: {{ .Values.global.namespace }}
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: {{ .Values.postgres.storage.size }}
{{- end }}
