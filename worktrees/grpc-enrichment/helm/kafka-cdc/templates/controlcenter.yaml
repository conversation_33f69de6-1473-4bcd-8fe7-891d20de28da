{{- if .Values.controlcenter.enabled }}
apiVersion: platform.confluent.io/v1beta1
kind: ControlCenter
metadata:
  name: controlcenter
  namespace: {{ .Values.global.namespace }}
spec:
  replicas: {{ .Values.controlcenter.replicas }}
  image:
    application: confluentinc/cp-enterprise-control-center:7.9.0
    init: confluentinc/confluent-init-container:2.11.0
  dataVolumeCapacity: {{ .Values.controlcenter.storage.size }}
  dependencies:
    kafka:
      bootstrapEndpoint: kafka:9092
  configOverrides:
    server:
      - "confluent.controlcenter.connect.cluster=http://{{ .Values.connect.name }}:8083"
      - "confluent.controlcenter.internal.topics.replication=1"
      - "confluent.controlcenter.command.topic.replication=1"
      - "confluent.metrics.topic.replication=1"
      - "confluent.monitoring.interceptor.topic.replication=1"
  resources:
    {{- toYaml .Values.controlcenter.resources | nindent 4 }}
{{- end }}
