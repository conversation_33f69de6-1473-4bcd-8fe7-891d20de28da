# Development environment configuration
platform:
  zookeeper:
    replicas: 1
    storage:
      size: 10Gi
  kafka:
    replicas: 1
    storage:
      size: 10Gi
    resources:
      requests:
        memory: "2Gi"
        cpu: "1000m"
      limits:
        memory: "4Gi"
        cpu: "2000m"

connect:
  replicas: 1
  resources:
    requests:
      memory: "1Gi"
      cpu: "500m"
    limits:
      memory: "2Gi"
      cpu: "1000m"

controlcenter:
  enabled: true
  replicas: 1
  storage:
    size: 10Gi

connector:
  config:
    topic:
      replicationFactor: 1
      partitions: 1
