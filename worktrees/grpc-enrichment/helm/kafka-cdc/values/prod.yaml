# Production environment configuration
platform:
  zookeeper:
    replicas: 3
    storage:
      size: 100Gi
  kafka:
    replicas: 3
    storage:
      size: 500Gi
    resources:
      requests:
        memory: "8Gi"
        cpu: "2000m"
      limits:
        memory: "16Gi"
        cpu: "4000m"

connect:
  replicas: 2
  resources:
    requests:
      memory: "4Gi"
      cpu: "2000m"
    limits:
      memory: "8Gi"
      cpu: "4000m"

controlcenter:
  enabled: true
  replicas: 1
  storage:
    size: 100Gi
  resources:
    requests:
      memory: "4Gi"
      cpu: "2000m"
    limits:
      memory: "8Gi"
      cpu: "4000m"

connector:
  config:
    topic:
      replicationFactor: 3
      partitions: 6

postgres:
  storage:
    size: 200Gi
  resources:
    requests:
      memory: "4Gi"
      cpu: "2000m"
    limits:
      memory: "8Gi"
      cpu: "4000m"
