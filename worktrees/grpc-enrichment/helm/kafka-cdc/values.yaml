# Global settings
global:
  namespace: confluent
  provider: aws
  region: us-west-2
  registry:
    account: "************"
    region: us-west-2

# Confluent Platform settings
platform:
  zookeeper:
    replicas: 1
    storage:
      size: 10Gi
  kafka:
    replicas: 1
    storage:
      size: 10Gi
    resources:
      requests:
        memory: "2Gi"
        cpu: "1000m"
      limits:
        memory: "4Gi"
        cpu: "2000m"

# Kafka Connect settings
connect:
  name: connect-pg-debezium
  replicas: 1
  image:
    repository: kafka-connect-debezium
    tag: latest
  resources:
    requests:
      memory: "1Gi"
      cpu: "500m"
    limits:
      memory: "2Gi"
      cpu: "1000m"

# Control Center settings
controlcenter:
  enabled: true
  replicas: 1
  storage:
    size: 10Gi
  resources:
    requests:
      memory: "2Gi"
      cpu: "1000m"
    limits:
      memory: "4Gi"
      cpu: "2000m"

# PostgreSQL settings
postgres:
  enabled: true
  image:
    repository: postgres
    tag: 13
  storage:
    size: 10Gi
  resources:
    requests:
      memory: "1Gi"
      cpu: "500m"
    limits:
      memory: "2Gi"
      cpu: "1000m"
  credentials:
    username: postgres
    password: postgres

# Debezium connector configuration
connector:
  name: postgres-source
  config:
    database:
      hostname: postgres.confluent.svc.cluster.local
      port: 5432
      user: postgres
      password: postgres
      dbname: postgres
    topic:
      prefix: postgres
      replicationFactor: 1
      partitions: 1
