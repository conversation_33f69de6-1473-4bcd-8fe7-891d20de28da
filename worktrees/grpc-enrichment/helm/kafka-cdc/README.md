# Kafka CDC Helm Chart

This Helm chart deploys a complete Kafka CDC setup with PostgreSQL and Debezium for change data capture.

## Components

- Confluent Platform Operator (CFK)
- Apache Kafka
- Apache Zookeeper
- Kafka Connect with Debezium
- Confluent Control Center
- PostgreSQL Database

## Prerequisites

- Kubernetes 1.27+
- Helm 3.0+
- AWS ECR access for custom Connect image
- kubectl configured with cluster access

## Installation

1. Build the Kafka Connect image:
```bash
./scripts/build-images.sh
```

2. Install the chart:
```bash
# For development
./scripts/install.sh dev

# For production
./scripts/install.sh prod
```

## Configuration

### Global Settings

| Parameter | Description | Default |
|-----------|-------------|---------|
| global.namespace | Kubernetes namespace | confluent |
| global.provider | Cloud provider | aws |
| global.region | AWS region | us-west-2 |

### Platform Settings

| Parameter | Description | Default |
|-----------|-------------|---------|
| platform.zookeeper.replicas | Number of Zookeeper nodes | 1 (dev), 3 (prod) |
| platform.kafka.replicas | Number of Kafka brokers | 1 (dev), 3 (prod) |
| platform.*.storage.size | Storage size | 10Gi (dev), 100Gi+ (prod) |

### Connect Settings

| Parameter | Description | Default |
|-----------|-------------|---------|
| connect.replicas | Number of Connect workers | 1 (dev), 2 (prod) |
| connect.image.repository | Connect image repository | kafka-connect-debezium |
| connect.image.tag | Connect image tag | latest |

### Control Center Settings

| Parameter | Description | Default |
|-----------|-------------|---------|
| controlcenter.enabled | Enable Control Center | true |
| controlcenter.storage.size | Storage size | 10Gi (dev), 100Gi (prod) |

### PostgreSQL Settings

| Parameter | Description | Default |
|-----------|-------------|---------|
| postgres.enabled | Enable PostgreSQL | true |
| postgres.storage.size | Storage size | 10Gi (dev), 200Gi (prod) |

### Connector Settings

| Parameter | Description | Default |
|-----------|-------------|---------|
| connector.name | Connector name | postgres-source |
| connector.config.topic.prefix | Topic prefix | postgres |
| connector.config.topic.replicationFactor | Topic replication factor | 1 (dev), 3 (prod) |

## Environment-Specific Configurations

### Development (dev.yaml)
- Single-node deployments
- Minimal resource requirements
- Suitable for development and testing

### Production (prod.yaml)
- Multi-node deployments
- High availability configuration
- Production-grade resource allocation
- Increased replication factors

## Accessing Services

1. Control Center:
```bash
kubectl port-forward controlcenter-0 9021:9021 -n confluent
```
Access at: http://localhost:9021

2. PostgreSQL:
```bash
kubectl port-forward postgres-0 5432:5432 -n confluent
```

## Monitoring

1. Check component status:
```bash
kubectl get pods -n confluent
```

2. View Connect logs:
```bash
kubectl logs -f connect-pg-debezium-0 -n confluent
```

3. Check connector status:
```bash
kubectl exec -it connect-pg-debezium-0 -n confluent -- \
  curl -s http://localhost:8083/connectors/postgres-source/status
```

## Troubleshooting

1. Connector Issues:
```bash
# Check connector status
kubectl exec -it connect-pg-debezium-0 -n confluent -- \
  curl -s http://localhost:8083/connectors/postgres-source/status

# Restart connector
kubectl exec -it connect-pg-debezium-0 -n confluent -- \
  curl -X POST http://localhost:8083/connectors/postgres-source/restart
```

2. PostgreSQL Issues:
```bash
# Check PostgreSQL logs
kubectl logs -f postgres-0 -n confluent

# Connect to PostgreSQL
kubectl exec -it postgres-0 -n confluent -- psql -U postgres
```

3. Kafka Issues:
```bash
# Check Kafka broker logs
kubectl logs -f kafka-0 -n confluent

# List topics
kubectl exec -it kafka-0 -n confluent -- \
  kafka-topics --list --bootstrap-server localhost:9092
```
