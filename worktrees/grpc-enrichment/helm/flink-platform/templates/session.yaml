apiVersion: flink.apache.org/v1beta1
kind: FlinkDeployment
metadata:
  name: {{ .Values.defaultSession.name }}
  namespace: {{ .Values.global.namespace }}
spec:
  image: {{ .Values.defaultSession.image }}
  flinkVersion: v1_17
  serviceAccount: {{ .Values.defaultSession.serviceAccount }}
  jobManager:
    replicas: {{ .Values.defaultSession.jobManager.replicas }}
    resource:
      memory: {{ .Values.defaultSession.jobManager.resources.requests.memory | quote }}
      cpu: {{ .Values.defaultSession.jobManager.resources.requests.cpu }}
    podTemplate:
      spec:
        containers:
          - name: jobmanager
            resources:
              requests:
                memory: {{ .Values.defaultSession.jobManager.resources.requests.memory | quote }}
                cpu: {{ .Values.defaultSession.jobManager.resources.requests.cpu }}
              limits:
                memory: {{ .Values.defaultSession.jobManager.resources.limits.memory | quote }}
                cpu: {{ .Values.defaultSession.jobManager.resources.limits.cpu }}
  taskManager:
    replicas: {{ .Values.defaultSession.taskManager.replicas }}
    resource:
      memory: {{ .Values.defaultSession.taskManager.resources.requests.memory | quote }}
      cpu: {{ .Values.defaultSession.taskManager.resources.requests.cpu }}
    podTemplate:
      spec:
        containers:
          - name: taskmanager
            resources:
              requests:
                memory: {{ .Values.defaultSession.taskManager.resources.requests.memory | quote }}
                cpu: {{ .Values.defaultSession.taskManager.resources.requests.cpu }}
              limits:
                memory: {{ .Values.defaultSession.taskManager.resources.limits.memory | quote }}
                cpu: {{ .Values.defaultSession.taskManager.resources.limits.cpu }}
  flinkConfiguration:
    taskmanager.numberOfTaskSlots: "2"
    state.backend: rocksdb
    state.checkpoints.dir: file:///flink-data/checkpoints
    state.savepoints.dir: file:///flink-data/savepoints
    execution.checkpointing.interval: 10s
    execution.checkpointing.mode: EXACTLY_ONCE
    execution.checkpointing.timeout: 5min
    restart-strategy: fixed-delay
    restart-strategy.fixed-delay.attempts: "3"
    restart-strategy.fixed-delay.delay: 10s
