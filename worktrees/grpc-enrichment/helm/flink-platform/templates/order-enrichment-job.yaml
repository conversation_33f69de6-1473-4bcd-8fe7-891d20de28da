apiVersion: flink.apache.org/v1beta1
kind: FlinkDeployment
metadata:
  name: order-enrichment
  namespace: {{ .Values.global.namespace }}
spec:
  image: {{ .Values.global.registry.account }}.dkr.ecr.{{ .Values.global.registry.region }}.amazonaws.com/flink-jobs:latest
  flinkVersion: v1_17
  serviceAccount: flink
  jobManager:
    resource:
      memory: "2048m"
      cpu: 1
  taskManager:
    resource:
      memory: "2048m"
      cpu: 1
  flinkConfiguration:
    taskmanager.numberOfTaskSlots: "2"
    state.backend: rocksdb
    state.checkpoints.dir: file:///flink-data/checkpoints
    state.savepoints.dir: file:///flink-data/savepoints
    execution.checkpointing.interval: 10s
    execution.checkpointing.mode: EXACTLY_ONCE
  job:
    jarURI: local:///opt/flink/usrlib/flink-jobs.jar
    parallelism: 2
    upgradeMode: savepoint
    state: running
