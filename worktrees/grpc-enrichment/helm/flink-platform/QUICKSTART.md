# Flink Kubernetes Operator Quickstart

This guide will help you quickly install the Flink Kubernetes Operator into your EKS (or any Kubernetes) cluster using the official Helm chart.

---

## Prerequisites
- Kubernetes cluster (EKS 1.27 tested)
- Helm installed
- `kubectl` access to your cluster

---

## 1. Download the Official Helm Chart

```
curl -LO https://downloads.apache.org/flink/flink-kubernetes-operator-1.11.0/flink-kubernetes-operator-1.11.0-helm.tgz
```

---

## 2. Install the Operator

```
helm install flink-kubernetes-operator ./flink-kubernetes-operator-1.11.0-helm.tgz \
  --namespace application \
  --create-namespace
```

---

## 3. Verify Installation

```
kubectl get pods -n application
helm list -n application
```

You should see the operator pod running and the Helm release deployed.

---

## 4. Uninstall (Cleanup)

```
helm uninstall flink-kubernetes-operator -n application
kubectl delete ns application
```

---

## Notes
- The chart version and namespace can be changed as needed.
- For production, review and customize values (RBAC, service accounts, storage, etc).
- For more, see the [official docs](https://nightlies.apache.org/flink/flink-kubernetes-operator-docs-main/docs/try-flink-kubernetes-operator/quick-start/).
