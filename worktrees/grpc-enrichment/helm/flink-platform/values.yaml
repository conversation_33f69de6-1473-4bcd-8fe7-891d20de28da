# Global settings
global:
  namespace: flink
  provider: aws
  region: us-west-2

# Flink Operator settings
flink-kubernetes-operator:
  watchNamespaces: ["flink"]
  webhook:
    create: true
  rbac:
    create: true
  serviceAccount:
    create: true
  image:
    repository: apache/flink-kubernetes-operator
    tag: 1.7.0

# Default Flink session settings
defaultSession:
  name: flink-session
  image: apache/flink:1.17.1
  serviceAccount: flink
  jobManager:
    replicas: 1
    resources:
      requests:
        memory: "2Gi"
        cpu: "1000m"
      limits:
        memory: "2Gi"
        cpu: "2000m"
  taskManager:
    replicas: 2
    resources:
      requests:
        memory: "2Gi"
        cpu: "1000m"
      limits:
        memory: "4Gi"
        cpu: "2000m"
