# Small EKS Cluster Setup

This directory contains Kubernetes manifests for setting up a small EKS (Elastic Kubernetes Service) cluster on AWS.

## Prerequisites

- AWS CLI configured with appropriate permissions
- `eksctl` installed (https://eksctl.io/installation/)
- `kubectl` installed (https://kubernetes.io/docs/tasks/tools/install-kubectl/)

## Cluster Configuration

The main configuration is in `eks-cluster.yaml`, which defines:
- A cluster named "small-eks-cluster" in the us-west-2 region
- Kubernetes version 1.27
- VPC with public and private endpoints
- A single node group with t3.medium instances
- Essential add-ons (vpc-cni, coredns, kube-proxy, aws-ebs-csi-driver)

## Deployment Instructions

1. Create the EKS cluster:
   ```
   eksctl create cluster -f eks-cluster.yaml
   ```

2. After the cluster is created, apply the Kubernetes manifests:
   ```
   kubectl apply -f namespace.yaml
   kubectl apply -f resource-quota.yaml
   kubectl apply -f network-policy.yaml
   ```

3. Verify the cluster setup:
   ```
   kubectl get nodes
   kubectl get ns
   kubectl get resourcequota -n application
   kubectl get networkpolicy -n application
   ```

## Cluster Management

- To scale the node group:
  ```
  eksctl scale nodegroup --cluster=small-eks-cluster --nodes=3 --name=ng-1
  ```

- To update the cluster:
  ```
  eksctl update cluster -f eks-cluster.yaml
  ```

- To delete the cluster when no longer needed:
  ```
  eksctl delete cluster -f eks-cluster.yaml
  ```

## Notes

- This is a minimal setup for a small EKS cluster
- The node group uses t3.medium instances which are suitable for development/testing
- Resource quotas are set to limit resource consumption in the application namespace
- Network policies are configured to restrict traffic within the application namespace
