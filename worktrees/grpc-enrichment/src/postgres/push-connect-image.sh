#!/bin/bash
set -e

# Source environment variables
if [ -f "env.sh" ]; then
    source env.sh
else
    echo "Error: env.sh file not found"
    echo "Please create env.sh with the following content:"
    echo 'export AWS_ACCOUNT="YOUR_AWS_ACCOUNT_ID"'
    echo 'export AWS_REGION="YOUR_AWS_REGION"'
    echo 'export ECR_REPO="kafka-connect-debezium"'
    exit 1
fi

# Verify environment variables
if [ -z "$AWS_ACCOUNT" ] || [ -z "$AWS_REGION" ] || [ -z "$ECR_REPO" ]; then
    echo "Error: Required environment variables not set"
    echo "Please ensure AWS_ACCOUNT, AWS_REGION, and ECR_REPO are set in env.sh"
    exit 1
fi

# Create ECR repository if it doesn't exist
echo "Creating ECR repository $ECR_REPO if it doesn't exist..."
aws ecr create-repository --repository-name $ECR_REPO --output json >/dev/null 2>&1 || true

# Build the image for linux/amd64 platform
echo "Building Kafka Connect image with De<PERSON>zi<PERSON>..."
docker build --platform linux/amd64 -t kafka-connect-debezium:test -f Dockerfile.connect .

# Tag the image for ECR
echo "Tagging image for ECR..."
docker tag kafka-connect-debezium:test $AWS_ACCOUNT.dkr.ecr.$AWS_REGION.amazonaws.com/$ECR_REPO:latest

# Push to ECR
echo "Pushing to ECR..."
aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT.dkr.ecr.$AWS_REGION.amazonaws.com
docker push $AWS_ACCOUNT.dkr.ecr.$AWS_REGION.amazonaws.com/$ECR_REPO:latest

echo "Done!"
