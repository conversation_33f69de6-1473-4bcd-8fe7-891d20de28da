apiVersion: platform.confluent.io/v1beta1
kind: ControlCenter
metadata:
  name: controlcenter
  namespace: confluent
spec:
  replicas: 1
  image:
    application: confluentinc/cp-enterprise-control-center:7.9.0
    init: confluentinc/confluent-init-container:2.11.0
  dataVolumeCapacity: "10Gi"
  dependencies:
    kafka:
      bootstrapEndpoint: kafka.confluent.svc.cluster.local:9092
  configOverrides:
    server:
      - confluent.controlcenter.connect.cluster=http://connect-pg-debezium.confluent.svc.cluster.local:8083
      - confluent.controlcenter.schema.registry.enable=false
      - confluent.controlcenter.internal.topics.replication=1
      - confluent.controlcenter.command.topic.replication=1
      - confluent.metrics.topic.replication=1
      - confluent.monitoring.interceptor.topic.replication=1
      - confluent.controlcenter.internal.topics.partitions=1
      - confluent.controlcenter.command.topic.partitions=1
      - confluent.metrics.topic.partitions=1
      - confluent.monitoring.interceptor.topic.partitions=1
  podTemplate:
    resources:
      requests:
        memory: "2Gi"
        cpu: "1"
      limits:
        memory: "4Gi"
        cpu: "2"
    probe:
      liveness:
        periodSeconds: 10
        failureThreshold: 5
        timeoutSeconds: 500
      readiness:
        periodSeconds: 10
        failureThreshold: 5
        timeoutSeconds: 500
