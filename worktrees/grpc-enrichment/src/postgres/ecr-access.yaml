apiVersion: v1
kind: ServiceAccount
metadata:
  name: ecr-access
  namespace: application
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: ecr-access
  namespace: application
rules:
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: ecr-access
  namespace: application
subjects:
- kind: ServiceAccount
  name: ecr-access
  namespace: application
roleRef:
  kind: Role
  name: ecr-access
  apiGroup: rbac.authorization.k8s.io
