# Kafka Connect worker configuration
plugin.path=/usr/share/java,/usr/share/confluent-hub-components

# Better defaults for production
offset.flush.interval.ms=10000
offset.flush.timeout.ms=5000

# Enable topic creation by default
topic.creation.enable=true
topic.creation.default.replication.factor=1
topic.creation.default.partitions=1

# Producer configuration
producer.compression.type=lz4
producer.max.request.size=10485760

# Consumer configuration
consumer.max.poll.records=500
consumer.max.poll.interval.ms=300000

# Error handling and logging
errors.tolerance=none
errors.deadletterqueue.topic.name=connect-dlq
errors.deadletterqueue.topic.replication.factor=1
errors.deadletterqueue.context.headers.enable=true

# Enhanced logging
connect.log4j.appender.stdout.layout.ConversionPattern=[%d] %p %m (%c:%L)%n
