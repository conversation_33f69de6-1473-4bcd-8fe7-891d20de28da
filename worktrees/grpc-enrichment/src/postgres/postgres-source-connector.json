{"name": "postgres-source", "config": {"connector.class": "io.debezium.connector.postgresql.PostgresConnector", "tasks.max": "1", "database.hostname": "postgres.confluent.svc.cluster.local", "database.port": "5432", "database.user": "postgres", "database.password": "postgres", "database.dbname": "postgres", "database.server.name": "postgres", "table.include.list": "public.*", "plugin.name": "pgoutput", "slot.name": "debezium", "publication.name": "dbz_publication", "topic.prefix": "postgres", "topic.creation.enable": "true", "topic.creation.default.replication.factor": "1", "topic.creation.default.partitions": "1", "topic.creation.default.cleanup.policy": "delete", "topic.creation.default.retention.ms": "604800000", "transforms": "unwrap", "transforms.unwrap.type": "io.debezium.transforms.ExtractNewRecordState", "transforms.unwrap.drop.tombstones": "false", "transforms.unwrap.delete.handling.mode": "rewrite", "schema.include.list": "public"}}