FROM confluentinc/cp-kafka-connect:7.9.0

# Install commonly used connectors
RUN confluent-hub install --no-prompt debezium/debezium-connector-postgresql:1.9.6 && \
    confluent-hub install --no-prompt confluentinc/kafka-connect-jdbc:10.7.4 && \
    confluent-hub install --no-prompt confluentinc/kafka-connect-s3:10.5.8 && \
    confluent-hub install --no-prompt confluentinc/kafka-connect-elasticsearch:14.0.8

# Create directories for custom configuration and scripts
RUN mkdir -p /etc/kafka-connect/jars /etc/kafka-connect/scripts

# Copy custom configuration
COPY connect-distributed.properties /etc/kafka/connect-distributed.properties

# Add health check script
COPY --chmod=755 <<-"EOF" /etc/kafka-connect/scripts/healthcheck.sh
#!/bin/bash
curl -s http://localhost:8083/ > /dev/null || exit 1
RESULT=$(curl -s -H "Accept: application/json" localhost:8083/connectors?expand=status)
if [ $(echo $RESULT | grep -c "FAILED") -gt 0 ]; then
    exit 1
fi
EOF

# Set up health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD /etc/kafka-connect/scripts/healthcheck.sh

# Verify installations
RUN echo "Verifying connector installations..." && \
    ls -la /usr/share/confluent-hub-components/*/lib/*.jar && \
    echo "Connector plugins installed successfully."

# Set default environment variables
ENV CONNECT_REST_PORT=8083 \
    CONNECT_REST_ADVERTISED_HOST_NAME=connect \
    CONNECT_GROUP_ID=connect-cluster \
    CONNECT_CONFIG_STORAGE_TOPIC=connect-configs \
    CONNECT_OFFSET_STORAGE_TOPIC=connect-offsets \
    CONNECT_STATUS_STORAGE_TOPIC=connect-status
