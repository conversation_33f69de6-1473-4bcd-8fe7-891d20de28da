# PostgreSQL CDC with Kafka Connect

This directory contains the setup for PostgreSQL Change Data Capture (CDC) using Debezium and Kafka Connect.

## Components

### 1. Custom Kafka Connect Image
- Base: `confluentinc/cp-kafka-connect:7.9.0`
- Includes Debezium PostgreSQL connector
- Configured for automatic topic creation
- Location: `Dockerfile.connect`

### 2. Kafka Connect Deployment
- Runs in Kubernetes using StatefulSet
- Configured for high availability
- Location: `connect-combined.yaml`

### 3. Debezium Source Connector
- Captures changes from PostgreSQL
- Configured for all public schema tables
- Uses pgoutput plugin for replication
- Location: `postgres-source-connector.json`

### 4. Confluent Control Center
- Web UI for monitoring and management
- Real-time data flow visualization
- Location: `controlcenter.yaml`

## Quick Start

1. Build and push the Kafka Connect image:
```bash
./push-connect-image.sh
```

2. Deploy Kafka Connect:
```bash
kubectl apply -f connect-combined.yaml
```

3. Deploy Confluent Control Center:
```bash
kubectl apply -f controlcenter.yaml
kubectl port-forward controlcenter-0 9021:9021 -n confluent
```

4. Access the Control Center UI:
- URL: http://localhost:9021
- Navigate to: Topics → postgres.public.*

## Connector Configuration

The Debezium connector is configured with:
```json
{
  "name": "postgres-source",
  "config": {
    "connector.class": "io.debezium.connector.postgresql.PostgresConnector",
    "database.hostname": "postgres.confluent.svc.cluster.local",
    "database.port": "5432",
    "database.user": "postgres",
    "database.password": "postgres",
    "database.dbname": "postgres",
    "table.include.list": "public.*",
    "topic.creation.enable": "true"
  }
}
```

## Testing the Setup

1. Create a test table:
```sql
CREATE TABLE orders (
  id SERIAL PRIMARY KEY,
  order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  customer_name TEXT,
  amount DECIMAL
);
```

2. Insert test data:
```sql
INSERT INTO orders (customer_name, amount) 
VALUES ('John Doe', 100.50), ('Jane Smith', 200.75);
```

3. View changes in Kafka:
- Check Control Center UI under Topics
- Or use CLI:
```bash
kubectl exec -it kafka-0 -n confluent -- kafka-console-consumer \
  --bootstrap-server localhost:9092 \
  --topic postgres.public.orders \
  --from-beginning
```

## Monitoring

1. Connect Status:
```bash
kubectl exec -it connect-pg-debezium-0 -n confluent -- \
  curl -s http://localhost:8083/connectors/postgres-source/status
```

2. Topic List:
```bash
kubectl exec -it kafka-0 -n confluent -- \
  kafka-topics --list --bootstrap-server localhost:9092
```

## Troubleshooting

1. Check Connect logs:
```bash
kubectl logs -f connect-pg-debezium-0 -n confluent
```

2. Check Control Center logs:
```bash
kubectl logs -f controlcenter-0 -n confluent
```

3. Common issues:
- Topic creation failures: Check Kafka broker configurations
- Connection issues: Verify PostgreSQL credentials and network access
- Replication slot conflicts: Check existing slots in PostgreSQL
