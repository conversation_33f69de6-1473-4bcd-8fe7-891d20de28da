apiVersion: platform.confluent.io/v1beta1
kind: Connect
metadata:
  name: connect-pg-debezium
  namespace: confluent
spec:
  replicas: 1
  image:
    application: ${AWS_ACCOUNT}.dkr.ecr.${AWS_REGION}.amazonaws.com/kafka-connect-debezium:latest
    init: confluentinc/confluent-init-container:2.11.0
  configOverrides:
    server:
      - bootstrap.servers=kafka:9092
      - key.converter=org.apache.kafka.connect.storage.StringConverter
      - value.converter=org.apache.kafka.connect.storage.StringConverter
      - internal.key.converter=org.apache.kafka.connect.json.JsonConverter
      - internal.value.converter=org.apache.kafka.connect.json.JsonConverter
      - internal.key.converter.schemas.enable=false
      - internal.value.converter.schemas.enable=false
      - offset.storage.topic=connect-offsets
      - config.storage.topic=connect-configs
      - status.storage.topic=connect-status
      - config.storage.replication.factor=1
      - offset.storage.replication.factor=1
      - status.storage.replication.factor=1
      - plugin.path=/usr/share/java,/usr/share/confluent-hub-components
      - rest.port=8083
      - rest.advertised.port=8083
      - rest.advertised.host.name=connect-pg-debezium
      - group.id=connect-pg-debezium
      - offset.flush.interval.ms=10000
  podTemplate:
    serviceAccountName: ecr-access
    podSecurityContext:
      fsGroup: 1000
      runAsUser: 1000
      runAsNonRoot: true
    resources:
      requests:
        memory: "1Gi"
        cpu: "500m"
      limits:
        memory: "2Gi"
        cpu: "1000m"

