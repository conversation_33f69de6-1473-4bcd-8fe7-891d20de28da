-- Connect to the inventory database
\c inventory

-- Enable the pgcrypto extension for UUID generation
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- Create and populate products table
CREATE TABLE products (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  weight DECIMAL(10, 2)
);

INSERT INTO products (name, description, weight)
VALUES 
  ('scooter', 'Small 2-wheel scooter', 3.14),
  ('car battery', '12V car battery', 8.1),
  ('12-pack drill bits', '12-pack of drill bits with sizes from #40 to #3', 0.8),
  ('hammer', '12oz carpenter''s hammer', 0.75),
  ('hammer', '14oz carpenter''s hammer', 0.875),
  ('hammer', '16oz carpenter''s hammer', 1.0),
  ('rocks', 'box of assorted rocks', 5.3),
  ('jacket', 'water resistent black wind breaker', 0.1),
  ('spare tire', '24 inch spare tire', 22.2);

-- Create and populate customers table
CREATE TABLE customers (
  id SERIAL PRIMARY KEY,
  first_name VARCHA<PERSON>(255) NOT NULL,
  last_name VARCHAR(255) NOT NULL,
  email VARCHAR(255) UNIQUE NOT NULL
);

INSERT INTO customers (first_name, last_name, email)
VALUES 
  ('Sally', 'Thomas', '<EMAIL>'),
  ('George', 'Bailey', '<EMAIL>'),
  ('<PERSON>', '<PERSON>', '<EMAIL>'),
  ('Anne', 'Kretchmar', '<EMAIL>');

-- Create and populate orders table
CREATE TABLE orders (
  id SERIAL PRIMARY KEY,
  order_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  purchaser INTEGER NOT NULL,
  quantity INTEGER NOT NULL,
  product_id INTEGER NOT NULL,
  FOREIGN KEY (purchaser) REFERENCES customers (id) ON DELETE CASCADE,
  FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE
);

INSERT INTO orders (order_date, purchaser, quantity, product_id)
VALUES 
  ('2023-01-16 10:31:09', 1, 1, 1),
  ('2023-01-17 14:22:10', 2, 2, 2),
  ('2023-01-18 09:15:22', 3, 3, 3),
  ('2023-01-19 16:45:33', 1, 4, 4),
  ('2023-01-20 11:30:44', 2, 5, 5),
  ('2023-01-21 08:20:55', 3, 6, 6),
  ('2023-01-22 17:10:06', 4, 7, 7),
  ('2023-01-23 13:40:17', 1, 8, 8),
  ('2023-01-24 10:50:28', 2, 9, 9);

-- Create a publication for Debezium
CREATE PUBLICATION dbz_publication FOR TABLE products, customers, orders;
