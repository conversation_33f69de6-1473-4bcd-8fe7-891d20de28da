apiVersion: batch/v1
kind: Job
metadata:
  name: deploy-debezium-connector
  namespace: confluent
spec:
  template:
    spec:
      containers:
      - name: deploy-connector
        image: curlimages/curl
        command:
        - /bin/sh
        - -c
        - |
          # Install Debezium connector
          echo "Installing Debezium connector..."
          curl -X POST -H "Content-Type: application/json" --data '{
            "name": "postgres-source",
            "config": {
              "connector.class": "io.debezium.connector.postgresql.PostgresConnector",
              "tasks.max": "1",
              "database.hostname": "postgres.confluent.svc.cluster.local",
              "database.port": "5432",
              "database.user": "postgres",
              "database.password": "postgres",
              "database.dbname": "postgres",
              "database.server.name": "postgres",
              "table.include.list": "public.customers,public.orders",
              "plugin.name": "pgoutput",
              "slot.name": "debezium",
              "publication.name": "dbz_publication",
              "topic.prefix": "postgres",
              "topic.creation.enable": "true",
              "topic.creation.default.replication.factor": "1",
              "topic.creation.default.partitions": "3",
              "transforms": "unwrap",
              "transforms.unwrap.type": "io.debezium.transforms.ExtractNewRecordState",
              "transforms.unwrap.drop.tombstones": "false",
              "transforms.unwrap.delete.handling.mode": "rewrite",
              "schema.include.list": "public"
            }
          }' http://debezium-connect:8083/connectors
      restartPolicy: OnFailure
