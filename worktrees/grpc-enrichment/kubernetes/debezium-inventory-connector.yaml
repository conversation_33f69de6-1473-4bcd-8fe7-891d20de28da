apiVersion: v1
kind: ConfigMap
metadata:
  name: debezium-inventory-connector
  namespace: confluent
data:
  inventory-connector.json: |
    {
      "name": "inventory-connector",
      "config": {
        "connector.class": "io.debezium.connector.postgresql.PostgresConnector",
        "tasks.max": "1",
        "database.hostname": "postgres.confluent.svc.cluster.local",
        "database.port": "5432",
        "database.user": "postgres",
        "database.password": "postgres",
        "database.dbname": "inventory",
        "database.server.name": "dbserver1",
        "schema.include.list": "public",
        "table.include.list": "public.products,public.customers,public.orders",
        "plugin.name": "pgoutput",
        "slot.name": "debezium_inventory",
        "publication.name": "dbz_publication",
        "topic.prefix": "inventory",
        "topic.creation.enable": "true",
        "topic.creation.default.replication.factor": "1",
        "topic.creation.default.partitions": "3",
        "transforms": "unwrap",
        "transforms.unwrap.type": "io.debezium.transforms.ExtractNewRecordState",
        "transforms.unwrap.drop.tombstones": "false",
        "transforms.unwrap.delete.handling.mode": "rewrite"
      }
    }
