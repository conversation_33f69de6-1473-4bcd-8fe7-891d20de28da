-- Create customers table
CREATE TABLE IF NOT EXISTS customers (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    tier VARCHAR(20) NOT NULL,
    region VARCHAR(50) NOT NULL,
    active BOOLEAN DEFAULT TRUE
);

-- Create orders table
CREATE TABLE IF NOT EXISTS orders (
    id SERIAL PRIMARY KEY,
    customer_name VARCHAR(100) NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(20) DEFAULT 'PENDING',
    deleted BOOLEAN DEFAULT FALSE
);

-- Insert sample customer data
INSERT INTO customers (name, email, tier, region, active) VALUES
('<PERSON>', '<EMAIL>', 'GOLD', 'WEST', TRUE),
('<PERSON>', '<EMAIL>', 'SILVER', 'EAST', TRUE),
('<PERSON>', '<EMAIL>', '<PERSON>ONZ<PERSON>', 'NORTH', TRUE),
('<PERSON>', '<EMAIL>', 'GOLD', 'SOUTH', TRUE),
('Charlie Davis', '<EMAIL>', 'SILVER', 'WEST', FALSE);

-- Insert sample order data
INSERT INTO orders (customer_name, amount, status) VALUES
('John Doe', 199.99, 'COMPLETED'),
('Jane Smith', 99.50, 'PENDING'),
('Bob Johnson', 149.75, 'PROCESSING'),
('Alice Brown', 299.99, 'COMPLETED'),
('Charlie Davis', 49.99, 'CANCELLED');
