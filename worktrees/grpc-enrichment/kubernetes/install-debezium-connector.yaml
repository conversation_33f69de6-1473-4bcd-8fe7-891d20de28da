apiVersion: batch/v1
kind: Job
metadata:
  name: install-debezium-connector
  namespace: confluent
spec:
  template:
    spec:
      containers:
      - name: install-debezium
        image: curlimages/curl:latest
        command:
        - /bin/sh
        - -c
        - |
          # Create a temporary directory
          mkdir -p /tmp/debezium
          cd /tmp/debezium
          
          # Download Debezium PostgreSQL connector
          echo "Downloading Debezium PostgreSQL connector..."
          curl -L -o debezium-connector-postgres.tar.gz https://repo1.maven.org/maven2/io/debezium/debezium-connector-postgres/2.5.0.Final/debezium-connector-postgres-2.5.0.Final-plugin.tar.gz
          
          # Create directory for the connector
          mkdir -p debezium-connector-postgres
          
          # Extract the connector
          echo "Extracting connector..."
          tar -xzf debezium-connector-postgres.tar.gz -C debezium-connector-postgres
          
          # Install kubectl
          echo "Installing kubectl..."
          curl -LO "https://dl.k8s.io/release/v1.27.0/bin/linux/amd64/kubectl"
          chmod +x kubectl
          
          # Copy the connector to the Kafka Connect pod
          echo "Copying connector to Kafka Connect pod..."
          # First, create a ConfigMap with the connector files
          kubectl create configmap debezium-connector --from-file=debezium-connector-postgres -n confluent
          
          # Patch the Connect StatefulSet to mount the ConfigMap
          echo "Patching Connect StatefulSet..."
          kubectl patch statefulset connect -n confluent --type=json -p='[
            {
              "op": "add", 
              "path": "/spec/template/spec/volumes/-", 
              "value": {
                "name": "debezium-connector",
                "configMap": {
                  "name": "debezium-connector"
                }
              }
            },
            {
              "op": "add", 
              "path": "/spec/template/spec/containers/0/volumeMounts/-", 
              "value": {
                "name": "debezium-connector",
                "mountPath": "/usr/share/java/debezium-connector-postgres"
              }
            }
          ]'
          
          # Restart the Connect pod to load the new connector
          echo "Restarting Connect pod..."
          kubectl delete pod connect-0 -n confluent
          
          echo "Waiting for Connect pod to be ready..."
          kubectl wait --for=condition=ready pod/connect-0 -n confluent --timeout=300s
          
          echo "Debezium connector installation complete!"
      restartPolicy: OnFailure
