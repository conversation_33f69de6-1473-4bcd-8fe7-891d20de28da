apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: confluent
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:13
        env:
        - name: POSTGRES_USER
          value: postgres
        - name: POSTGRES_PASSWORD
          value: postgres
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        args:
        - -c
        - wal_level=logical
        - -c
        - max_wal_senders=10
        - -c
        - max_replication_slots=10
        ports:
        - containerPort: 5432
        volumeMounts:
        - name: postgres-data
          mountPath: /var/lib/postgresql/data
      volumes:
      - name: postgres-data
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: postgres
  namespace: confluent
spec:
  selector:
    app: postgres
  ports:
  - port: 5432
    targetPort: 5432
