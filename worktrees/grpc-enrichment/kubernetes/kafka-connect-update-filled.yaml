apiVersion: platform.confluent.io/v1beta1
kind: Connect
metadata:
  name: connect
  namespace: confluent
spec:
  replicas: 1
  image:
    application: 739368017290.dkr.ecr.us-west-2.amazonaws.com/kafka-connect-debezium:latest
    init: confluentinc/confluent-init-container:2.11.0
  dependencies:
    kafka:
      bootstrapEndpoint: kafka:9092
  configOverrides:
    server:
      - "key.converter=org.apache.kafka.connect.json.JsonConverter"
      - "value.converter=org.apache.kafka.connect.json.JsonConverter"
      - "key.converter.schemas.enable=false"
      - "value.converter.schemas.enable=false"
      - "group.id=connect-cluster"
      - "config.storage.topic=connect-configs"
      - "offset.storage.topic=connect-offsets"
      - "status.storage.topic=connect-status"
      - "config.storage.replication.factor=1"
      - "offset.storage.replication.factor=1"
      - "status.storage.replication.factor=1"
      - "plugin.path=/usr/share/java,/usr/share/confluent-hub-components"
  podTemplate:
    podSecurityContext:
      fsGroup: 1000
      runAsNonRoot: true
      runAsUser: 1000
    resources:
      requests:
        memory: "1Gi"
        cpu: "500m"
      limits:
        memory: "2Gi"
        cpu: "1000m"
