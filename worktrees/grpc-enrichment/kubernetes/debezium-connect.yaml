apiVersion: apps/v1
kind: Deployment
metadata:
  name: debezium-connect
  namespace: confluent
spec:
  replicas: 1
  selector:
    matchLabels:
      app: debezium-connect
  template:
    metadata:
      labels:
        app: debezium-connect
    spec:
      containers:
      - name: debezium-connect
        image: 739368017290.dkr.ecr.us-west-2.amazonaws.com/kafka-connect-debezium:latest
        ports:
        - containerPort: 8083
        env:
        - name: CONNECT_BOOTSTRAP_SERVERS
          value: kafka.confluent.svc.cluster.local:9092
        - name: CONNECT_GROUP_ID
          value: debezium-connect-cluster
        - name: CONNECT_CONFIG_STORAGE_TOPIC
          value: debezium-connect-configs
        - name: CONNECT_OFFSET_STORAGE_TOPIC
          value: debezium-connect-offsets
        - name: CONNECT_STATUS_STORAGE_TOPIC
          value: debezium-connect-status
        - name: CONNECT_KEY_CONVERTER
          value: org.apache.kafka.connect.json.JsonConverter
        - name: CONNECT_VALUE_CONVERTER
          value: org.apache.kafka.connect.json.JsonConverter
        - name: CONNECT_KEY_CONVERTER_SCHEMAS_ENABLE
          value: "false"
        - name: CONNECT_VALUE_CONVERTER_SCHEMAS_ENABLE
          value: "false"
        - name: CONNECT_REST_ADVERTISED_HOST_NAME
          value: debezium-connect
        - name: CONNECT_CONFIG_STORAGE_REPLICATION_FACTOR
          value: "1"
        - name: CONNECT_OFFSET_STORAGE_REPLICATION_FACTOR
          value: "1"
        - name: CONNECT_STATUS_STORAGE_REPLICATION_FACTOR
          value: "1"
---
apiVersion: v1
kind: Service
metadata:
  name: debezium-connect
  namespace: confluent
spec:
  selector:
    app: debezium-connect
  ports:
  - port: 8083
    targetPort: 8083
