apiVersion: batch/v1
kind: Job
metadata:
  name: deploy-debezium-connector
  namespace: confluent
spec:
  template:
    spec:
      containers:
      - name: deploy-connector
        image: curlimages/curl:latest
        command:
        - /bin/sh
        - -c
        - |
          # Wait for Kafka Connect to be ready
          echo "Waiting for Kafka Connect to be ready..."
          until curl -s http://connect:8083/ > /dev/null; do
            echo "Kafka Connect is not ready yet, waiting..."
            sleep 10
          done
          
          echo "Kafka Connect is ready, checking available plugins..."
          curl -s http://connect:8083/connector-plugins | grep -i debezium || echo "Debezium plugin not found"
          
          # Deploy the Debezium connector
          echo "Deploying Debezium connector..."
          curl -X POST -H "Content-Type: application/json" --data '{
            "name": "postgres-source",
            "config": {
              "connector.class": "io.debezium.connector.postgresql.PostgresConnector",
              "tasks.max": "1",
              "database.hostname": "postgres.confluent.svc.cluster.local",
              "database.port": "5432",
              "database.user": "postgres",
              "database.password": "postgres",
              "database.dbname": "postgres",
              "database.server.name": "postgres",
              "table.include.list": "public.customers,public.orders",
              "plugin.name": "pgoutput",
              "slot.name": "debezium",
              "publication.name": "dbz_publication",
              "topic.prefix": "postgres",
              "topic.creation.enable": "true",
              "topic.creation.default.replication.factor": "1",
              "topic.creation.default.partitions": "3"
            }
          }' http://connect:8083/connectors
          
          echo "Checking connector status..."
          sleep 5
          curl -s http://connect:8083/connectors/postgres-source/status
      restartPolicy: OnFailure
