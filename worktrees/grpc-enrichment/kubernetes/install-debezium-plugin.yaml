apiVersion: batch/v1
kind: Job
metadata:
  name: install-debezium-plugin
  namespace: confluent
spec:
  template:
    spec:
      containers:
      - name: install-debezium
        image: confluentinc/cp-server-connect:7.9.0
        command:
        - /bin/sh
        - -c
        - |
          # Install Debezium connector
          mkdir -p /tmp/debezium
          cd /tmp/debezium
          
          # Download Debezium PostgreSQL connector
          echo "Downloading Debezium PostgreSQL connector..."
          curl -O https://repo1.maven.org/maven2/io/debezium/debezium-connector-postgres/2.5.0.Final/debezium-connector-postgres-2.5.0.Final-plugin.tar.gz
          
          # Extract the connector
          echo "Extracting connector..."
          tar -xzf debezium-connector-postgres-2.5.0.Final-plugin.tar.gz
          
          # Copy the connector to the Kafka Connect plugins directory
          echo "Copying connector to Kafka Connect plugins directory..."
          mkdir -p /usr/share/java/debezium-connector-postgres
          cp -r debezium-connector-postgres/* /usr/share/java/debezium-connector-postgres/
          
          # List the installed connector
          echo "Installed connector files:"
          ls -la /usr/share/java/debezium-connector-postgres/
          
          # Copy the connector to the Kafka Connect pod
          echo "Copying connector to Kafka Connect pod..."
          kubectl cp /usr/share/java/debezium-connector-postgres connect-0:/usr/share/java/ -n confluent
          
          # Restart Kafka Connect to load the new connector
          echo "Restarting Kafka Connect..."
          kubectl rollout restart statefulset/connect -n confluent
          
          echo "Debezium connector installation complete!"
      restartPolicy: OnFailure
