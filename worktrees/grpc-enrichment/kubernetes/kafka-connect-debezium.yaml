apiVersion: apps/v1
kind: Deployment
metadata:
  name: kafka-connect-debezium
  namespace: confluent
spec:
  replicas: 1
  selector:
    matchLabels:
      app: kafka-connect-debezium
  template:
    metadata:
      labels:
        app: kafka-connect-debezium
    spec:
      containers:
      - name: kafka-connect
        image: quay.io/debezium/connect:2.5.0
        ports:
        - containerPort: 8083
        env:
        - name: BOOTSTRAP_SERVERS
          value: kafka.confluent.svc.cluster.local:9092
        - name: GROUP_ID
          value: debezium-connect-cluster
        - name: CONFIG_STORAGE_TOPIC
          value: debezium-connect-configs
        - name: OFFSET_STORAGE_TOPIC
          value: debezium-connect-offsets
        - name: STATUS_STORAGE_TOPIC
          value: debezium-connect-status
        - name: KEY_CONVERTER
          value: org.apache.kafka.connect.json.JsonConverter
        - name: VALUE_CONVERTER
          value: org.apache.kafka.connect.json.JsonConverter
        - name: CONNECT_KEY_CONVERTER_SCHEMAS_ENABLE
          value: "false"
        - name: CONNECT_VALUE_CONVERTER_SCHEMAS_ENABLE
          value: "false"
---
apiVersion: v1
kind: Service
metadata:
  name: kafka-connect-debezium
  namespace: confluent
spec:
  selector:
    app: kafka-connect-debezium
  ports:
  - port: 8083
    targetPort: 8083
