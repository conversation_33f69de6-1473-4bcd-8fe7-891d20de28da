# Kafka CDC with <PERSON>be<PERSON>um and Flink Architecture

## Overview

This document describes the architecture of the Change Data Capture (CDC) pipeline using <PERSON><PERSON><PERSON><PERSON> for PostgreSQL and Apache Flink for stream processing. The solution enables real-time data synchronization from a PostgreSQL database to Kafka topics and subsequent processing with Flink.

## Architecture Components

### 1. Infrastructure

The entire solution is deployed on an Amazon EKS (Elastic Kubernetes Service) cluster with the following specifications:

- **Cluster Name**: small-eks-cluster
- **Region**: us-west-2
- **Kubernetes Version**: 1.27
- **Node Group**: t3.medium instances (2-4 nodes)
- **VPC CIDR**: 10.0.0.0/16
- **Essential Add-ons**: vpc-cni, coredns, kube-proxy, aws-ebs-csi-driver

The EKS cluster is organized into multiple namespaces:
- `confluent`: Contains Kafka, Zookeeper, Kafka Connect, and PostgreSQL
- `flink`: Contains Flink JobManager, TaskManager, and deployed Flink jobs

### 2. PostgreSQL Database

PostgreSQL serves as the source database for CDC:

- **Image**: postgres:13
- **Namespace**: confluent
- **Service Name**: postgres.confluent.svc.cluster.local
- **Port**: 5432
- **Credentials**: postgres/postgres
- **CDC Configuration**:
  - `wal_level=logical`: Required for logical replication
  - `max_wal_senders=10`: Maximum number of concurrent connections for WAL streaming
  - `max_replication_slots=10`: Maximum number of replication slots
  - **Publication**: dbz_publication (for all tables)
  - **Tables**: public.customers, public.orders

### 3. Confluent Platform

The Confluent Platform provides the Kafka infrastructure:

- **Deployment Method**: Confluent for Kubernetes (CFK)
- **Components**:
  - **Zookeeper**:
    - Replicas: 1 (dev), 3 (prod)
    - Storage: 10Gi (dev), 100Gi+ (prod)
  - **Kafka**:
    - Replicas: 1 (dev), 3 (prod)
    - Storage: 10Gi (dev), 100Gi+ (prod)
    - Resources: 2Gi memory, 1000m CPU (requests)
  - **Schema Registry**:
    - Replicas: 1
  - **Kafka Connect**:
    - Replicas: 1 (dev), 2 (prod)
    - Resources: 1Gi memory, 500m CPU (requests)
    - Image: Custom image with Debezium connector
  - **Control Center**:
    - Enabled: true
    - Storage: 10Gi (dev), 100Gi (prod)

### 4. Debezium CDC Connector

The Debezium connector captures database changes:

- **Connector Class**: io.debezium.connector.postgresql.PostgresConnector
- **Version**: 1.9.6
- **Configuration**:
  - Database hostname: postgres.confluent.svc.cluster.local
  - Database port: 5432
  - Database user: postgres
  - Database password: postgres
  - Database name: postgres
  - Plugin name: pgoutput
  - Slot name: debezium
  - Publication name: dbz_publication
  - Topic prefix: postgres
  - Topic creation: Enabled with replication factor 1 (dev), 3 (prod)
  - Transforms: unwrap (ExtractNewRecordState)

### 5. Apache Flink

Flink processes the CDC events for order enrichment:

- **Namespace**: flink
- **Deployment**: Flink Kubernetes Operator
- **Job Name**: order-enrichment
- **Image**: 739368017290.dkr.ecr.us-west-2.amazonaws.com/flink-jobs:latest
- **Components**:
  - **JobManager**:
    - Replicas: 1
    - Resources: 1Gi memory, 500m CPU
  - **TaskManager**:
    - Replicas: 2
    - Resources: 2Gi memory, 1000m CPU
    - Slots: 2
- **Configuration**:
  - State Backend: Filesystem
  - Processing Semantics: EXACTLY_ONCE
  - Checkpoint Interval: 60000ms
  - Parallelism: 2

### 6. Flink Order Enrichment Job

The Flink job processes order data:

- **Source**: Kafka topic (postgres.public.orders)
- **Process**:
  - Async customer lookup from PostgreSQL
  - Enrich orders with customer details
- **Sink**: Kafka topic (postgres.public.enriched-orders)
- **Features**:
  - Async I/O for database lookups
  - Exactly-once processing guarantees
  - Checkpointing for fault tolerance
  - Watermarking for handling late events

## Data Flow

1. **Change Capture**:
   - Changes to PostgreSQL tables (inserts, updates, deletes) are captured by the PostgreSQL WAL (Write-Ahead Log)
   - The pgoutput plugin streams these changes to the Debezium connector

2. **Event Publication**:
   - Debezium connector transforms the WAL events into Kafka Connect records
   - Records are published to Kafka topics with the format: `postgres.public.<table_name>`
   - The ExtractNewRecordState transform converts CDC events to regular events

3. **Stream Processing**:
   - Flink job consumes events from the `postgres.public.orders` topic
   - For each order event, an async lookup to the customers table is performed
   - Order data is enriched with customer information
   - Enriched orders are published to the `postgres.public.enriched-orders` topic

4. **Downstream Consumption**:
   - Applications can consume from the enriched orders topic
   - Control Center provides monitoring and management of the entire pipeline

## Deployment Process

The deployment follows these steps:

1. **Infrastructure Setup**:
   - EKS cluster creation using eksctl
   - Namespace creation and resource quotas

2. **Confluent Platform Deployment**:
   - Deploy Confluent for Kubernetes operator
   - Deploy Zookeeper, Kafka, Schema Registry, and Control Center

3. **PostgreSQL Deployment**:
   - Deploy PostgreSQL with CDC configuration
   - Initialize database with sample data
   - Create publication for CDC

4. **Kafka Connect with Debezium**:
   - Build custom Kafka Connect image with Debezium
   - Push image to ECR
   - Deploy Kafka Connect using Confluent for Kubernetes

5. **Debezium Connector Deployment**:
   - Configure and deploy Debezium PostgreSQL connector
   - Verify connector status and topic creation

6. **Flink Deployment**:
   - Deploy Flink Kubernetes Operator
   - Deploy Flink cluster (JobManager and TaskManagers)

7. **Flink Job Deployment**:
   - Build and push Flink job image
   - Deploy order enrichment job

## Monitoring and Management

- **Confluent Control Center**: Monitoring Kafka, Connect, and topics
- **Flink Dashboard**: Monitoring Flink jobs and performance
- **Kubernetes Dashboard**: Overall cluster health and resource usage

## Security Considerations

- **Network Policies**: Restrict communication between namespaces
- **Resource Quotas**: Prevent resource exhaustion
- **Credentials Management**: Currently using basic authentication for development
  - For production: Consider using Kubernetes Secrets or AWS Secrets Manager

## Scaling and High Availability

- **Development Environment**:
  - Single replicas for components
  - Minimal resource allocation

- **Production Environment**:
  - Multiple replicas for Kafka, Zookeeper, and Connect
  - Higher resource allocation
  - Increased replication factors for Kafka topics
  - Pod anti-affinity rules for high availability

## Future Enhancements

1. **Security Improvements**:
   - Implement TLS for all communications
   - Integrate with AWS IAM for authentication
   - Implement fine-grained access control

2. **Monitoring Enhancements**:
   - Set up Prometheus and Grafana for comprehensive monitoring
   - Implement alerting for critical components

3. **Operational Improvements**:
   - Automated backup and restore procedures
   - Disaster recovery planning
   - Blue/green deployment strategy

4. **Performance Optimizations**:
   - Tune Kafka and Flink for higher throughput
   - Implement partitioning strategies for better parallelism
   - Optimize PostgreSQL for CDC performance
