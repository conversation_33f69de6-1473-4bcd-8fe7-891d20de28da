flowchart TD
    %% EKS Cluster
    subgraph EKS["EKS Cluster (small-eks-cluster)"]
        %% Confluent Namespace
        subgraph CONF["Namespace: confluent"]
            %% Confluent Platform Components
            subgraph CP["Confluent Platform"]
                ZK["Zookeeper\nStatefulSet\nReplicas: 1"]
                KAFKA["Kafka\nStatefulSet\nReplicas: 1\nPorts: 9092"]
                SR["Schema Registry\nDeployment\nPorts: 8081"]
                CONNECT["Kafka Connect\nStatefulSet\nReplicas: 1\nPorts: 8083"]
                CC["Control Center\nDeployment\nPorts: 9021"]
            end
            
            %% PostgreSQL
            subgraph PGSQL["PostgreSQL"]
                PG["PostgreSQL\nDeployment\nReplicas: 1\nPorts: 5432"]
                PG_CONF["CDC Configuration\nwal_level=logical\ndbz_publication"]
            end
            
            %% Debezium Connector
            subgraph DEB["Debezium"]
                DEBEZIUM["Debezium Connector\nPostgreSQL Plugin\nSlot: debezium"]
                CONNECTOR_JOB["Connector Deployment Job\nConfigures connector"]
            end
        end
        
        %% Flink Namespace
        subgraph FLINK["Namespace: flink"]
            %% Flink Components
            subgraph FLINK_COMP["Flink Components"]
                FO["Flink Operator\nDeployment\nReplicas: 1"]
                FJM["Flink JobManager\nDeployment\nReplicas: 1\nPorts: 8081"]
                FTM["Flink TaskManager\nDeployment\nReplicas: 2\nSlots: 2"]
            end
            
            %% Flink Job
            subgraph FLINK_JOB["Flink Job"]
                FJOB["Order Enrichment Job\nFlinkDeployment\nParallelism: 2"]
            end
        end
    end
    
    %% External Components
    ECR["AWS ECR\nContainer Registry"]
    DEV["Developer\nWorkstation"]
    
    %% Connections and Data Flow
    ZK <--> KAFKA
    KAFKA <--> SR
    KAFKA <--> CONNECT
    CONNECT <--> DEBEZIUM
    KAFKA <--> CC
    SR <--> CC
    CONNECT <--> CC
    
    PG -- "WAL Events" --> DEBEZIUM
    PG_CONF -- "Configures" --> PG
    
    CONNECTOR_JOB -- "Deploys" --> DEBEZIUM
    DEBEZIUM -- "CDC Events" --> CONNECT
    CONNECT -- "Publishes to\npostgres.public.orders" --> KAFKA
    
    FO -- "Manages" --> FJM
    FO -- "Manages" --> FTM
    FO -- "Deploys" --> FJOB
    FJM -- "Coordinates" --> FTM
    
    KAFKA -- "Consumes from\npostgres.public.orders" --> FJOB
    FJOB -- "Async Lookup" --> PG
    FJOB -- "Publishes to\npostgres.public.enriched-orders" --> KAFKA
    
    ECR -- "Provides Images" --> CONNECT
    ECR -- "Provides Images" --> FJOB
    DEV -- "Deploys" --> EKS
    DEV -- "Pushes Images" --> ECR
    
    %% Styling
    classDef eks fill:#326ce5,stroke:#fff,stroke-width:1px,color:#fff
    classDef namespace fill:#326ce5,stroke:#fff,stroke-width:1px,color:#fff,opacity:0.7
    classDef platform fill:#ff9900,stroke:#fff,stroke-width:1px,color:#fff
    classDef postgres fill:#336791,stroke:#fff,stroke-width:1px,color:#fff
    classDef debezium fill:#DB2D20,stroke:#fff,stroke-width:1px,color:#fff
    classDef flink fill:#E4A11B,stroke:#fff,stroke-width:1px,color:#fff
    classDef flinkjob fill:#E4A11B,stroke:#fff,stroke-width:1px,color:#fff,opacity:0.8
    classDef external fill:#909090,stroke:#fff,stroke-width:1px,color:#fff
    
    class EKS eks
    class CONF,FLINK namespace
    class CP platform
    class PGSQL postgres
    class DEB debezium
    class FLINK_COMP flink
    class FLINK_JOB flinkjob
    class ECR,DEV external
