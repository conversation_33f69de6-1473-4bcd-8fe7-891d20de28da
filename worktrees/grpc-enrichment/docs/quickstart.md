# Quick Start Guide

This guide will help you set up the Postgres CDC pipeline using Kafka Connect.

## Prerequisites
- Access to EKS cluster
- Docker installed
- AWS CLI configured
- kubectl configured for your cluster

## Setup Steps

1. Build and push the Kafka Connect image:
```bash
./scripts/build-images.sh
```

2. Deploy to development environment:
```bash
./scripts/deploy.sh dev
```

3. Access Control Center UI:
```bash
kubectl port-forward controlcenter-0 9021:9021 -n confluent
```
Then open http://localhost:9021 in your browser.

## Testing the Pipeline

1. Create a test table in Postgres:
```bash
kubectl exec -it postgres-0 -n confluent -- psql -U postgres -c "
CREATE TABLE orders (
  id SERIAL PRIMARY KEY,
  order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  customer_name TEXT,
  amount DECIMAL
);"
```

2. Insert test data:
```bash
kubectl exec -it postgres-0 -n confluent -- psql -U postgres -c "
INSERT INTO orders (customer_name, amount) 
VALUES ('<PERSON>', 100.50), ('<PERSON>', 200.75);"
```

3. Verify in Control Center:
- Navigate to Topics
- Look for `postgres.public.orders`
- View messages in the topic

## Directory Structure

- `infrastructure/`: Kubernetes manifests
  - `base/`: Base configurations
  - `overlays/`: Environment-specific configs
- `connectors/`: Connector configurations
- `docker/`: Docker image definitions
- `scripts/`: Utility scripts

## Common Tasks

1. Update connector configuration:
```bash
# Edit config
vim connectors/postgres-cdc/config/connector.json

# Apply changes
kubectl exec -i connect-pg-debezium-0 -n confluent -- \
  curl -X PUT -H "Content-Type: application/json" \
  --data @- http://localhost:8083/connectors/postgres-source/config \
  < connectors/postgres-cdc/config/connector.json
```

2. View connector status:
```bash
kubectl exec -it connect-pg-debezium-0 -n confluent -- \
  curl -s http://localhost:8083/connectors/postgres-source/status | jq
```

3. View Connect logs:
```bash
kubectl logs -f connect-pg-debezium-0 -n confluent
```
