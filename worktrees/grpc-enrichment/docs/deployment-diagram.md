# Kafka CDC with <PERSON><PERSON><PERSON><PERSON> and Flink Deployment Diagram

```mermaid
graph TD
    %% Cluster and Namespaces
    EKS[EKS Cluster\nsmall-eks-cluster] --> CONF[Namespace: confluent]
    EKS --> FLINK[Namespace: flink]
    
    %% Confluent Platform Components
    CONF --> <PERSON><PERSON>[Zookeeper\nStatefulSet]
    CONF --> KAFKA[Kafka\nStatefulSet]
    CONF --> SR[Schema Registry\nStatefulSet]
    CONF --> CONNECT[Kafka Connect\nStatefulSet]
    CONF --> CC[Control Center\nStatefulSet]
    CONF --> PG[PostgreSQL\nDeployment]
    
    %% Flink Components
    FLINK --> FO[Flink Operator\nDeployment]
    FLINK --> FJM[Flink JobManager\nDeployment]
    FLINK --> FTM[Flink TaskManager\nDeployment]
    FLINK --> FJOB[Order Enrichment Job\nFlinkDeployment]
    
    %% Configurations and Dependencies
    PG -. "wal_level=logical\ndbz_publication" .-> PG_CONF[PostgreSQL CDC Config]
    CONNECT -. "Debezium Connector\nPostgreSQL Plugin" .-> DEBEZIUM[Debezium Connector]
    
    %% Data Flow
    PG -- "CDC Events\n(WAL)" --> DEBEZIUM
    DEBEZIUM -- "CDC Events" --> CONNECT
    CONNECT -- "Publish to\npostgres.public.orders" --> KAFKA
    KAFKA -- "Consume from\npostgres.public.orders" --> FJOB
    FJOB -- "Async Lookup" --> PG
    FJOB -- "Publish to\npostgres.public.enriched-orders" --> KAFKA
    
    %% Services
    PG -. "Service:\npostgres:5432" .-> PG_SVC[PostgreSQL Service]
    KAFKA -. "Service:\nkafka:9092" .-> KAFKA_SVC[Kafka Service]
    CONNECT -. "Service:\nconnect:8083" .-> CONNECT_SVC[Connect Service]
    CC -. "Service:\ncontrolcenter:9021" .-> CC_SVC[Control Center Service]
    
    %% Styling
    classDef eks fill:#326ce5,stroke:#fff,stroke-width:1px,color:#fff
    classDef namespace fill:#326ce5,stroke:#fff,stroke-width:1px,color:#fff,opacity:0.7
    classDef statefulset fill:#ff9900,stroke:#fff,stroke-width:1px,color:#fff
    classDef deployment fill:#326ce5,stroke:#fff,stroke-width:1px,color:#fff
    classDef config fill:#f9f,stroke:#333,stroke-width:1px
    classDef service fill:#fff,stroke:#326ce5,stroke-width:1px,color:#326ce5
    classDef dataflow fill:none,stroke:#333,stroke-width:2px,stroke-dasharray: 5 5
    
    class EKS eks
    class CONF,FLINK namespace
    class ZK,KAFKA,SR,CONNECT,CC statefulset
    class PG,FO,FJM,FTM,FJOB deployment
    class PG_CONF,DEBEZIUM config
    class PG_SVC,KAFKA_SVC,CONNECT_SVC,CC_SVC service
```

## Detailed Component Specifications

### EKS Cluster
- **Region**: us-west-2
- **Version**: 1.27
- **Node Type**: t3.medium
- **Node Count**: 2-4
- **VPC CIDR**: 10.0.0.0/16

### Confluent Namespace Components

#### Zookeeper
- **Image**: confluentinc/cp-zookeeper:7.9.0
- **Replicas**: 1 (dev), 3 (prod)
- **Storage**: 10Gi PVC (dev), 100Gi+ PVC (prod)
- **Ports**: 2181, 2888, 3888

#### Kafka
- **Image**: confluentinc/cp-server:7.9.0
- **Replicas**: 1 (dev), 3 (prod)
- **Storage**: 10Gi PVC (dev), 100Gi+ PVC (prod)
- **Ports**: 9092 (internal), 9071 (external)
- **Resources**:
  - Requests: 2Gi memory, 1000m CPU
  - Limits: 4Gi memory, 2000m CPU

#### Schema Registry
- **Image**: confluentinc/cp-schema-registry:7.9.0
- **Replicas**: 1
- **Ports**: 8081
- **Dependencies**: Kafka

#### Kafka Connect
- **Image**: Custom (739368017290.dkr.ecr.us-west-2.amazonaws.com/kafka-connect-debezium:latest)
- **Replicas**: 1 (dev), 2 (prod)
- **Ports**: 8083
- **Resources**:
  - Requests: 1Gi memory, 500m CPU
  - Limits: 2Gi memory, 1000m CPU
- **Plugins**:
  - Debezium PostgreSQL Connector (1.9.6)
  - Kafka Connect JDBC (10.7.4)
  - Kafka Connect S3 (10.5.8)
  - Kafka Connect Elasticsearch (14.0.8)

#### Control Center
- **Image**: confluentinc/cp-enterprise-control-center:7.9.0
- **Replicas**: 1
- **Storage**: 10Gi PVC (dev), 100Gi PVC (prod)
- **Ports**: 9021
- **Dependencies**: Kafka, Connect

#### PostgreSQL
- **Image**: postgres:13
- **Replicas**: 1
- **Storage**: EmptyDir (dev), PVC (prod)
- **Ports**: 5432
- **Environment Variables**:
  - POSTGRES_USER: postgres
  - POSTGRES_PASSWORD: postgres
- **Configuration**:
  - wal_level=logical
  - max_wal_senders=10
  - max_replication_slots=10

### Flink Namespace Components

#### Flink Operator
- **Image**: apache/flink-kubernetes-operator:1.6.0
- **Replicas**: 1
- **Ports**: 8081
- **CRDs**: FlinkDeployment, FlinkSessionJob

#### Flink JobManager
- **Image**: apache/flink:1.16.0
- **Replicas**: 1
- **Ports**: 8081, 6123
- **Resources**:
  - Requests: 1Gi memory, 500m CPU
  - Limits: 2Gi memory, 1000m CPU

#### Flink TaskManager
- **Image**: apache/flink:1.16.0
- **Replicas**: 2
- **Ports**: 6122
- **Resources**:
  - Requests: 2Gi memory, 1000m CPU
  - Limits: 4Gi memory, 2000m CPU
- **Slots**: 2

#### Order Enrichment Job
- **Image**: 739368017290.dkr.ecr.us-west-2.amazonaws.com/flink-jobs:latest
- **Job Type**: FlinkDeployment
- **Parallelism**: 2
- **State Backend**: Filesystem
- **Checkpointing**: 60s interval
- **Restart Strategy**: Fixed delay (5 attempts, 10s delay)

## Network Flow

1. **CDC Flow**:
   - PostgreSQL WAL changes → Debezium Connector → Kafka Connect → Kafka Topics

2. **Processing Flow**:
   - Kafka Topics → Flink Job → Enriched Kafka Topics

3. **Management Flow**:
   - External Access → Control Center → Monitoring of all components
   - External Access → Flink Dashboard → Monitoring of Flink jobs

## Resource Requirements

| Component | CPU Request | Memory Request | Storage |
|-----------|------------|---------------|---------|
| Zookeeper | 500m | 1Gi | 10Gi |
| Kafka | 1000m | 2Gi | 10Gi |
| Schema Registry | 500m | 1Gi | - |
| Kafka Connect | 500m | 1Gi | - |
| Control Center | 1000m | 2Gi | 10Gi |
| PostgreSQL | 500m | 1Gi | 10Gi |
| Flink JobManager | 500m | 1Gi | - |
| Flink TaskManager | 1000m | 2Gi | - |
| **Total** | **5.5 cores** | **11Gi** | **40Gi** |

## Deployment Sequence

1. EKS Cluster → Namespaces
2. Confluent Namespace → Zookeeper → Kafka → Schema Registry → Control Center
3. Confluent Namespace → PostgreSQL (with CDC config)
4. Confluent Namespace → Kafka Connect → Debezium Connector
5. Flink Namespace → Flink Operator → JobManager/TaskManager → Order Enrichment Job
