# Confluent Platform Deployment Issues

## Namespace Termination Issues

### Problem
When attempting to delete the `confluent` namespace, it got stuck in "Terminating" state due to finalizers attached to Confluent Platform resources.

### Root Cause
Confluent Platform custom resources have finalizers (e.g., `kafka.finalizers.platform.confluent.io`) that prevent the namespace from being fully deleted until all resources are properly cleaned up.

### Resolution
Created and executed scripts to:
1. Remove finalizers from individual Confluent Platform resources
2. Force delete the namespace by removing the kubernetes finalizer

## Connect Pod Failures

### Problem
The `connect-0` pod repeatedly crashes with `CrashLoopBackOff` status, unable to start properly.

### Root Cause
1. Kafka Connect is trying to create internal topics (`confluent.connect-offsets`, `confluent.connect-configs`, `confluent.connect-status`) with a replication factor of 3
2. Only 1 Kafka broker is configured in the deployment, but the default replication factor is 3
3. Error: `InvalidReplicationFactorException: Unable to replicate the partition 3 time(s): The target replication factor of 3 cannot be reached because only 1 broker(s) are registered`

### Resolution
Two possible solutions:
1. Increase the number of Kafka brokers to 3 (recommended for production)
2. Reduce the default replication factor for internal topics to 1 (suitable for development)

## Control Center Issues

### Problem
The `controlcenter-0` pod is in `CrashLoopBackOff` state, unable to start properly.

### Root Cause
Similar to Connect, Control Center is likely trying to create internal topics with a replication factor higher than the available number of brokers.

### Resolution
Same as for Connect - either increase broker count or reduce replication factor.

## Producer Application Failures

### Problem
The `elastic-0` pod (Kafka producer) initially failed with connection errors.

### Root Cause
The producer was trying to connect to Kafka before the Kafka broker was fully ready.

### Resolution
The issue resolved itself once the Kafka broker became available.

## Confluent Operator Missing

### Problem
Initially, Confluent Platform custom resources were not being properly managed.

### Root Cause
The Confluent for Kubernetes (CFK) operator was not installed, which is required to manage Confluent Platform custom resources.

### Resolution
Installed the CFK operator using Helm:
```bash
helm upgrade --install confluent-operator confluentinc/confluent-for-kubernetes -n confluent
```

## Connect Resource Issues

### Problem
Even with correct replication factor settings, the `connect-0` pod was still failing to initialize properly, taking excessive time to load plugins and repeatedly restarting.

### Root Cause
Insufficient CPU and memory resources allocated to the Connect pod:
- Original allocation: 100m CPU, 256Mi memory
- Connect needs significant resources to load its many plugins during initialization
- The pod was timing out during readiness checks because plugin loading was taking too long

### Resolution
Increased the resource allocation for the Connect pod:
1. CPU requests: 100m → 500m (5x increase)
2. Memory requests: 256Mi → 1Gi (4x increase)
3. Added resource limits (1 CPU, 2Gi memory)

This provided sufficient resources for Connect to initialize properly without timing out.

## General Deployment Considerations

### Resource Requirements
- Confluent Platform components are resource-intensive
- Ensure adequate CPU and memory are available in the Kubernetes cluster
- Kafka Connect requires at least 500m CPU and 1Gi memory for reliable operation

### Startup Sequence
- Components start in a specific order based on dependencies
- KRaftController → Kafka → Schema Registry → Connect → KSQL → Control Center

### Production Recommendations
1. Use at least 3 Kafka brokers for fault tolerance
2. Configure appropriate resource limits and requests
3. Use persistent storage for all stateful components
4. Implement proper security measures (authentication, authorization, encryption)
