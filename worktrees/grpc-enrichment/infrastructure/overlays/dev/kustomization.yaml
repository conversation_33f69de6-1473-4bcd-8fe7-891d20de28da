apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: confluent

bases:
- ../../base/platform
- ../../base/kafka
- ../../base/postgres

commonLabels:
  env: dev

patches:
- target:
    kind: Connect
    name: connect-pg-debezium
  patch: |-
    - op: replace
      path: /spec/replicas
      value: 1
    - op: replace
      path: /spec/image/application
      value: 739368017290.dkr.ecr.us-west-2.amazonaws.com/kafka-connect-debezium:latest

- target:
    kind: Kafka
    name: kafka
  patch: |-
    - op: replace
      path: /spec/replicas
      value: 1
    - op: replace
      path: /spec/dataVolumeCapacity
      value: 10Gi

- target:
    kind: Zookeeper
    name: zookeeper
  patch: |-
    - op: replace
      path: /spec/replicas
      value: 1
    - op: replace
      path: /spec/dataVolumeCapacity
      value: 10Gi
