version: '1.0'

stages:
#  - deploy_dev
#  - deploy_prd_approval
#  - deploy_prd

steps:  # TODO: Set up your Lambda func deployment steps and destination environments
#  deploy_dev:
#    stage: deploy_dev
#    title: Deploy to DEV
#    type: codefresh-run
#    arguments:
#      PIPELINE_ID: '${{CF_REPO_NAME}}/task-deploy-artifact'
#      TRIGGER_ID: 'deploy'
#      BRANCH: '${{CF_BRANCH}}'
#      SHA: '${{CF_REVISION}}'
#      VARIABLE:
#        - TAG=${{TAG}}
#        - ACCT='pii-dev'

#  deploy_prd_approval:
#    title: Should we proceed with the prod deploy?
#    stage: deploy_prd
#    type: pending-approval
#    description: Manually approve release to production.
#    timeout:
#      duration: 8
#      finalState: denied

#  deploy_prd:
#    stage: deploy_prd
#    title: Deploy to PRD
#    type: codefresh-run
#    arguments:
#      PIPELINE_ID: '${{CF_REPO_NAME}}/task-deploy-artifact'
#      TRIGGER_ID: 'deploy'
#      BRANCH: '${{CF_BRANCH}}'
#      SHA: '${{CF_REVISION}}'
#      VARIABLE:
#        - TAG=${{TAG}}
#        - ACCT='pii-prd'
