version: "1.0"

stages:
  - "clone"
  - "build"
  - "archive"

steps:
  clone:
    title: "Cloning repository"
    stage: "clone"
    type: "git-clone"
    repo: "GoodRx/codefresh-lambda-template"  # TODO: Replace with your Lambda func repository
    revision: "${{CF_BRANCH}}"
    git: "goodrx"

  package_dependencies:
    title: "Package Dependencies"
    stage: "build"
    image: "python:3.9-buster"
    working_directory: "${{clone}}/src/handlers"
    shell: bash
    commands:
      - "apt update && apt install -y zip"
      - "pip install -r ./codefresh-lambda-template/requirements.txt -t ./python/lib/python3.9/site-packages"  # TODO: Replace with your requirements.txt path
      - "zip -r ${{CF_VOLUME_PATH}}/${{TAG}}_deps.zip ./python/"
      - "rm -rf ./python"

  package_source:
    title: "Package Source"
    stage: "build"
    image: "python:3.9-buster"
    working_directory: "${{clone}}/src/handlers/codefresh-lambda-template"  # TODO: Replace with your path to Lambda func directory
    shell: bash
    commands:
      - "apt update && apt install -y zip"
      - "zip -r ${{CF_VOLUME_PATH}}/${{TAG}}_src.zip ."
      - "rm -rf ./python"

  archive:
    stage: archive
    title: 'Archive artifact'
    type: 'freestyle'
    image: 'amazon/aws-cli'
    environment:
      - AWS_ACCESS_KEY_ID=${{ENGDEV_S3A_BA_RW_AWS_ACCESS_KEY_ID}}
      - AWS_SECRET_ACCESS_KEY=${{ENGDEV_S3A_BA_RW_AWS_SECRET_ACCESS_KEY}}
    commands:
      - aws s3 cp ${{CF_VOLUME_PATH}}/${{TAG}}_deps.zip s3://s3a-build-artifacts.grxdev.com/pkgs/${{CF_REPO_NAME}}/${{TAG}}_deps.zip
      - aws s3 cp ${{CF_VOLUME_PATH}}/${{TAG}}_src.zip s3://s3a-build-artifacts.grxdev.com/pkgs/${{CF_REPO_NAME}}/${{TAG}}_src.zip
      - rm -f ${{CF_VOLUME_PATH}}/${{TAG}}_deps.zip ${{CF_VOLUME_PATH}}/${{TAG}}_src.zip
