version: '1.0'

stages:
  - setup
  - deploy

steps:
  main_clone:
    title: Cloning repository
    stage: setup
    type: git-clone
    repo: '${{CF_REPO_OWNER}}/${{CF_REPO_NAME}}'
    revision: '${{CF_REVISION}}'
    git: goodrx
  infra_clone:
    title: Cloning gdrx-infrastructure
    stage: setup
    type: git-clone
    repo: '${{CF_REPO_OWNER}}/gdrx-infrastructure'
    revision: 'main'
    git: goodrx

  prepare:
    title: Fetch artifact
    stage: setup
    type: freestyle
    image: bitnami/aws-cli:latest
    working_directory: "${{infra_clone}}"
    environment:
      - AWS_ACCESS_KEY_ID=${{ENGDEV_S3A_BA_RW_AWS_ACCESS_KEY_ID}}
      - AWS_SECRET_ACCESS_KEY=${{ENGDEV_S3A_BA_RW_AWS_SECRET_ACCESS_KEY}}
    entry_point: bash
    commands:
      - cd apps/codefresh-lambda-template/app/${{ACCT}}  # TODO: Replace with the location path to your gdrx-infrastructure "app" directory
      - aws s3 cp s3://s3a-build-artifacts.grxdev.com/pkgs/${{CF_REPO_NAME}}/${{TAG}}_deps.zip ${{TAG}}_deps.zip
      - aws s3 cp s3://s3a-build-artifacts.grxdev.com/pkgs/${{CF_REPO_NAME}}/${{TAG}}_src.zip ${{TAG}}_src.zip

  deploy_app:
    title: "Run Terraform Apply"
    image: hashicorp/terraform:1.1.0
    working_directory: "${{infra_clone}}"
    stage: "deploy"
    commands:
      - pwd
      - cd apps/codefresh-lambda-template/app/${{ACCT}}  # TODO: Replace with the location path to your gdrx-infrastructure "app" directory
      - mkdir -p ~/.terraform.d/
      - printf %s "${TFC_TOKEN}" > ~/.terraform.d/credentials.tfrc.json
      - echo "dependency_package_zip=\"${{TAG}}_deps.zip\"" >> deploy.auto.tfvars
      - echo "source_code_zip=\"${{TAG}}_src.zip\"" >> deploy.auto.tfvars
      - cat deploy.auto.tfvars
      - terraform init -reconfigure
      - terraform apply -auto-approve # apply with -target if more than one resource in the same workspace
