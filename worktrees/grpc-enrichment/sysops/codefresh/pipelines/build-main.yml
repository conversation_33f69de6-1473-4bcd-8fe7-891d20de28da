version: '1.0'

stages:
  - setup
  - build
  - release

steps:
  prepare:
    stage: setup
    title: 'Prepare variables'
    type: 'freestyle'
    image: 'node:14'
    commands:
      - cf_export TAG=${{CF_REPO_NAME}}-${{CF_BRANCH}}-${{CF_SHORT_REVISION}}
  main_clone:
    stage: setup
    title: 'Cloning repository'
    type: 'git-clone'
    repo: '${{CF_REPO_OWNER}}/${{CF_REPO_NAME}}'
    revision: '${{CF_REVISION}}'
    git: 'goodrx'

  build:
    stage: build
    title: Build Artifact
    type: codefresh-run
    arguments:
      PIPELINE_ID: '${{CF_REPO_NAME}}/task-build-artifact'
      TRIGGER_ID: 'build'
      BRANCH: '${{CF_BRANCH}}'
      SHA: '${{CF_REVISION}}'
      TIMEOUT_MINS: '120'
      VARIABLE:
        - TAG='${{TAG}}'

  release:
    stage: release
    title: Trigger Release
    type: codefresh-run
    arguments:
      PIPELINE_ID: '${{CF_REPO_NAME}}/release'
      TRIGGER_ID: 'release'
      BRANCH: '${{CF_BRANCH}}'
      SHA: '${{CF_REVISION}}'
      TIMEOUT_MINS: '120'
      DETACH: true
      VARIABLE:
        - TAG='${{TAG}}'
