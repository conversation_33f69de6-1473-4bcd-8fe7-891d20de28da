#!/bin/bash
set -e

# Default environment
ENV=${1:-dev}

# Add Confluent Helm repository
helm repo add confluentinc https://packages.confluent.io/helm
helm repo update

# Install the chart
helm upgrade --install kafka-cdc ./helm/kafka-cdc \
  --namespace confluent \
  --create-namespace \
  --values ./helm/kafka-cdc/values.yaml \
  --values ./helm/kafka-cdc/values/${ENV}.yaml \
  --wait

echo "Installation complete!"
echo "Access Control Center at: http://localhost:9021 (after running kubectl port-forward controlcenter-0 9021:9021 -n confluent)"
