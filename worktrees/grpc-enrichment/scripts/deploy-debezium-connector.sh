#!/bin/bash
set -e

# Source environment variables
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${SCRIPT_DIR}/env.sh"

echo "=== Deploying Kafka Connect with Debezium Connector ==="

# Verify Kubernetes connection
echo "Verifying Kubernetes connection..."
kubectl get namespace confluent || {
  echo "Error: Cannot connect to Kubernetes cluster"
  echo "Please run 'gimme-aws-creds' and update kubeconfig first"
  exit 1
}

# Update the Kafka Connect deployment manifest with actual values
echo "Preparing Kafka Connect deployment manifest..."
sed -e "s/\${AWS_ACCOUNT}/${AWS_ACCOUNT}/g" \
    -e "s/\${AWS_REGION}/${AWS_REGION}/g" \
    "${SCRIPT_DIR}/../kubernetes/kafka-connect-update.yaml" > "${SCRIPT_DIR}/../kubernetes/kafka-connect-update-filled.yaml"

# Apply the updated Kafka Connect configuration
echo "Updating Kafka Connect with Debezium image..."
kubectl apply -f "${SCRIPT_DIR}/../kubernetes/kafka-connect-update-filled.yaml"

# Wait for Kafka Connect to be ready
echo "Waiting for Kafka Connect to be ready..."
kubectl rollout status statefulset/connect -n confluent --timeout=300s

# Deploy the Debezium connector
echo "Deploying Debezium connector for PostgreSQL..."
kubectl apply -f "${SCRIPT_DIR}/../connectors/postgres-cdc/config/connector-deployment.yaml"

# Wait for the connector deployment job to complete
echo "Waiting for connector deployment job to complete..."
kubectl wait --for=condition=complete job/deploy-postgres-cdc-connector -n confluent --timeout=300s

# Verify connector status
echo "Verifying connector status..."
kubectl exec -it connect-0 -n confluent -- curl -s http://localhost:8083/connectors/postgres-source/status

echo "=== Debezium connector deployment completed ==="
echo "You can monitor CDC events using:"
echo "kubectl exec -it connect-0 -n confluent -- kafka-console-consumer --bootstrap-server kafka:9092 --topic postgres.public.customers --from-beginning"
echo "kubectl exec -it connect-0 -n confluent -- kafka-console-consumer --bootstrap-server kafka:9092 --topic postgres.public.orders --from-beginning"
