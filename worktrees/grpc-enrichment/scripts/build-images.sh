#!/bin/bash
set -e

# Source environment variables
if [ -f "$(dirname "$0")/env.sh" ]; then
    source "$(dirname "$0")/env.sh"
else
    echo "Error: env.sh file not found"
    exit 1
fi

# Build Kafka Connect image
echo "Building Kafka Connect image..."
docker build -t kafka-connect-debezium:latest -f docker/kafka-connect/Dockerfile docker/kafka-connect

# Tag and push to ECR
echo "Tagging and pushing to ECR..."
docker tag kafka-connect-debezium:latest $AWS_ACCOUNT.dkr.ecr.$AWS_REGION.amazonaws.com/kafka-connect-debezium:latest
aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT.dkr.ecr.$AWS_REGION.amazonaws.com
docker push $AWS_ACCOUNT.dkr.ecr.$AWS_REGION.amazonaws.com/kafka-connect-debezium:latest

echo "Build and push complete!"
