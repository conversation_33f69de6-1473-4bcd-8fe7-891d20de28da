#!/bin/bash

# Resources to clean up
RESOURCES=(
  "connects.platform.confluent.io/connect-pg-debezium"
  "controlcenters.platform.confluent.io/controlcenter"
  "kafkas.platform.confluent.io/kafka"
  "kafkatopics.platform.confluent.io/elastic-0"
  "ksqldbs.platform.confluent.io/ksqldb"
  "schemaregistries.platform.confluent.io/schemaregistry"
  "zookeepers.platform.confluent.io/zookeeper"
)

# Loop through resources and remove finalizers
for RESOURCE in "${RESOURCES[@]}"; do
  echo "Processing $RESOURCE..."
  
  # Get the resource in JSON format
  kubectl get -n confluent $RESOURCE -o json > tmp.json
  
  # Remove finalizers using jq
  cat tmp.json | jq '.metadata.finalizers = null' > tmp2.json
  
  # Apply the changes
  kubectl replace -f tmp2.json
  
  echo "Finalizers removed from $RESOURCE"
done

# Clean up temporary files
rm -f tmp.json tmp2.json

echo "All finalizers removed. The namespace should now be able to delete."
