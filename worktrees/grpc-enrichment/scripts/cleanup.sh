#!/bin/bash
set -e

# Default to dev environment
ENV=${1:-dev}

echo "Cleaning up $ENV environment..."

# Delete connector first to avoid data loss
kubectl exec -i connect-pg-debezium-0 -n confluent -- \
  curl -X DELETE http://localhost:8083/connectors/postgres-source || true

# Delete all resources
kubectl delete -k infrastructure/overlays/$ENV || true
kubectl delete -k infrastructure/base/operators || true

# Uninstall CFK operator
helm uninstall confluent-operator -n confluent || true

# Clean up namespace if needed
read -p "Do you want to delete the confluent namespace? (y/n) " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]
then
    kubectl delete namespace confluent
fi

echo "Cleanup complete!"
