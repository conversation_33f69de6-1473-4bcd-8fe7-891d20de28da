#!/bin/bash
set -e

# Default to dev environment
ENV=${1:-dev}

# Install CFK Operator
echo "Installing Confluent for Kubernetes operator..."
helm repo add confluentinc https://packages.confluent.io/helm
helm repo update
helm upgrade --install confluent-operator confluentinc/confluent-for-kubernetes -n confluent --create-namespace

# Wait for CFK operator to be ready
echo "Waiting for CFK operator to be ready..."
kubectl -n confluent wait --for=condition=ready pod -l app=confluent-operator --timeout=300s

# Apply Kustomize configuration
echo "Deploying to $ENV environment..."
kubectl apply -k infrastructure/base/operators
kubectl apply -k infrastructure/overlays/$ENV

# Wait for core platform to be ready
echo "Waiting for Confluent Platform to be ready..."
kubectl -n confluent wait --for=condition=ready pod -l app=zookeeper --timeout=300s
kubectl -n confluent wait --for=condition=ready pod -l app=kafka --timeout=300s

# Wait for components to be ready
echo "Waiting for components to be ready..."
kubectl -n confluent wait --for=condition=ready pod -l app=connect-pg-debezium --timeout=300s
kubectl -n confluent wait --for=condition=ready pod -l app=controlcenter --timeout=300s

# Apply connector configuration
echo "Applying connector configuration..."
kubectl exec -i connect-pg-debezium-0 -n confluent -- \
  curl -X POST -H "Content-Type: application/json" \
  --data @- http://localhost:8083/connectors \
  < connectors/postgres-cdc/config/connector.json

echo "Deployment complete!"
echo "Access Control Center at: http://localhost:9021 (after running kubectl port-forward controlcenter-0 9021:9021 -n confluent)"
