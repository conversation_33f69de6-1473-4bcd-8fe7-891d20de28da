#!/bin/bash
set -e

echo "=== Installing Debezium PostgreSQL Connector in Kafka Connect ==="

# Create a temporary installation script
cat > /tmp/install-debezium.sh << 'EOF'
#!/bin/bash
set -e

echo "Installing Debezium PostgreSQL connector..."
mkdir -p /tmp/debezium
cd /tmp/debezium

# Download Debezium PostgreSQL connector
echo "Downloading connector..."
curl -LO https://repo1.maven.org/maven2/io/debezium/debezium-connector-postgres/2.5.0.Final/debezium-connector-postgres-2.5.0.Final-plugin.tar.gz

# Extract the connector
echo "Extracting connector..."
mkdir -p debezium-connector-postgres
tar -xzf debezium-connector-postgres-2.5.0.Final-plugin.tar.gz -C debezium-connector-postgres

# Create plugin directory if it doesn't exist
echo "Creating plugin directory..."
mkdir -p /usr/share/java/debezium-connector-postgres

# Copy connector files
echo "Copying connector files..."
cp -r debezium-connector-postgres/* /usr/share/java/debezium-connector-postgres/

# List installed files
echo "Installed connector files:"
ls -la /usr/share/java/debezium-connector-postgres/

echo "Debezium PostgreSQL connector installation complete!"
EOF

# Copy the installation script to the Kafka Connect pod
echo "Copying installation script to Kafka Connect pod..."
kubectl cp /tmp/install-debezium.sh connect-0:/tmp/install-debezium.sh -n confluent

# Make the script executable
echo "Making script executable..."
kubectl exec connect-0 -n confluent -- chmod +x /tmp/install-debezium.sh

# Run the installation script
echo "Running installation script in Kafka Connect pod..."
kubectl exec connect-0 -n confluent -- /tmp/install-debezium.sh

# Restart Kafka Connect to load the new connector
echo "Restarting Kafka Connect to load the new connector..."
kubectl rollout restart statefulset/connect -n confluent

# Wait for Kafka Connect to restart
echo "Waiting for Kafka Connect to restart..."
kubectl rollout status statefulset/connect -n confluent --timeout=300s

# Verify connector installation
echo "Verifying connector installation..."
kubectl exec connect-0 -n confluent -- curl -s http://localhost:8083/connector-plugins | grep -i debezium

echo "=== Debezium PostgreSQL connector installation complete ==="
