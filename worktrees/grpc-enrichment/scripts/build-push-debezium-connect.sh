#!/bin/bash
# Build and push Debezium Connect Docker image with multi-architecture support
# Usage: ./scripts/build-push-debezium-connect.sh [platform1,platform2,...]
# Example: ./scripts/build-push-debezium-connect.sh linux/amd64,linux/arm64

set -euo pipefail

# Default values
DEFAULT_PLATFORMS="linux/amd64,linux/arm64"
DEFAULT_AWS_PROFILE="eng-sbx-/engineering"
DEFAULT_AWS_REGION="us-west-2"
DEFAULT_ECR_REPO="kafka-connect-debezium"
DEFAULT_IMAGE_TAG="latest"

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Parse command line arguments
PLATFORMS="${1:-$DEFAULT_PLATFORMS}"
AWS_PROFILE="${AWS_PROFILE:-$DEFAULT_AWS_PROFILE}"
AWS_REGION="${AWS_REGION:-$DEFAULT_AWS_REGION}"
ECR_REPO="${ECR_REPO:-$DEFAULT_ECR_REPO}"
IMAGE_TAG="${IMAGE_TAG:-$DEFAULT_IMAGE_TAG}"
ECR_URI="${AWS_ACCOUNT:-************}.dkr.ecr.${AWS_REGION}.amazonaws.com/${ECR_REPO}"

# Validate required tools are installed
command -v docker >/dev/null 2>&1 || { echo "Error: docker is not installed"; exit 1; }
command -v aws >/dev/null 2>&1 || { echo "Error: aws-cli is not installed"; exit 1; }
command -v jq >/dev/null 2>&1 || { echo "Error: jq is not installed"; exit 1; }

echo "=== Building and Pushing Debezium Connect Image ==="
echo "Platforms: ${PLATFORMS}"
echo "AWS Profile: ${AWS_PROFILE}"
echo "AWS Region: ${AWS_REGION}"
echo "ECR Repository: ${ECR_REPO}"
echo "Image Tag: ${IMAGE_TAG}"
echo "ECR URI: ${ECR_URI}"

# Verify AWS credentials
echo -e "\n[1/5] Verifying AWS credentials..."
if ! aws sts get-caller-identity --profile "${AWS_PROFILE}" >/dev/null 2>&1; then
  echo "Error: Failed to verify AWS credentials for profile ${AWS_PROFILE}"
  exit 1
fi

# Create ECR repository if it doesn't exist
echo -e "\n[2/5] Ensuring ECR repository exists..."
if ! aws ecr describe-repositories --repository-names "${ECR_REPO}" --profile "${AWS_PROFILE}" --region "${AWS_REGION}" >/dev/null 2>&1; then
  echo "Creating ECR repository: ${ECR_REPO}"
  aws ecr create-repository --repository-name "${ECR_REPO}" --profile "${AWS_PROFILE}" --region "${AWS_REGION}"
  echo "ECR repository created successfully"
else
  echo "ECR repository already exists"
fi

# Login to ECR
echo -e "\n[3/5] Logging in to ECR..."
aws ecr get-login-password --region "${AWS_REGION}" --profile "${AWS_PROFILE}" | \
  docker login --username AWS --password-stdin "${AWS_ACCOUNT:-************}.dkr.ecr.${AWS_REGION}.amazonaws.com"

# Build and push multi-architecture image
echo -e "\n[4/5] Building and pushing multi-architecture image..."
# Enable Docker CLI experimental features
export DOCKER_CLI_EXPERIMENTAL=enabled

# Create a new builder instance if it doesn't exist
if ! docker buildx ls | grep -q multiarch; then
  echo "Creating new builder instance..."
  docker buildx create --name multiarch --use
else
  echo "Using existing builder instance..."
  docker buildx use multiarch
fi

# Build and push the image
docker buildx build \
  --platform "${PLATFORMS}" \
  -t "${ECR_URI}:${IMAGE_TAG}" \
  -f "${SCRIPT_DIR}/../kubernetes/debezium-connect-dockerfile" \
  --push \
  "${SCRIPT_DIR}/.."

echo -e "\n[5/5] Verifying pushed image..."
echo "Image pushed successfully to: ${ECR_URI}:${IMAGE_TAG}"

# List the available architectures for the pushed image
echo -e "\nAvailable architectures for ${ECR_URI}:${IMAGE_TAG}:"
aws ecr describe-images \
  --repository-name "${ECR_REPO}" \
  --image-ids "imageTag=${IMAGE_TAG}" \
  --profile "${AWS_PROFILE}" \
  --region "${AWS_REGION}" \
  --query 'imageDetails[].imageManifest' \
  --output text | jq -r '.manifests[].platform.architecture // empty' | sort | uniq | paste -sd, -

echo -e "\n=== Build and Push Completed Successfully ==="
