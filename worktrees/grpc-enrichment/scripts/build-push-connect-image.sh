#!/bin/bash
set -e

# Source environment variables
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${SCRIPT_DIR}/env.sh"

echo "=== Building and Pushing Kafka Connect with Debezium Image ==="
echo "AWS Account: ${AWS_ACCOUNT}"
echo "AWS Region: ${AWS_REGION}"
echo "ECR Repository: ${ECR_REPO}"

# Verify AWS credentials
echo "Verifying AWS credentials..."
aws sts get-caller-identity --profile eng-sbx-/engineering

# Create ECR repository if it doesn't exist
echo "Ensuring ECR repository exists..."
aws ecr describe-repositories --repository-names ${ECR_REPO} --profile eng-sbx-/engineering || \
  aws ecr create-repository --repository-name ${ECR_REPO} --profile eng-sbx-/engineering

# Build Kafka Connect image with Debezium
echo "Building Kafka Connect image with <PERSON><PERSON><PERSON><PERSON>..."
docker build -t ${ECR_REPO}:latest -f "${SCRIPT_DIR}/../docker/kafka-connect/Dockerfile" "${SCRIPT_DIR}/../docker/kafka-connect"

# Tag image
echo "Tagging image..."
docker tag ${ECR_REPO}:latest ${AWS_ACCOUNT}.dkr.ecr.${AWS_REGION}.amazonaws.com/${ECR_REPO}:latest

# Login to ECR
echo "Logging in to ECR..."
aws ecr get-login-password --region ${AWS_REGION} --profile eng-sbx-/engineering | docker login --username AWS --password-stdin ${AWS_ACCOUNT}.dkr.ecr.${AWS_REGION}.amazonaws.com

# Push image to ECR
echo "Pushing image to ECR..."
docker push ${AWS_ACCOUNT}.dkr.ecr.${AWS_REGION}.amazonaws.com/${ECR_REPO}:latest

echo "=== Image build and push completed successfully ==="
echo "Image: ${AWS_ACCOUNT}.dkr.ecr.${AWS_REGION}.amazonaws.com/${ECR_REPO}:latest"
