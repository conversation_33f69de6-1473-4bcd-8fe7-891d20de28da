#!/bin/bash
set -e

# Default environment
ENV=${1:-dev}

# Add Flink Helm repository
helm repo add apache https://downloads.apache.org/flink/flink-kubernetes-operator-1.7.0/
helm repo update

# Install the chart
helm upgrade --install flink-platform ./helm/flink-platform \
  --namespace flink \
  --create-namespace \
  --values ./helm/flink-platform/values.yaml \
  --values ./helm/flink-platform/values/${ENV}.yaml \
  --wait

echo "Installation complete!"
echo "Access Flink UI: kubectl port-forward svc/flink-jobmanager-rest 8081:8081 -n flink"
