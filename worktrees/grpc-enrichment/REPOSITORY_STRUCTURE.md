# Repository Structure

```
arch-kafka-eda-sandbox/
├── infrastructure/
│   ├── base/                    # Base Kubernetes manifests
│   │   ├── kafka/              # Kafka infrastructure
│   │   │   ├── kustomization.yaml
│   │   │   ├── kafka.yaml
│   │   │   └── zookeeper.yaml
│   │   ├── postgres/           # PostgreSQL infrastructure
│   │   │   ├── kustomization.yaml
│   │   │   └── postgres.yaml
│   │   └── monitoring/         # Monitoring stack
│   │       ├── kustomization.yaml
│   │       └── controlcenter.yaml
│   │
│   ├── overlays/               # Environment-specific overlays
│   │   ├── dev/
│   │   │   └── kustomization.yaml
│   │   └── prod/
│   │       └── kustomization.yaml
│   │
│   └── secrets/                # Secret management
│       └── sealed-secrets.yaml
│
├── connectors/                 # Kafka Connect connectors
│   ├── postgres-cdc/          # PostgreSQL CDC connector
│   │   ├── Dockerfile         # Custom connector image
│   │   ├── config/           
│   │   │   └── connector.json  # Connector configuration
│   │   └── README.md          # Connector documentation
│   └── README.md
│
├── docker/                     # Docker image definitions
│   └── kafka-connect/
│       ├── Dockerfile
│       └── config/
│           └── connect-distributed.properties
│
├── scripts/                    # Utility scripts
│   ├── build-images.sh
│   ├── deploy.sh
│   └── cleanup.sh
│
├── examples/                   # Example applications
│   └── postgres-events/
│       ├── README.md
│       └── schema.sql
│
└── docs/                      # Documentation
    ├── architecture.md
    ├── deployment.md
    └── monitoring.md
```

## Key Changes

1. **Infrastructure Separation**
   - All Kubernetes manifests moved to `infrastructure/`
   - Uses Kustomize for environment management
   - Clear separation between base and overlay configurations

2. **Connector Organization**
   - Dedicated `connectors/` directory for all connectors
   - Each connector has its own documentation and configuration
   - Easier to add new connectors

3. **Docker Image Management**
   - Centralized `docker/` directory for all custom images
   - Consistent build and configuration approach

4. **Documentation**
   - Dedicated `docs/` directory
   - Separated by concern (architecture, deployment, etc.)

5. **Scripts**
   - Centralized utility scripts
   - Consistent naming and documentation

## Migration Steps

1. Create new directory structure:
```bash
mkdir -p infrastructure/{base,overlays}/{kafka,postgres,monitoring}/{dev,prod}
mkdir -p connectors/postgres-cdc/{config,docs}
mkdir -p docker/kafka-connect/config
mkdir -p scripts docs examples/postgres-events
```

2. Move existing files:
```bash
# Move Kubernetes manifests
mv src/postgres/connect-combined.yaml infrastructure/base/kafka/
mv src/postgres/postgres-deployment.yaml infrastructure/base/postgres/
mv src/postgres/controlcenter.yaml infrastructure/base/monitoring/

# Move connector configuration
mv src/postgres/postgres-source-connector.json connectors/postgres-cdc/config/

# Move Docker files
mv src/postgres/Dockerfile.connect docker/kafka-connect/
mv src/postgres/connect-distributed.properties docker/kafka-connect/config/

# Move scripts
mv src/postgres/push-connect-image.sh scripts/build-images.sh
mv remove-finalizers.sh scripts/cleanup.sh
```

3. Update documentation:
```bash
mv src/postgres/README.md docs/postgres-cdc.md
```

## Benefits

1. **GitOps Ready**
   - Clear separation of infrastructure and application code
   - Easy to implement GitOps workflows
   - Environment-specific configurations isolated

2. **Maintainability**
   - Clear organization by component type
   - Easier to find and update configurations
   - Consistent structure across components

3. **Scalability**
   - Easy to add new connectors
   - Simple to add new environments
   - Clear path for adding new components

4. **Documentation**
   - Centralized and organized documentation
   - Each component self-documented
   - Clear examples and guides

Would you like me to help implement this reorganization?
